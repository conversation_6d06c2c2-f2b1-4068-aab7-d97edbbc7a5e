# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file and the fastmcp SDK
COPY requirements.txt .
COPY fastmcp ./fastmcp_sdk/

# Install fastmcp from the local SDK directory
# This will also install its dependencies (mcp, anyio, httpx, rich, websockets, etc.)
RUN pip install --no-cache-dir ./fastmcp_sdk/

# Install other direct dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the main application file
COPY main.py .
# The .env file will be sourced by the docker run command, not copied here.

# Make port 6005 available to the world outside this container
EXPOSE 6005

# Define environment variables (can be overridden at runtime)
# These are placeholders; actual values should be injected during docker run or via docker-compose
ENV SENDGRID_API_KEY=""
ENV SENDGRID_SENDER_EMAIL=""
ENV SENDGRID_REPLYTO_ADDRESS=""
ENV SENDGRID_SENDER_FROM_NAME="AgentiveAIQ Assistant"
ENV PYTHONUNBUFFERED=1

# Run main.py when the container launches
CMD ["python", "main.py"]