import os
from typing import Dict, Any
from pydantic import BaseModel, <PERSON>
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, From, To, Subject, Content, ReplyTo

from fastmcp import FastMCP


# Pydantic model for tool arguments
class SendEmailArgs(BaseModel):
    email_address: str = Field(..., description="The recipient's email address.")
    subject: str = Field(..., description="The subject line of the email.")
    body_content: str = Field(..., description="The plain text content of the email body.")

# Initialize the MCP with enhanced description
mcp = FastMCP(
    name="Assistant_Email_MCP",
    instructions="""
# Assistant Email MCP - v1.0

## TOOL DESCRIPTION: 
This MCP allows AI agents to send emails programmatically using the SendGrid API. It is designed to send text-based emails with a specified recipient, subject, and body content. It ensures proper formatting for readability.

## QUICK START:
Send an email by providing the recipient's email address, subject, and body content.
EXAMPLE: {"email_address": "<EMAIL>", "subject": "Hello from <PERSON>", "body_content": "This is a test email sent by an AI agent."}

## AVAILABLE TOOLS:
- send_email: Sends a plain text email to a specified recipient.

## COMMON WORKFLOWS:
1. Notification Task: Agent determines need to notify user → Agent gathers email details (address, subject, message) → Agent uses send_email tool.

## LIMITATIONS:
- Only sends plain text emails. HTML content is not supported.
- Attachments are not supported in this version.
- Relies on SendGrid API availability and correct API key configuration.
- Sender email, reply-to address, and sender name are pre-configured via environment variables.

## TROUBLESHOOTING:
SYMPTOM: Emails are not being sent.
CAUSE: Incorrect SendGrid API key, SendGrid account issues, or invalid recipient email format.
SOLUTION: Verify the SENDGRID_API_KEY environment variable. Check the SendGrid dashboard for any account alerts or sending issues. Ensure the recipient email address is valid.

SYMPTOM: 'FastMCP' module not found.
CAUSE: The FastMCP SDK is not correctly placed or its path is not added to sys.path.
SOLUTION: Ensure the 'fastmcp' directory is located at 'mcps/assistant_email_mcp/fastmcp' and the sys.path.append line is correct.
    """
)

@mcp.tool()
async def send_email(args: SendEmailArgs) -> Dict[str, Any]:
    """
    ### send_email:
    TOOL USE: Sends a plain text email to a specified recipient using SendGrid. The email will be sent from a pre-configured sender address.

    INPUTS:
      - REQUIRED:
        - email_address: String - The email address of the recipient.
        - subject: String - The subject line for the email.
        - body_content: String - The plain text content for the email body.
      
      - OPTIONAL:
        - None

    DEFAULT VALUES:
      - None

    VARIABLE OPTIONS:
      - None

    PARAMETER RELATIONSHIPS:
      - All three parameters (email_address, subject, body_content) are required to send an email.

    OUTPUT: Returns a JSON object containing:
      - success: Boolean - True if the email was sent successfully (or at least accepted by SendGrid), False otherwise.
      - message: String - A message indicating the outcome of the send request (e.g., "Email sent successfully." or an error message).
      - status_code: Integer - The HTTP status code returned by the SendGrid API (e.g., 202 for accepted).

    ERROR HANDLING:
      - If SENDGRID_API_KEY is not set: Returns {"success": false, "message": "SendGrid API key not configured.", "status_code": null}
      - If SENDGRID_SENDER_EMAIL is not set: Returns {"success": false, "message": "Sender email not configured.", "status_code": null}
      - If SendGrid API returns an error: Returns {"success": false, "message": "Failed to send email: [SendGrid error details]", "status_code": [HTTP status code]}
      - On other exceptions: Returns {"success": false, "message": "An unexpected error occurred: [exception details]", "status_code": null}

    EXAMPLE USAGE:
    ```json
    {
      "email_address": "<EMAIL>",
      "subject": "Important Update",
      "body_content": "Hello Test User,\\n\\nThis is an important update regarding your account.\\n\\nThanks,\\nThe Team"
    }
    ```

    RELATED TOOLS:
      - None in this MCP.

    USER HINTS:
      - IDENTIFYING NEED: Use when an agent needs to send a notification, confirmation, or any textual information to a user via email.
      - GATHERING INFO: Ensure the agent has the correct recipient email address, a clear subject, and the complete body content for the email.
      - PRESENTING RESULTS: Inform the user whether the email was successfully dispatched. If there's an error, provide the error message.

    CONVERSATION EXAMPLES:
    User: "Please send an <NAME_EMAIL> with the subject 'Project Alpha Update' and the body 'The project is on track for completion next week.'"
    Agent: "Understood. I will send an <NAME_EMAIL> with the subject 'Project Alpha Update' and the body 'The project is on track for completion next week.'."
    (Agent then uses this tool with the provided details)
    """
    api_key = os.environ.get("SENDGRID_API_KEY")
    sender_email_address = os.environ.get("SENDGRID_SENDER_EMAIL")
    reply_to_address = os.environ.get("SENDGRID_REPLYTO_ADDRESS")
    sender_from_name = os.environ.get("SENDGRID_SENDER_FROM_NAME")

    if not api_key:
        return {"success": False, "message": "SendGrid API key not configured.", "status_code": None}
    if not sender_email_address:
        return {"success": False, "message": "Sender email not configured.", "status_code": None}

    message = Mail()
    message.from_email = From(email=sender_email_address, name=sender_from_name)
    message.to = To(args.email_address)
    message.subject = Subject(args.subject)
    message.content = Content("text/plain", args.body_content)
    if reply_to_address:
        message.reply_to = ReplyTo(reply_to_address, sender_from_name if sender_from_name else "No Reply")


    try:
        sg = SendGridAPIClient(api_key)
        response = sg.send(message)
        
        if 200 <= response.status_code < 300:
            return {"success": True, "message": "Email sent successfully.", "status_code": response.status_code}
        else:
            return {"success": False, "message": f"Failed to send email. Status: {response.status_code}. Body: {response.body}", "status_code": response.status_code}
    except Exception as e:
        return {"success": False, "message": f"An unexpected error occurred: {str(e)}", "status_code": None}

# Run the MCP server
if __name__ == "__main__":
    # Load environment variables from .env file if it exists
    # This is helpful for local development.
    # In a containerized environment, env vars are typically injected.
    if os.path.exists(".env"):
        print("Loading .env file...")
        with open(".env", "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    os.environ[key] = value
                    print(f"Loaded {key} from .env")
    
    # Verify necessary env vars are loaded before starting
    required_env_vars = ["SENDGRID_API_KEY", "SENDGRID_SENDER_EMAIL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    if missing_vars:
        print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please ensure they are set in the .env file or system environment.")
        sys.exit(1)
    else:
        print("All required environment variables are present.")

    print(f"Starting Assistant Email MCP on port 6005...")
    mcp.run(transport="sse", host="0.0.0.0", port=6005, log_level="info")