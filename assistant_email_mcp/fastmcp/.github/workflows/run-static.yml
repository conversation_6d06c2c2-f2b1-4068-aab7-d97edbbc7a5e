name: Run static analysis

env:
  # enable colored output
  # https://github.com/pytest-dev/pytest/issues/7443
  PY_COLORS: 1

on:
  push:
    branches: ["main"]
    paths:
      - "src/**"
      - "tests/**"
      - "uv.lock"
      - "pyproject.toml"
      - ".github/workflows/**"

  # run on all pull requests because these checks are required and will block merges otherwise
  pull_request:

  workflow_dispatch:

permissions:
  contents: read

jobs:
  static_analysis:
    timeout-minutes: 2

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - name: Install dependencies
        run: uv sync --dev
      - name: Run pre-commit
        uses: pre-commit/action@v3.0.1
