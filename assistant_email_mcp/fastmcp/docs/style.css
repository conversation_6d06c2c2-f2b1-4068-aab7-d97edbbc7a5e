/* Code highlighting -- target only inline code elements, not code blocks */
p code:not(pre code),
table code:not(pre code),
li code:not(pre code),
h1 code:not(pre code),
h2 code:not(pre code),
h3 code:not(pre code),
h4 code:not(pre code),
h5 code:not(pre code),
h6 code:not(pre code) {
  color: #f72585 !important;
  background-color: rgba(247, 37, 133, 0.09);
}

/* Version badge -- display a badge with the current version of the documentation */
.version-badge {
  display: inline-block;
  align-items: center;
  gap: 0.3em;
  padding: 0.2em 0.8em;
  font-size: 1.1em;
  font-weight: 400;

  font-family: "Inter", sans-serif;
  letter-spacing: 0.025em;
  color: #ff5400;
  background: #ffeee6;
  border: 1px solid rgb(255, 84, 0, 0.5);
  border-radius: 6px;
  box-shadow: none;
  vertical-align: middle;
  position: relative;
  transition: box-shadow 0.2s, transform 0.15s;
}

.version-badge-container {
  margin: 0;
  padding: 0;
}

.version-badge:hover {
  box-shadow: 0 2px 8px 0 rgba(160, 132, 252, 0.1);
  transform: translateY(-1px) scale(1.03);
}

.dark .version-badge {
  color: #fff;
  background: #312e81;
  border: 1.5px solid #a78bfa;
}
