---
title: Authentication
sidebarTitle: Authentication
description: Secure your FastMCP server with authentication.
icon: lock
---
import { VersionBadge } from '/snippets/version-badge.mdx'

<VersionBadge version="2.2.7" />

This document will cover how to implement authentication for your FastMCP servers.

FastMCP leverages the OAuth 2.0 support provided by the underlying Model Context Protocol (MCP) SDK.

For now, refer to the [MCP Server Authentication documentation](/servers/fastmcp#authentication) for initial details and the [official MCP SDK documentation](https://modelcontextprotocol.io/specification/2025-03-26/basic/authorization) for more.
