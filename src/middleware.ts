import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { auth } from "./auth"; // Import your existing auth configuration

export default auth((req) => {
  // This is the middleware function that Next.js expects
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;

  // Define protected routes
  const isProtectedRoute = nextUrl.pathname.startsWith('/dashboard') || 
                          nextUrl.pathname.startsWith('/profile') ||
                          nextUrl.pathname.startsWith('/admin');

  // Define auth routes (sign-in, sign-up, etc.)
  const isAuthRoute = nextUrl.pathname.startsWith('/sign-in') ||
                     nextUrl.pathname.startsWith('/sign-up');

  // Redirect logic
  if (isProtectedRoute && !isLoggedIn) {
    return NextResponse.redirect(new URL('/sign-in', nextUrl));
  }

  if (isAuthRoute && isLoggedIn) {
    return NextResponse.redirect(new URL('/dashboard', nextUrl));
  }

  return NextResponse.next();
});

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    // Match all routes except static files and API routes
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ]
};