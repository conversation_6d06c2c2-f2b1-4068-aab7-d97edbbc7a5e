import { auth } from "@/auth"; // Correct import for NextAuth.js v5
import { prisma } from "@/lib/db";

export async function getCurrentUser() {
  const session = await auth();

  if (!session?.user?.email) {
    return null;
  }

  const user = await prisma.user.findUnique({
    where: {
      email: session.user.email,
    },
    select: {
      id: true,
      name: true,
      email: true,
      image: true,
      role: true,
      tier: true,
      paymentProviderCustomerId: true,
    },
  });

  return user;
}