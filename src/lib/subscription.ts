import { SubscriptionTier, User } from "@prisma/client";

import { prisma } from "@/lib/db";

export type SubscriptionPlan = {
  name: string;
  description: string;
  stripePriceId: string | null;
  monthlyPrice: number | null;
  yearlyPrice: number | null;
  features: string[];
  isFree: boolean;
  isCurrent: boolean;
  interval: "month" | "year" | null;
  polarProductId: string | null;
  polarPriceId: string | null;
};

export async function getUserSubscriptionPlan(
  userId: User["id"],
): Promise<SubscriptionPlan> {
  const user = await prisma.user.findFirst({
    where: {
      id: userId,
    },
    select: {
      tier: true,
      paymentProviderSubscriptionId: true,
      paymentProviderPriceId: true,
      paymentProviderCurrentPeriodEnd: true,
    },
  });

  if (!user) {
    throw new Error("User not found");
  }

  const isFree = user.tier === SubscriptionTier.FREE;
  const isBase = user.tier === SubscriptionTier.BASE;
  const isPro = user.tier === SubscriptionTier.PRO;

  let plan: SubscriptionPlan = {
    name: "Free",
    description: "The free plan for basic usage.",
    stripePriceId: null,
    monthlyPrice: 0,
    yearlyPrice: 0,
    features: ["5 Chatbots", "1000 Messages/month"],
    isFree: true,
    isCurrent: isFree,
    interval: null,
    polarProductId: null,
    polarPriceId: null,
  };

  if (isBase) {
    plan = {
      name: "Base",
      description: "The base plan for growing businesses.",
      stripePriceId: null, // Replace with actual Stripe Price ID if using Stripe
      monthlyPrice: 10,
      yearlyPrice: 100,
      features: ["25 Chatbots", "10,000 Messages/month", "Customizable UI"],
      isFree: false,
      isCurrent: true,
      interval: user.paymentProviderPriceId?.includes("monthly") ? "month" : "year",
      polarProductId: null, // Replace with actual Polar Product ID
      polarPriceId: user.paymentProviderPriceId,
    };
  } else if (isPro) {
    plan = {
      name: "Pro",
      description: "The pro plan for large-scale operations.",
      stripePriceId: null, // Replace with actual Stripe Price ID if using Stripe
      monthlyPrice: 50,
      yearlyPrice: 500,
      features: ["Unlimited Chatbots", "Unlimited Messages", "Advanced Analytics", "Priority Support"],
      isFree: false,
      isCurrent: true,
      interval: user.paymentProviderPriceId?.includes("monthly") ? "month" : "year",
      polarProductId: null, // Replace with actual Polar Product ID
      polarPriceId: user.paymentProviderPriceId,
    };
  }

  return plan;
}