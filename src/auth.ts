import NextAuth from "next-auth";
import Credentials from "next-auth/providers/credentials";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { PrismaClient } from "@prisma/client";
import bcryptjs from "bcryptjs";
import authConfig from "./auth.config"; // This path is now correct as both will be in src/

const prisma = new PrismaClient();

export const { handlers, auth, signIn, signOut } = NextAuth({
  ...authConfig, // Spread the edge-safe config
  adapter: PrismaAdapter(prisma), // Add the adapter here
  // session strategy is already in authConfig
  providers: [ // Define or override providers here
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) {
          return null;
        }

        const email = credentials.email as string;
        const password = credentials.password as string;

        const user = await prisma.user.findUnique({
          where: { email: email },
        });

        if (!user || !user.password) {
          // It's good practice to log this for debugging, but don't expose details to the client
          console.log("Attempted login for non-existent user or user without password:", email);
          return null;
        }

        const isPasswordValid = await bcryptjs.compare(password, user.password);

        if (!isPasswordValid) {
          console.log("Invalid password attempt for user:", email);
          return null;
        }

        // Return user object without the password
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
          role: user.role, // Include other fields you might need in the session
          tier: user.tier,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // The user object is only passed on the first call after sign-in
      if (user && typeof user.id === 'string') {
        token.id = user.id;
        // Ensure role and tier are explicitly cast or handled if they can be undefined/null from user object
        token.role = user.role; 
        token.tier = user.tier; 
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string; 
        session.user.role = token.role; 
        session.user.tier = token.tier; 
      }
      return session;
    },
  },
  pages: {
    signIn: '/sign-in', // Redirect to our custom sign-in page
    // error: '/auth/error', // Error code passed in query string as ?error=
    // newUser: '/auth/new-user' // New users will be directed here on first sign in (leave the property out to disable)
  }
})
