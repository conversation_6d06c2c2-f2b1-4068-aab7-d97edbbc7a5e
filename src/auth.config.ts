import type { NextAuthConfig } from "next-auth";

export default {
  providers: [], // Required by NextAuthConfig, actual providers in auth.ts
  pages: {
    signIn: '/sign-in',
    // error: '/auth/error', // You can add an error page if you have one
  },
  session: { 
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  trustHost: true, // Important for production deployment behind proxy/load balancer
  debug: process.env.NODE_ENV === "development", // For verbose logging in development
} satisfies NextAuthConfig;