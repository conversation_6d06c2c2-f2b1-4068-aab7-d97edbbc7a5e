"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner'; // Assuming sonner is installed for notifications
import { Switch } from '@/components/ui/switch'; // Import Switch component
import { SwitchProps } from '@radix-ui/react-switch'; // Import SwitchProps for type safety

interface MCPConfiguration {
  id: number;
  name: string;
  url: string;
  is_active: boolean; // Add is_active field
  created_at: string;
  updated_at: string;
}

export default function AdminMCPsPage() {
  const [mcps, setMcps] = useState<MCPConfiguration[]>([]);
  const [newMcpName, setNewMcpName] = useState('');
  const [newMcpUrl, setNewMcpUrl] = useState('');
  const [loading, setLoading] = useState(false);

  // Use environment variable for API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_CENTRAL_API_URL || 'http://localhost:8008';

  const fetchMcps = async () => {
    setLoading(true);
    try {
      // Directly use the API_BASE_URL. If your backend is configured for HTTPS,
      // it should handle the protocol, or you should set API_BASE_URL to 'https://localhost:8008'
      const response = await fetch(`${API_BASE_URL}/api/admin/mcps`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setMcps(data);
      toast.success('MCPs loaded successfully.');
    } catch (error) {
      console.error('Failed to fetch MCPs:', error);
      toast.error(`Failed to load MCPs: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMcps();
  }, []);

  const handleCreateMcp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/mcps`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newMcpName, url: newMcpUrl, is_active: true }), // Default to active
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      toast.success('MCP created successfully!');
      setNewMcpName('');
      setNewMcpUrl('');
      fetchMcps(); // Refresh the list
    } catch (error) {
      console.error('Failed to create MCP:', error);
      toast.error(`Failed to create MCP: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async (mcpId: number) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/mcps/${mcpId}/test-connection`, {
        method: 'POST',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }
      toast.success('Connection successful!');
    } catch (error) {
      console.error('Failed to test connection:', error);
      toast.error(`Connection failed: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleListTools = async (mcpId: number, mcpName: string) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/mcps/${mcpId}/tools`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      console.log(`Tools for ${mcpName}:`, data.tools);
      toast.info(`Tools for ${mcpName} logged to console.`, {
        description: JSON.stringify(data.tools, null, 2),
        duration: 10000,
      });
    } catch (error) {
      console.error('Failed to list tools:', error);
      toast.error(`Failed to list tools: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMcp = async (mcpId: number, mcpName: string) => {
    if (!confirm(`Are you sure you want to delete MCP "${mcpName}"?`)) {
      return;
    }
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/mcps/${mcpId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }
      toast.success(`MCP "${mcpName}" deleted successfully!`);
      fetchMcps(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete MCP:', error);
      toast.error(`Failed to delete MCP: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (mcpId: number, currentStatus: boolean) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/mcps/${mcpId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: !currentStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      toast.success(`MCP status updated successfully!`);
      fetchMcps(); // Refresh the list
    } catch (error) {
      console.error('Failed to toggle MCP status:', error);
      toast.error(`Failed to toggle MCP status: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-8">
        MCP Management
      </h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Add New MCP</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleCreateMcp} className="space-y-4">
            <div>
              <Label htmlFor="mcpName">MCP Name</Label>
              <Input
                id="mcpName"
                value={newMcpName}
                onChange={(e) => setNewMcpName(e.target.value)}
                placeholder="e.g., Weather MCP"
                required
              />
            </div>
            <div>
              <Label htmlFor="mcpUrl">MCP URL</Label>
              <Input
                id="mcpUrl"
                type="url"
                value={newMcpUrl}
                onChange={(e) => setNewMcpUrl(e.target.value)}
                placeholder="e.g., http://localhost:8001"
                required
              />
            </div>
            <Button type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add MCP'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Existing MCPs</CardTitle>
        </CardHeader>
        <CardContent>
          {mcps.length === 0 && !loading ? (
            <p className="text-gray-600 dark:text-gray-400">No MCPs configured yet.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Active</TableHead> {/* New column for toggle */}
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mcps.map((mcp) => (
                  <TableRow key={mcp.id}>
                    <TableCell className="font-medium">{mcp.name}</TableCell>
                    <TableCell>{mcp.url}</TableCell>
                    <TableCell>{new Date(mcp.created_at).toLocaleString()}</TableCell>
                    <TableCell>
                      <Switch
                        checked={mcp.is_active}
                        onCheckedChange={() => handleToggleActive(mcp.id, mcp.is_active)}
                        disabled={loading}
                        aria-label={`Toggle ${mcp.name} active status`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestConnection(mcp.id)}
                          disabled={loading}
                        >
                          Test Connection
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleListTools(mcp.id, mcp.name)}
                          disabled={loading}
                        >
                          List Tools
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteMcp(mcp.id, mcp.name)}
                          disabled={loading}
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}