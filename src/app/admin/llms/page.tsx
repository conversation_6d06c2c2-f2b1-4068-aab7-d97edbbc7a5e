"use client";

import React, { useState, useEffect, FormEvent } from 'react';

// Define interfaces based on your backend schemas
interface LLMProvider {
  id: number;
  name: string;
  api_base_url: string | null;
}

interface LLMCredentialDisplay { // For displaying credentials, not for creation
  id: number;
  provider_id: number;
  provider_name?: string;
  is_active: boolean;
  last_tested_at: string | null;
  last_test_successful: boolean | null;
}

interface GlobalLLMSettings {
  embedding_llm_provider_id: number | null;
  embedding_model_name: string | null;
  chat_llm_provider_id: number | null;
  chat_model_name: string | null;
  assistant_llm_provider_id: number | null;
  assistant_model_name: string | null;
  embedding_llm_provider_name?: string; // For display
  chat_llm_provider_name?: string; // For display
  assistant_llm_provider_name?: string; // For display
}

// Adjust this if your API is running elsewhere or you have a configured API client
const API_URL = process.env.NEXT_PUBLIC_CENTRAL_API_URL || 'http://localhost:8008'; // From your central-api-service

// --- Basic UI Components (Replace with your actual UI library) ---
const Input = ({ label, value, onChange, type = "text", placeholder, ...props }: React.InputHTMLAttributes<HTMLInputElement> & { label: string }) => (
  <div style={{ marginBottom: '1rem' }}>
    <label style={{ display: 'block', marginBottom: '0.25rem', fontWeight: '500' }}>{label}</label>
    <input type={type} value={value} onChange={onChange} placeholder={placeholder} {...props} style={{ padding: '0.5rem', border: '1px solid #ccc', borderRadius: '4px', width: '100%' }} />
  </div>
);

const Button = ({ children, onClick, type = "button", disabled, variant = "primary", ...props }: React.ButtonHTMLAttributes<HTMLButtonElement> & { variant?: "primary" | "secondary" | "danger" }) => {
  const baseStyle: React.CSSProperties = { padding: '0.5rem 1rem', borderRadius: '4px', cursor: 'pointer', border: 'none', marginRight: '0.5rem', fontWeight: '500' };
  const variants: Record<string, React.CSSProperties> = {
    primary: { backgroundColor: '#007bff', color: 'white' },
    secondary: { backgroundColor: '#6c757d', color: 'white' },
    danger: { backgroundColor: '#dc3545', color: 'white' },
  };
  return <button type={type} onClick={onClick} disabled={disabled} style={{ ...baseStyle, ...variants[variant] }} {...props}>{children}</button>;
};

const Select = ({ label, value, onChange, children, ...props }: React.SelectHTMLAttributes<HTMLSelectElement> & { label: string }) => (
  <div style={{ marginBottom: '1rem' }}>
    <label style={{ display: 'block', marginBottom: '0.25rem', fontWeight: '500' }}>{label}</label>
    <select value={value} onChange={onChange} {...props} style={{ padding: '0.5rem', border: '1px solid #ccc', borderRadius: '4px', width: '100%' }}>
      {children}
    </select>
  </div>
);

const Card = ({ title, children }: { title: string, children: React.ReactNode }) => (
  <div style={{ border: '1px solid #eee', borderRadius: '8px', padding: '1rem', marginBottom: '1rem', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
    <h3 style={{ marginTop: 0, borderBottom: '1px solid #eee', paddingBottom: '0.5rem' }}>{title}</h3>
    {children}
  </div>
);
// --- End Basic UI Components ---

export default function LLMManagementPage() {
  const [providers, setProviders] = useState<LLMProvider[]>([]);
  const [credentialsMap, setCredentialsMap] = useState<Record<number, LLMCredentialDisplay[]>>({}); // provider.id -> its credentials
  const [globalSettings, setGlobalSettings] = useState<GlobalLLMSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form states
  const [apiKeyInputs, setApiKeyInputs] = useState<Record<number, string>>({}); // provider.id -> api_key
  const [ollamaBaseUrlInputs, setOllamaBaseUrlInputs] = useState<Record<number, string>>({}); // provider.id -> base_url (for Ollama)
  
  const [currentEmbeddingProviderId, setCurrentEmbeddingProviderId] = useState<string>('');
  const [currentEmbeddingModel, setCurrentEmbeddingModel] = useState<string>('');
  const [currentChatProviderId, setCurrentChatProviderId] = useState<string>('');
  const [currentChatModel, setCurrentChatModel] = useState<string>('');
  const [currentAssistantProviderId, setCurrentAssistantProviderId] = useState<string>('');
  const [currentAssistantModel, setCurrentAssistantModel] = useState<string>('');

  const [testResults, setTestResults] = useState<Record<number, { success: boolean; message: string }>>({}); // credential.id -> test result

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch Providers
        const providersRes = await fetch(`${API_URL}/api/admin/llm-management/providers`);
        if (!providersRes.ok) throw new Error(`Failed to fetch providers: ${providersRes.statusText}`);
        const providersData: LLMProvider[] = await providersRes.json();
        setProviders(providersData);

        // Fetch Credentials for each provider
        const credsMap: Record<number, LLMCredentialDisplay[]> = {};
        for (const provider of providersData) {
          try {
            const credsRes = await fetch(`${API_URL}/api/admin/llm-management/credentials/provider/${provider.id}`);
            if (credsRes.ok) {
              credsMap[provider.id] = await credsRes.json();
            } else {
              console.warn(`Failed to fetch credentials for provider ${provider.name}: ${credsRes.statusText}`);
              credsMap[provider.id] = [];
            }
          } catch (credError) {
            console.error(`Error fetching credentials for provider ${provider.name}:`, credError);
            credsMap[provider.id] = [];
          }
        }
        setCredentialsMap(credsMap);

        // Fetch Global Settings (using the new endpoint that returns all settings)
        const settingsRes = await fetch(`${API_URL}/api/admin/llms/settings/global`); // Updated endpoint
        if (!settingsRes.ok) {
          // Handle 404 specifically if settings might not exist yet
          if (settingsRes.status === 404) {
            console.warn("Global LLM settings not found (404). Initializing with empty/default values.");
            setGlobalSettings(null); // Or a default empty GlobalLLMSettings object
            // Initialize form fields to empty if no settings found
            setCurrentEmbeddingProviderId('');
            setCurrentEmbeddingModel('');
            setCurrentChatProviderId('');
            setCurrentChatModel('');
            setCurrentAssistantProviderId('');
            setCurrentAssistantModel('');
          } else {
            throw new Error(`Failed to fetch global settings: ${settingsRes.statusText}`);
          }
        } else {
          const settingsData: GlobalLLMSettings | null = await settingsRes.json();
          setGlobalSettings(settingsData);
          if (settingsData) {
            setCurrentEmbeddingProviderId(settingsData.embedding_llm_provider_id?.toString() || '');
            setCurrentEmbeddingModel(settingsData.embedding_model_name || '');
            setCurrentChatProviderId(settingsData.chat_llm_provider_id?.toString() || '');
            setCurrentChatModel(settingsData.chat_model_name || '');
            setCurrentAssistantProviderId(settingsData.assistant_llm_provider_id?.toString() || '');
            setCurrentAssistantModel(settingsData.assistant_model_name || '');
          } else {
            // Case where API returns 200 OK with null body (if Optional response model allows)
             setCurrentEmbeddingProviderId('');
            setCurrentEmbeddingModel('');
            setCurrentChatProviderId('');
            setCurrentChatModel('');
            setCurrentAssistantProviderId('');
            setCurrentAssistantModel('');
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error("Fetch data error:", err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleApiKeySave = async (providerId: number) => {
    const apiKey = apiKeyInputs[providerId];
    const providerName = providers.find(p => p.id === providerId)?.name;

    if (!apiKey) {
      alert('API Key cannot be empty.');
      return;
    }

    try {
      // 1. Deactivate all existing credentials for this provider
      const existingCredentials = credentialsMap[providerId] || [];
      for (const cred of existingCredentials) {
        if (cred.is_active) {
          const deactivateRes = await fetch(`${API_URL}/api/admin/llm-management/credentials/${cred.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ is_active: false }),
          });
          if (!deactivateRes.ok) {
            console.warn(`Failed to deactivate old credential ${cred.id}: ${deactivateRes.statusText}`);
            // Continue, but log warning. Don't block new key creation.
          }
        }
      }

      // 2. Create a new active credential
      const payload = { provider_id: providerId, api_key: apiKey, is_active: true };
      const response = await fetch(`${API_URL}/api/admin/llm-management/credentials`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to save API key: ${errorData.detail || response.statusText}`);
      }
      const savedCredential = await response.json();
      
      // Refresh credentials for this provider to reflect deactivation and new active key
      await fetchCredentialsForProvider(providerId);
      setApiKeyInputs(prev => ({ ...prev, [providerId]: '' })); // Clear input after successful save
      alert(`API Key for ${providerName} saved and activated successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save API key';
      setError(errorMessage);
      alert(errorMessage);
    }
  };

  const handleOllamaBaseUrlSave = async (providerId: number) => {
    const baseUrl = ollamaBaseUrlInputs[providerId];
    // Backend stores empty string as NULL, so we can pass it directly.
    // No real validation needed here other than it being a string.
    
    try {
      const response = await fetch(`${API_URL}/api/admin/llm-management/providers/${providerId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ api_base_url: baseUrl }), // Only sending api_base_url for update
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to save Ollama Base URL: ${errorData.detail || response.statusText}`);
      }
      const updatedProvider = await response.json();
      
      // Update the provider in the local state
      setProviders(prevProviders =>
        prevProviders.map(p => p.id === providerId ? { ...p, api_base_url: updatedProvider.api_base_url } : p)
      );
      alert(`Ollama Base URL saved successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save Ollama Base URL';
      setError(errorMessage);
      alert(errorMessage);
    }
  };

  const handleTestConnection = async (credentialId: number) => {
    setTestResults(prev => ({ ...prev, [credentialId]: { success: false, message: "Testing..."} }));
    try {
      const response = await fetch(`${API_URL}/api/admin/llm-management/credentials/${credentialId}/test`, {
        method: 'POST',
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || result.detail || `Test failed with status ${response.status}`);
      }
      setTestResults(prev => ({ ...prev, [credentialId]: { success: result.success, message: result.message } }));
      // Refresh credentials for this provider to show updated test status
      const providerId = providers.find(p => credentialsMap[p.id]?.some(c => c.id === credentialId))?.id;
      if (providerId) {
        fetchCredentialsForProvider(providerId);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection test failed';
      setTestResults(prev => ({ ...prev, [credentialId]: { success: false, message: errorMessage } }));
      alert(errorMessage);
    }
  };

  const handleDeleteCredential = async (credentialId: number, providerId: number) => {
    if (!confirm('Are you sure you want to delete this API key credential?')) return;

    try {
      const response = await fetch(`${API_URL}/api/admin/llm-management/credentials/${credentialId}`, {
        method: 'DELETE',
      });
      if (!response.ok) { // response.ok is true for 200-299. DELETE is 204.
        // For 204, response.ok will be true, but response.json() will fail.
        if (response.status !== 204) {
            const errorData = await response.json(); // This might fail for 204
            throw new Error(`Failed to delete credential: ${errorData.detail || response.statusText}`);
        }
      }
      // Refresh credentials for this provider
      setCredentialsMap(prev => ({
        ...prev,
        [providerId]: (prev[providerId] || []).filter(c => c.id !== credentialId),
      }));
      alert('Credential deleted successfully!');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete credential');
      alert(err instanceof Error ? err.message : 'Failed to delete credential');
    }
  };
  
  const fetchCredentialsForProvider = async (providerId: number) => {
    try {
      const credsRes = await fetch(`${API_URL}/api/admin/llm-management/credentials/provider/${providerId}`);
      if (credsRes.ok) {
        const newCreds = await credsRes.json();
        setCredentialsMap(prev => ({ ...prev, [providerId]: newCreds }));
      } else {
        console.warn(`Failed to re-fetch credentials for provider ${providerId}: ${credsRes.statusText}`);
      }
    } catch (credError) {
      console.error(`Error re-fetching credentials for provider ${providerId}:`, credError);
    }
  };


  const handleGlobalSettingsSave = async (e: FormEvent | React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setError(null); // Clear previous errors

    // Payload for chat and embedding settings (assuming this endpoint remains separate)
    const chatEmbeddingPayload = {
      embedding_llm_provider_id: currentEmbeddingProviderId ? parseInt(currentEmbeddingProviderId) : null,
      embedding_model_name: currentEmbeddingModel || null,
      chat_llm_provider_id: currentChatProviderId ? parseInt(currentChatProviderId) : null,
      chat_model_name: currentChatModel || null,
    };

    // Payload for assistant LLM settings
    const assistantPayload = {
      assistant_llm_provider_id: currentAssistantProviderId ? parseInt(currentAssistantProviderId) : null,
      assistant_model_name: currentAssistantModel || null,
    };

    try {
      // Save Chat & Embedding Settings (assuming existing endpoint)
      // If this endpoint is also meant to be the new one, this logic would merge.
      // For now, keeping it separate as per current structure.
      const chatEmbeddingResponse = await fetch(`${API_URL}/api/admin/llm-management/global-settings`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(chatEmbeddingPayload),
      });
      if (!chatEmbeddingResponse.ok) {
        const errorData = await chatEmbeddingResponse.json();
        throw new Error(`Failed to save chat/embedding settings: ${errorData.detail || chatEmbeddingResponse.statusText}`);
      }
      // const updatedChatEmbeddingSettings = await chatEmbeddingResponse.json();
      // console.log("Chat/Embedding settings saved:", updatedChatEmbeddingSettings);

      // Save Assistant LLM Settings (to the new endpoint)
      const assistantResponse = await fetch(`${API_URL}/api/admin/llms/settings/assistant`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assistantPayload),
      });
      if (!assistantResponse.ok) {
        const errorData = await assistantResponse.json();
        throw new Error(`Failed to save assistant LLM settings: ${errorData.detail || assistantResponse.statusText}`);
      }
      // const updatedAssistantSettings = await assistantResponse.json();
      // console.log("Assistant settings saved:", updatedAssistantSettings);
      
      // After all successful saves, re-fetch all global settings to update UI comprehensively
      const settingsRes = await fetch(`${API_URL}/api/admin/llms/settings/global`);
      if (!settingsRes.ok) {
          console.error("Failed to re-fetch global settings after save.");
          // Potentially set an error or rely on existing displayed data
      } else {
          const latestSettings = await settingsRes.json();
          setGlobalSettings(latestSettings);
          if (latestSettings) {
            setCurrentEmbeddingProviderId(latestSettings.embedding_llm_provider_id?.toString() || '');
            setCurrentEmbeddingModel(latestSettings.embedding_model_name || '');
            setCurrentChatProviderId(latestSettings.chat_llm_provider_id?.toString() || '');
            setCurrentChatModel(latestSettings.chat_model_name || '');
            setCurrentAssistantProviderId(latestSettings.assistant_llm_provider_id?.toString() || '');
            setCurrentAssistantModel(latestSettings.assistant_model_name || '');
          }
      }
      alert('Global LLM settings saved successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save global settings';
      setError(errorMessage);
      alert(errorMessage);
    }
  };


  if (isLoading) return <p>Loading LLM Management Console...</p>;
  // if (error) return <p style={{ color: 'red' }}>Error loading data: {error}</p>; // Display persistent error at top

  return (
    <div style={{ padding: '2rem', fontFamily: 'sans-serif' }}>
      <header style={{ marginBottom: '2rem', borderBottom: '1px solid #ccc', paddingBottom: '1rem' }}>
        <h1>LLM Management</h1>
        <p>Configuration and management of available Large Language Models (LLMs) will be available here.</p>
      </header>

      {error && <p style={{ color: 'red', backgroundColor: '#ffebee', padding: '1rem', borderRadius: '4px', border: '1px solid #ef9a9a', marginBottom: '1rem' }}>Error: {error}</p>}

      <section>
        <h2>Provider API Keys</h2>
        <p>Enter and test API keys for your LLM providers. The key will be stored securely. "Save Key" will add a new credential.</p>
        {providers.map(provider => {
          const providerCreds = credentialsMap[provider.id] || [];
          
          return (
            <Card key={provider.id} title={provider.name}>
              {provider.name.toLowerCase() !== 'ollama' && (
                <>
                  <Input
                    label={`New ${provider.name} API Key`}
                    type="password"
                    placeholder={"Enter API Key to Add"}
                    value={apiKeyInputs[provider.id] || ''}
                    onChange={(e) => setApiKeyInputs(prev => ({ ...prev, [provider.id]: e.target.value }))}
                  />
                  <Button onClick={() => handleApiKeySave(provider.id)}>
                    Add New Key
                  </Button>
                </>
              )}
              {provider.name.toLowerCase() === 'ollama' && (
                <>
                  <Input
                    label="Ollama Base URL (Optional)"
                    type="text"
                    placeholder="e.g., http://localhost:11434 - Leave blank for default"
                    value={ollamaBaseUrlInputs[provider.id] !== undefined ? ollamaBaseUrlInputs[provider.id] : (provider.api_base_url || '')}
                    onChange={(e) => setOllamaBaseUrlInputs(prev => ({ ...prev, [provider.id]: e.target.value }))}
                  />
                  <Button onClick={() => handleOllamaBaseUrlSave(provider.id)}>
                    Save Base URL
                  </Button>
                </>
              )}

              {providerCreds.length > 0 && provider.name.toLowerCase() !== 'ollama' && (
                <div style={{ marginTop: '1rem', borderTop: '1px solid #eee', paddingTop: '1rem' }}>
                  <h4 style={{marginTop: 0}}>Existing Credentials:</h4>
                  {providerCreds.map(cred => (
                    <div key={cred.id} style={{ marginBottom: '0.75rem', padding: '0.5rem', border: '1px solid #ddd', borderRadius: '4px' }}>
                      <p style={{ margin: '0 0 0.25rem 0', fontWeight: 'bold' }}>Credential ID: {cred.id}</p>
                      <p style={{ margin: '0 0 0.25rem 0', fontSize: '0.9em' }}>
                        Status: {cred.is_active ? 'Active' : 'Inactive'}
                        {cred.last_tested_at && (
                          <span> | Last Tested: {new Date(cred.last_tested_at).toLocaleString()} ({cred.last_test_successful ? 'Success' : 'Fail'})</span>
                        )}
                      </p>
                      <Button onClick={() => handleTestConnection(cred.id)} variant="secondary" style={{padding: '0.25rem 0.5rem', fontSize: '0.9em'}}>
                        Test This Key
                      </Button>
                      <Button onClick={() => handleDeleteCredential(cred.id, provider.id)} variant="danger" style={{padding: '0.25rem 0.5rem', fontSize: '0.9em'}}>
                        Delete This Key
                      </Button>
                      {testResults[cred.id] && (
                        <p style={{ marginTop: '0.25rem', color: testResults[cred.id].success ? 'green' : 'red', fontSize: '0.8em' }}>
                          Test: {testResults[cred.id].message}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card>
          );
        })}
      </section>

      <section style={{ marginTop: '2rem' }}>
        <h2>Global LLM Settings</h2>
        <p>Select the default LLM providers and models for embedding and chat functionalities across the platform.</p>
        {/* Changed form to div and button to explicit onClick */}
        <div>
          <Card title="Embedding LLM">
            <Select
              label="Embedding Provider"
              value={currentEmbeddingProviderId}
              onChange={(e) => setCurrentEmbeddingProviderId(e.target.value)}
            >
              <option value="">-- Select Embedding Provider --</option>
              {providers.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
            </Select>
            <Input
              label="Embedding Model Name"
              placeholder="e.g., nomic-embed-text, text-embedding-004"
              value={currentEmbeddingModel}
              onChange={(e) => setCurrentEmbeddingModel(e.target.value)}
            />
          </Card>

          <Card title="Chat LLM">
            <Select
              label="Chat Provider"
              value={currentChatProviderId}
              onChange={(e) => setCurrentChatProviderId(e.target.value)}
            >
              <option value="">-- Select Chat Provider --</option>
              {providers.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
            </Select>
            <Input
              label="Chat Model Name"
              placeholder="e.g., llama3, gpt-4o, gemini-pro"
              value={currentChatModel}
              onChange={(e) => setCurrentChatModel(e.target.value)}
            />
          </Card>

          <Card title="Assistant LLM">
            <Select
              label="Assistant Provider"
              value={currentAssistantProviderId}
              onChange={(e) => setCurrentAssistantProviderId(e.target.value)}
            >
              <option value="">-- Select Assistant Provider --</option>
              {providers.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
            </Select>
            <Input
              label="Assistant Model Name"
              placeholder="e.g., claude-3-opus, gpt-4-turbo"
              value={currentAssistantModel}
              onChange={(e) => setCurrentAssistantModel(e.target.value)}
            />
          </Card>
          <Button onClick={handleGlobalSettingsSave} variant="primary" style={{ marginTop: '1rem' }}>Save Global Settings</Button>
        </div>
        {globalSettings && (
          <div style={{ marginTop: '1rem', fontSize: '0.9em', background: '#f0f0f0', padding: '0.5rem', borderRadius: '4px' }}>
            <h4>Current Saved Global Settings:</h4>
            <p><strong>Embedding:</strong> {globalSettings.embedding_llm_provider_name || providers.find(p=>p.id === globalSettings.embedding_llm_provider_id)?.name || 'N/A'} - {globalSettings.embedding_model_name || 'N/A'}</p>
            <p><strong>Chat:</strong> {globalSettings.chat_llm_provider_name || providers.find(p=>p.id === globalSettings.chat_llm_provider_id)?.name || 'N/A'} - {globalSettings.chat_model_name || 'N/A'}</p>
            <p><strong>Assistant:</strong> {globalSettings.assistant_llm_provider_name || providers.find(p=>p.id === globalSettings.assistant_llm_provider_id)?.name || 'N/A'} - {globalSettings.assistant_model_name || 'N/A'}</p>
          </div>
        )}
      </section>
    </div>
  );
}
