"use client";

import React, { useState, useEffect } from 'react';

// Define interfaces for Assistant Agent data
interface AssistantAgent {
  id: number;
  name: string;
  parent_prompt: string;
  main_prompt: string;
  prompt_sections?: PromptSection[]; // Added
  mcp_ids?: number[]; // Added
  created_at: string;
  updated_at: string;
}

interface PromptSection {
  id?: number;
  type: string;
  content: string;
}

interface MCP {
  id: number;
  name: string;
  url: string;
  is_active: boolean;
}

// API configuration
const CENTRAL_API_URL = process.env.NEXT_PUBLIC_CENTRAL_API_URL || 'http://localhost:8008';
const ASSISTANT_API_URL = process.env.NEXT_PUBLIC_ASSISTANT_API_URL || 'http://localhost:6015';

// Basic UI Components
const Card = ({ title, children, className = "" }: { title: string, children: React.ReactNode, className?: string }) => (
  <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm ${className}`}>
    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
    </div>
    <div className="p-6">
      {children}
    </div>
  </div>
);

const Button = ({ 
  children, 
  onClick, 
  type = "button", 
  variant = "primary",
  size = "md",
  disabled = false,
  className = ""
}: {
  children: React.ReactNode;
  onClick?: () => void;
  type?: "button" | "submit";
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  className?: string;
}) => {
  const baseClasses = "font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 disabled:bg-gray-100",
    danger: "bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 disabled:bg-red-300"
  };
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base"
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {children}
    </button>
  );
};

const Input = ({ 
  label, 
  value, 
  onChange, 
  type = "text", 
  placeholder = "",
  required = false,
  className = "",
  disabled = false
}: {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
}) => (
  <div className={`space-y-2 ${className}`}>
    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <input
      type={type}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      required={required}
      disabled={disabled}
      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
    />
  </div>
);

const Textarea = ({ 
  label, 
  value, 
  onChange, 
  placeholder = "",
  rows = 4,
  required = false,
  className = ""
}: {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  rows?: number;
  required?: boolean;
  className?: string;
}) => (
  <div className={`space-y-2 ${className}`}>
    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <textarea
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      rows={rows}
      required={required}
      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
    />
  </div>
);

const Checkbox = ({
  label,
  checked,
  onChange,
  className = ""
}: {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
}) => (
  <div className={`flex items-center space-x-2 ${className}`}>
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onChange(e.target.checked)}
      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
    />
    <label className="text-sm text-gray-700 dark:text-gray-300">{label}</label>
  </div>
);

export default function AdminAgentsPage() {
  const [assistantAgent, setAssistantAgent] = useState<AssistantAgent | null>(null);
  const [mcps, setMcps] = useState<MCP[]>([]);
  const [isEditing, setIsEditing] = useState(false); // Always editing the single agent
  const [isCreating, setIsCreating] = useState(false); // True if no agent exists yet
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    name: '',
    parent_prompt: '',
    main_prompt: ''
  });

  const [promptSections, setPromptSections] = useState<PromptSection[]>([
    { type: 'leads', content: '' },
    { type: 'customer_issues', content: '' },
    { type: 'summary', content: '' }
  ]);

  const [selectedMcps, setSelectedMcps] = useState<number[]>([]);

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch the single assistant agent
      const agentsRes = await fetch(`${ASSISTANT_API_URL}/assistant-agents`);
      if (agentsRes.ok) {
        const agentsData = await agentsRes.json();
        if (agentsData.agents && agentsData.agents.length > 0) {
          const agent = agentsData.agents[0]; // Assume only one assistant agent
          setAssistantAgent(agent);
          setFormData({
            name: agent.name,
            parent_prompt: agent.parent_prompt,
            main_prompt: agent.main_prompt
          });
          // Load prompt sections and MCPs for the existing agent
          await loadAgentDetails(agent.id);
          setIsEditing(true); // Already exists, so we are editing
          setIsCreating(false);
        } else {
          setAssistantAgent(null);
          setIsCreating(true); // No agent exists, so we are creating
          setIsEditing(false);
        }
      }

      // Fetch MCPs from assistant service
      const mcpsRes = await fetch(`${ASSISTANT_API_URL}/mcps`);
      if (mcpsRes.ok) {
        const mcpsData = await mcpsRes.json();
        setMcps(mcpsData.mcps || []);
      }

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const loadAgentDetails = async (agentId: number) => {
    try {
      // Fetch prompt sections
      const promptSectionsRes = await fetch(`${ASSISTANT_API_URL}/assistant-agents/${agentId}/prompt-sections`);
      if (promptSectionsRes.ok) {
        const sectionsData = await promptSectionsRes.json();
        setPromptSections(sectionsData.prompt_sections || []);
      } else {
        console.warn("Failed to fetch prompt sections for agent:", agentId);
        setPromptSections([
          { type: 'leads', content: '' },
          { type: 'customer_issues', content: '' },
          { type: 'summary', content: '' }
        ]);
      }

      // Fetch MCP associations
      const mcpsRes = await fetch(`${ASSISTANT_API_URL}/assistant-agents/${agentId}/mcps`);
      if (mcpsRes.ok) {
        const mcpsData = await mcpsRes.json();
        setSelectedMcps(mcpsData.mcp_ids || []);
      } else {
        console.warn("Failed to fetch MCP associations for agent:", agentId);
        setSelectedMcps([]);
      }
    } catch (error) {
      console.error("Error loading agent details:", error);
      setError(error instanceof Error ? error.message : 'Failed to load agent details');
    }
  };

  const handleSaveAgent = async () => {
    try {
      const payload = {
        ...formData,
        prompt_sections: promptSections,
        mcp_ids: selectedMcps
      };

      let response;
      if (assistantAgent) { // If agent exists, update it
        response = await fetch(`${ASSISTANT_API_URL}/assistant-agents/${assistantAgent.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
      } else { // If no agent exists, create it
        response = await fetch(`${ASSISTANT_API_URL}/assistant-agents`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
      }

      if (response?.ok) {
        await fetchData(); // Re-fetch data to update UI
        // After saving, we are always in editing mode for the single agent
        setIsCreating(false);
        setIsEditing(true);
      } else {
        throw new Error('Failed to save agent');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save agent');
    }
  };

  const handleCancel = () => {
    // If creating and cancel, go back to initial state (no agent selected)
    if (isCreating) {
      setAssistantAgent(null);
      setFormData({ name: '', parent_prompt: '', main_prompt: '' });
      setPromptSections([
        { type: 'leads', content: '' },
        { type: 'customer_issues', content: '' },
        { type: 'summary', content: '' }
      ]);
      setSelectedMcps([]);
      setIsCreating(false);
      setIsEditing(false);
    } else {
      // If editing and cancel, revert to original data (re-fetch)
      if (assistantAgent) {
        loadAgentDetails(assistantAgent.id);
        setFormData({
          name: assistantAgent.name,
          parent_prompt: assistantAgent.parent_prompt,
          main_prompt: assistantAgent.main_prompt
        });
      }
    }
  };
  const updatePromptSection = (type: string, content: string) => {
    setPromptSections(prev => 
      prev.map(section => 
        section.type === type ? { ...section, content } : section
      )
    );
  };

  const toggleMcp = (mcpId: number) => {
    setSelectedMcps(prev => 
      prev.includes(mcpId) 
        ? prev.filter(id => id !== mcpId)
        : [...prev, mcpId]
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <p className="text-center text-gray-600">Loading Assistant Agents...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8"> {/* 1. Main container: Starts */}
      <div className="flex justify-between items-center mb-8"> {/* 2. Header container: Starts */}
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
          Assistant Agent Configuration
        </h1>
      </div> {/* 2. Header container: Ends */}

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )} {/* Conditional error message container: Ends */}
      <div className="grid grid-cols-1 gap-6"> {/* 3. Grid container for content: Starts */}
        {/* Agent Form */}
        <div className="lg:col-span-2"> {/* 4. Agent form column: Starts */}
          <div className="space-y-6"> {/* 5. Wrapper for cards and buttons: Starts */}
            <Card title={assistantAgent ? "Edit Assistant Agent" : "Create Assistant Agent"}>
              <div className="space-y-4">
                <Input
                  label="Agent Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter agent name"
                  required
                />
                <Textarea
                  label="Parent Prompt"
                  value={formData.parent_prompt}
                  onChange={(e) => setFormData(prev => ({ ...prev, parent_prompt: e.target.value }))}
                  placeholder="Enter the parent prompt that sets the base context"
                  rows={4}
                  required
                />
                <Textarea
                  label="Main Prompt"
                  value={formData.main_prompt}
                  onChange={(e) => setFormData(prev => ({ ...prev, main_prompt: e.target.value }))}
                  placeholder="Enter the main prompt for the agent"
                  rows={6}
                  required
                />
              </div>
            </Card>

            <Card title="Prompt Sections">
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Configure specific prompt sections that will be activated based on chatbot settings.
                </p>
                {promptSections.map((section) => (
                  <Textarea
                    key={section.type}
                    label={`${section.type.replace('_', ' ').toUpperCase()} Prompt`}
                    value={section.content}
                    onChange={(e) => updatePromptSection(section.type, e.target.value)}
                    placeholder={`Enter prompt for ${section.type.replace('_', ' ')} processing`}
                    rows={3}
                  />
                ))}
              </div>
            </Card>

            <Card title="MCP Tool Assignments">
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Select which MCP tools this agent can use.
                </p>
                {mcps.length === 0 ? (
                  <p className="text-gray-500">No MCP tools available</p>
                ) : (
                  <div className="space-y-2">
                    {mcps.filter(mcp => mcp.is_active).map((mcp) => (
                      <Checkbox
                        key={mcp.id}
                        label={`${mcp.name} (${mcp.url})`}
                        checked={selectedMcps.includes(mcp.id)}
                        onChange={() => toggleMcp(mcp.id)}
                      />
                    ))}
                  </div>
                )}
              </div>
            </Card>

            <div className="flex justify-end space-x-3"> {/* 6. Buttons container: Starts */}
              <Button onClick={handleCancel} variant="secondary">
                Cancel
              </Button>
              <Button onClick={handleSaveAgent} variant="primary">
                {isCreating ? 'Create Agent' : 'Save Changes'}
              </Button>
            </div> {/* 6. Buttons container: Ends */}
          </div> {/* 5. Wrapper for cards and buttons: Ends */}
        </div> {/* 4. Agent form column: Ends */}
      </div> {/* 3. Grid container for content: Ends */}
    </div> /* 1. Main container: Ends */
  );
}