"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Bot, MessageSquare, Database, Server, BrainCircuit, BarChart3, Loader2 } from 'lucide-react'; // Added Server, BrainCircuit, Loader2 icons

interface DashboardData {
  totalUsers: number | null;
  usersPerPlan: { [key: string]: number };
  totalChatbots: number | null;
  embeddedChatbots: number | null;
  floatingChatbots: number | null;
  totalChunksInDB: number | null;
  totalMessages: number | null;
  activeMcps: { name: string; url: string }[];
  globalLlmSettings: {
    embedding_model_name: string;
    embedding_provider_name: string;
    chat_model_name: string;
    chat_provider_name: string;
  };
}

export default function AdminDashboardPage() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`${process.env.NEXT_PUBLIC_CENTRAL_API_URL}/api/admin/dashboard/status`); // Use environment variable
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data: DashboardData = await response.json();
        setDashboardData(data);
      } catch (e: any) {
        console.error("Failed to fetch dashboard data:", e);
        setError(`Failed to load data: ${e.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const adminStats = [
    {
      title: 'Total Users',
      value: dashboardData?.totalUsers !== null ? dashboardData?.totalUsers : 'N/A',
      icon: Users,
      details: ''
    },
    {
      title: 'Total Chatbots',
      value: dashboardData?.totalChatbots !== null ? dashboardData?.totalChatbots : 'N/A',
      icon: Bot,
      details: `(Embedded: ${dashboardData?.embeddedChatbots !== null ? dashboardData?.embeddedChatbots : 'N/A'}, Floating: ${dashboardData?.floatingChatbots !== null ? dashboardData?.floatingChatbots : 'N/A'})`
    },
    {
      title: 'Total Chunks in DB',
      value: dashboardData?.totalChunksInDB !== null ? dashboardData?.totalChunksInDB : 'N/A',
      icon: Database,
      details: '(From RAG system)'
    },
    {
      title: 'Total Messages',
      value: dashboardData?.totalMessages !== null ? dashboardData?.totalMessages : 'N/A',
      icon: MessageSquare,
      details: '(Across all chatbots)'
    },
    {
      title: 'Users per Plan',
      value: 'See Details',
      icon: BarChart3,
      details: `(Free: ${dashboardData?.usersPerPlan?.FREE !== undefined ? dashboardData.usersPerPlan.FREE : 'N/A'}, Base: ${dashboardData?.usersPerPlan?.BASE !== undefined ? dashboardData.usersPerPlan.BASE : 'N/A'}, Pro: ${dashboardData?.usersPerPlan?.PRO !== undefined ? dashboardData.usersPerPlan.PRO : 'N/A'})`
    },
  ];

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-8">
        Admin Dashboard
      </h1>
      
      {loading ? (
        <div className="flex items-center justify-center h-48">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <p className="ml-3 text-gray-500">Loading dashboard data...</p>
        </div>
      ) : error ? (
        <div className="text-red-500 text-center py-8">
          <p>{error}</p>
          <p>Please ensure the Central API Service is running and accessible.</p>
        </div>
      ) : (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {adminStats.map((stat) => (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className="h-5 w-5 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  {stat.details && <p className="text-xs text-muted-foreground">{stat.details}</p>}
                </CardContent>
              </Card>
            ))}

            {/* New Card: Active MCPs */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active MCPs</CardTitle>
                <Server className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.activeMcps?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.activeMcps && dashboardData.activeMcps.length > 0 ? (
                    dashboardData.activeMcps.map((mcp, index) => (
                      <span key={index} className="block">{mcp.name} ({mcp.url})</span>
                    ))
                  ) : (
                    'No active MCPs found.'
                  )}
                </p>
              </CardContent>
            </Card>

            {/* New Card: Active Global LLM Settings */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Global LLM</CardTitle>
                <BrainCircuit className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.globalLlmSettings?.chat_model_name || 'N/A'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Chat: {dashboardData?.globalLlmSettings?.chat_provider_name || 'N/A'} - {dashboardData?.globalLlmSettings?.chat_model_name || 'N/A'}
                  <br />
                  Embedding: {dashboardData?.globalLlmSettings?.embedding_provider_name || 'N/A'} - {dashboardData?.globalLlmSettings?.embedding_model_name || 'N/A'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Placeholder for more detailed charts or tables */}
          <div className="mt-10">
            <h2 className="text-2xl font-semibold mb-4">Detailed Analytics (Placeholder)</h2>
            <Card>
              <CardContent className="p-6">
                <p className="text-muted-foreground">
                  More detailed analytics, charts, and data tables will be displayed here.
                  This section will provide deeper insights into user activity, chatbot performance,
                  and system health.
                </p>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}