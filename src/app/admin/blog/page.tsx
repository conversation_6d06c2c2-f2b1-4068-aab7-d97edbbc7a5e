import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileText, List } from "lucide-react";

export default function AdminBlogPage() {
  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-6">Blog Management</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl">
        <Link href="/admin/blog/categories">
          <Button className="w-full h-32 flex flex-col items-center justify-center gap-2">
            <List className="h-8 w-8" />
            <span className="text-lg">Manage Categories</span>
          </Button>
        </Link>
        
        <Link href="/admin/blog/posts">
          <Button className="w-full h-32 flex flex-col items-center justify-center gap-2">
            <FileText className="h-8 w-8" />
            <span className="text-lg">Manage Posts</span>
          </Button>
        </Link>
      </div>
    </div>
  );
}
