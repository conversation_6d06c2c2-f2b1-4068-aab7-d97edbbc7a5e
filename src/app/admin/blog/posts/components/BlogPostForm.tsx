"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BlogCategory, BlogPost } from "@prisma/client";

interface BlogPostFormProps {
  initialData?: BlogPost;
  onSubmit: (formData: any) => void;
  isSubmitting: boolean;
  isEditMode?: boolean;
  categories: BlogCategory[];
}

export default function BlogPostForm({ 
  initialData, 
  onSubmit, 
  isSubmitting,
  isEditMode = false,
  categories
}: BlogPostFormProps) {
  const [formData, setFormData] = useState({
    title: initialData?.title || "",
    excerpt: initialData?.excerpt || "",
    content: initialData?.content || "",
    status: initialData?.status || "DRAFT",
    categoryId: initialData?.categoryId || (categories.length > 0 ? categories[0].id : ""),
    metaTitle: initialData?.metaTitle || "",
    metaDescription: initialData?.metaDescription || ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">Title *</label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Post title"
            disabled={isSubmitting}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">Category *</label>
          <Select
            value={formData.categoryId}
            onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}
            disabled={isSubmitting}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2">Excerpt</label>
        <Textarea
          name="excerpt"
          value={formData.excerpt}
          onChange={handleChange}
          placeholder="Short excerpt (optional)"
          rows={3}
          disabled={isSubmitting}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2">Content *</label>
        <Textarea
          name="content"
          value={formData.content}
          onChange={handleChange}
          placeholder="Write your post content here"
          rows={10}
          disabled={isSubmitting}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2">Status</label>
        <Select
          value={formData.status}
          onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
          disabled={isSubmitting}
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="DRAFT">Draft</SelectItem>
            <SelectItem value="PUBLISHED">Published</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">Meta Title (optional)</label>
          <Input
            name="metaTitle"
            value={formData.metaTitle}
            onChange={handleChange}
            placeholder="SEO meta title"
            disabled={isSubmitting}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">Meta Description (optional)</label>
          <Textarea
            name="metaDescription"
            value={formData.metaDescription}
            onChange={handleChange}
            placeholder="SEO meta description"
            rows={3}
            disabled={isSubmitting}
          />
        </div>
      </div>
      
      <div className="flex gap-4">
        <Button type="submit" disabled={isSubmitting}>
          {isEditMode ? "Update Post" : "Create Post"}
        </Button>
        <Button 
          variant="outline" 
          type="button"
          onClick={() => window.history.back()}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
      </div>
    </form>
  );
}
