"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import axios from "axios";
import BlogPostForm from "../components/BlogPostForm";

export default function NewBlogPostPage() {
  const { toast } = useToast();
  const router = useRouter();
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axios.get("/api/admin/blog/categories");
      setCategories(response.data);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch categories",
        variant: "destructive"
      });
    }
  };

  const handleSubmit = async (formData: any) => {
    if (!formData.title || !formData.content || !formData.categoryId) {
      toast({
        title: "Validation Error",
        description: "Title, content, and category are required",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      await axios.post("/api/admin/blog/posts", formData);
      toast({ title: "Success", description: "Blog post created successfully" });
      router.push("/admin/blog/posts");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create blog post",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-6">New Blog Post</h1>
      <BlogPostForm 
        onSubmit={handleSubmit} 
        isSubmitting={isLoading} 
        categories={categories}
      />
    </div>
  );
}
