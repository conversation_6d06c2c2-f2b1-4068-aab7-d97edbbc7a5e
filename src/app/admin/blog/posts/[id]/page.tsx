"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import axios from "axios";
import BlogPostForm from "../components/BlogPostForm";

export default function EditBlogPostPage({ params }: { params: { id: string } }) {
  const { toast } = useToast();
  const router = useRouter();
  const [postData, setPostData] = useState<any>(null);
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [postResponse, categoriesResponse] = await Promise.all([
          axios.get(`/api/admin/blog/posts/${params.id}`),
          axios.get("/api/admin/blog/categories")
        ]);
        
        setPostData(postResponse.data);
        setCategories(categoriesResponse.data);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to fetch data",
          variant: "destructive"
        });
        router.push("/admin/blog/posts");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [params.id]);

  const handleSubmit = async (formData: any) => {
    setIsLoading(true);
    try {
      await axios.put(`/api/admin/blog/posts/${params.id}`, formData);
      toast({ title: "Success", description: "Blog post updated successfully" });
      router.push("/admin/blog/posts");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update blog post",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <p>Loading post data...</p>
      </div>
    );
  }

  if (!postData) {
    return (
      <div className="p-8">
        <p>Post not found</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-6">Edit Blog Post</h1>
      <BlogPostForm 
        initialData={postData} 
        onSubmit={handleSubmit} 
        isSubmitting={isLoading} 
        categories={categories}
        isEditMode={true}
      />
    </div>
  );
}
