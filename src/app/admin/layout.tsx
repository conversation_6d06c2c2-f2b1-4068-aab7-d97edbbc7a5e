'use client'; // For consistency, though AdminSidebar itself is client

import React from 'react';
import AdminSidebar from '@/components/layout/AdminSidebar';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This layout is rendered within the RootLayout,
  // so it inherits the main header.
  // We provide the admin-specific sidebar and content area.
  return (
    <>
      <AdminSidebar />
      {/* The main content area needs to be offset by the sidebar width */}
      {/* The RootLayout's main content already has 'md:ml-64' if its sidebar is shown. */}
      {/* We need to ensure this admin content area is also correctly offset if the RootLayout's sidebar is NOT shown but AdminSidebar IS. */}
      {/* However, AdminLayout is ONLY for /admin/* routes, and RootLayout's sidebar is ONLY for /dashboard/* routes. */}
      {/* So, they won't show at the same time. The md:ml-64 here is for the AdminSidebar. */}
      <div className="md:ml-64"> 
        {children}
      </div>
    </>
  );
}