"use client";

import React, { useState, useEffect } from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';

interface PromptSnippet {
  id: string;
  name: string;
  content: string;
  type: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function AdminPromptsPage() {
  const [promptSnippets, setPromptSnippets] = useState<PromptSnippet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingSnippet, setEditingSnippet] = useState<PromptSnippet | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchPromptSnippets();
  }, []);

  const fetchPromptSnippets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/prompts');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: PromptSnippet[] = await response.json();
      setPromptSnippets(data);
    } catch (e: any) {
      setError(e.message);
      toast({
        title: 'Error',
        description: `Failed to fetch prompt snippets: ${e.message}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (snippet: PromptSnippet) => {
    setEditingSnippet({ ...snippet });
  };

  const handleSave = async () => {
    if (!editingSnippet) return;

    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editingSnippet),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const updatedSnippet: PromptSnippet = await response.json();
      setPromptSnippets((prevSnippets) =>
        prevSnippets.map((s) => (s.id === updatedSnippet.id ? updatedSnippet : s))
      );
      setEditingSnippet(null);
      toast({
        title: 'Success',
        description: 'Prompt snippet saved successfully!',
        variant: 'default',
      });
    } catch (e: any) {
      toast({
        title: 'Error',
        description: `Failed to save prompt snippet: ${e.message}`,
        variant: 'destructive',
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingSnippet(null);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditingSnippet((prev) => (prev ? { ...prev, [name]: value } : null));
  };

  const handleSwitchChange = (checked: boolean) => {
    setEditingSnippet((prev) => (prev ? { ...prev, isActive: checked } : null));
  };

  const handleSelectChange = (value: string) => {
    setEditingSnippet((prev) => (prev ? { ...prev, type: value } : null));
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-8">
          Prompt Management
        </h1>
        <p>Loading prompt snippets...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-8">
          Prompt Management
        </h1>
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-8">
        Prompt Management
      </h1>

      {editingSnippet ? (
        <div className="p-6 border rounded-lg bg-gray-50 dark:bg-gray-800 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Edit Prompt Snippet</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Name
              </Label>
              <Input
                id="name"
                name="name"
                value={editingSnippet.name}
                onChange={handleChange}
                className="mt-1 block w-full"
              />
            </div>
            <div>
              <Label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Type
              </Label>
              <Select value={editingSnippet.type} onValueChange={handleSelectChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BASE_SYSTEM_PROMPT">BASE_SYSTEM_PROMPT</SelectItem>
                  <SelectItem value="GOAL_INSTRUCTION">GOAL_INSTRUCTION</SelectItem>
                  <SelectItem value="TONE_MODIFIER">TONE_MODIFIER</SelectItem>
                  <SelectItem value="INDUSTRY_CONTEXT">INDUSTRY_CONTEXT</SelectItem>
                  <SelectItem value="AUDIENCE_ADJUSTMENT">AUDIENCE_ADJUSTMENT</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Content
              </Label>
              <Textarea
                id="content"
                name="content"
                value={editingSnippet.content}
                onChange={handleChange}
                rows={10}
                className="mt-1 block w-full"
              />
            </div>
            <div className="flex items-center space-x-2 md:col-span-2">
              <Switch
                id="isActive"
                checked={editingSnippet.isActive}
                onCheckedChange={handleSwitchChange}
              />
              <Label htmlFor="isActive">Is Active</Label>
            </div>
          </div>
          <div className="mt-6 flex justify-end space-x-4">
            <Button variant="outline" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </div>
      ) : (
        <div className="p-6 border rounded-lg bg-gray-50 dark:bg-gray-800">
          <h2 className="text-2xl font-semibold mb-4">Available Prompt Snippets</h2>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Is Active</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {promptSnippets.map((snippet) => (
                  <TableRow key={snippet.id}>
                    <TableCell className="font-medium">{snippet.name}</TableCell>
                    <TableCell>{snippet.type}</TableCell>
                    <TableCell>{snippet.isActive ? 'Yes' : 'No'}</TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" onClick={() => handleEdit(snippet)}>
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
}