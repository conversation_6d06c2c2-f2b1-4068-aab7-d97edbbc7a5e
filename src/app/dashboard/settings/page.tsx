'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label'; // Assuming Label is also a Shadcn/ui component

// Placeholder for session data - in a real app, you'd get this from NextAuth
const DUMMY_USER_EMAIL = "<EMAIL>";

export default function SettingsPage() {
  // In a real application, these would be handled with state and API calls
  const handleUpdateEmail = (e: React.FormEvent) => {
    e.preventDefault();
    alert('Update Email functionality to be implemented.');
  };

  const handleUpdatePassword = (e: React.FormEvent) => {
    e.preventDefault();
    alert('Update Password functionality to be implemented.');
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-8">
        Settings
      </h1>

      <div className="grid gap-8 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>Manage your account details.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <form onSubmit={handleUpdateEmail} className="space-y-4">
              <div>
                <Label htmlFor="currentEmail">Current Email</Label>
                <Input id="currentEmail" type="email" defaultValue={DUMMY_USER_EMAIL} readOnly disabled className="mt-1" />
                <p className="text-xs text-gray-500 mt-1">Your current email address is read-only here.</p>
              </div>
              <div>
                <Label htmlFor="newEmail">New Email Address</Label>
                <Input id="newEmail" type="email" placeholder="<EMAIL>" className="mt-1" />
              </div>
              <Button type="submit">Update Email</Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Password</CardTitle>
            <CardDescription>Change your password.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <form onSubmit={handleUpdatePassword} className="space-y-4">
              <div>
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input id="currentPassword" type="password" className="mt-1" />
              </div>
              <div>
                <Label htmlFor="newPassword">New Password</Label>
                <Input id="newPassword" type="password" className="mt-1" />
              </div>
              <div>
                <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
                <Input id="confirmNewPassword" type="password" className="mt-1" />
              </div>
              <Button type="submit">Change Password</Button>
            </form>
          </CardContent>
        </Card>
      </div>
      {/* Add more settings sections as needed, e.g., Profile, Notifications, etc. */}
    </div>
  );
}