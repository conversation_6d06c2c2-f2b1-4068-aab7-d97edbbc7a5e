'use client';

import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Label } from "@/components/ui/label"; // ADDED THIS IMPORT
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { List, MessageSquareText, UserCircle, Bot } from 'lucide-react';

interface ChatbotInstanceForHistory {
  id: string;
  name: string;
}

interface ChatSessionForHistory {
  session_db_id: string; // The UUID PK from chat_sessions table
  chatbot_instance_id: string; // The CUID from SaaS DB
  user_identifier?: string | null;
  start_time: string;
  end_time?: string | null;
  langserve_thread_id?: string | null;
  // Potentially add first message snippet or message count later
}

interface ChatMessageForHistory {
  message_db_id: string;
  sender: string; // 'user' or 'agent'
  content: string;
  timestamp: string;
}

export default function ChatHistoryPage() {
  const [userChatbots, setUserChatbots] = useState<ChatbotInstanceForHistory[]>([]);
  const [selectedChatbotId, setSelectedChatbotId] = useState<string | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSessionForHistory[]>([]);
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessageForHistory[]>([]);
  const [isLoadingChatbots, setIsLoadingChatbots] = useState(true);
  const [isLoadingSessions, setIsLoadingSessions] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  // Fetch user's chatbots
  useEffect(() => {
    const fetchUserChatbots = async () => {
      setIsLoadingChatbots(true);
      try {
        const response = await fetch('/api/chatbot-instances'); // Assuming this GETs all instances for the user
        if (!response.ok) {
          throw new Error('Failed to fetch chatbots');
        }
        const data: ChatbotInstanceForHistory[] = await response.json();
        setUserChatbots(data);
        if (data.length > 0) {
          setSelectedChatbotId(data[0].id); // Select the first chatbot by default
        }
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Could not load your chatbots.');
        console.error("Error fetching user chatbots:", error);
      } finally {
        setIsLoadingChatbots(false);
      }
    };
    fetchUserChatbots();
  }, []);

  // Fetch sessions when a chatbot is selected
  useEffect(() => {
    if (!selectedChatbotId) {
      setChatSessions([]);
      setMessages([]);
      setSelectedSessionId(null);
      return;
    }

    const fetchSessions = async () => {
      setIsLoadingSessions(true);
      setMessages([]); // Clear messages when changing chatbot
      setSelectedSessionId(null);
      try {
        const response = await fetch(`/api/chat-history/sessions/${selectedChatbotId}`);
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: "Failed to parse error"}));
          throw new Error(errorData.message || `Failed to fetch sessions (status: ${response.status})`);
        }
        const data: ChatSessionForHistory[] = await response.json();
        setChatSessions(data);
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Could not load chat sessions.');
        console.error("Error fetching sessions:", error);
        setChatSessions([]);
      } finally {
        setIsLoadingSessions(false);
      }
    };
    fetchSessions();
  }, [selectedChatbotId]);

  // Fetch messages when a session is selected
  useEffect(() => {
    if (!selectedSessionId) {
      setMessages([]);
      return;
    }

    const fetchMessages = async () => {
      setIsLoadingMessages(true);
      try {
        const response = await fetch(`/api/chat-history/messages/${selectedSessionId}`);
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: "Failed to parse error"}));
          throw new Error(errorData.message || `Failed to fetch messages (status: ${response.status})`);
        }
        const data: ChatMessageForHistory[] = await response.json();
        setMessages(data);
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Could not load messages for this session.');
        console.error("Error fetching messages:", error);
        setMessages([]);
      } finally {
        setIsLoadingMessages(false);
      }
    };
    fetchMessages();
  }, [selectedSessionId]);

  const formatDateTime = (dateTimeString?: string | null) => {
    if (!dateTimeString) return 'N/A';
    try {
      return new Date(dateTimeString).toLocaleString(undefined, { 
        dateStyle: 'medium', 
        timeStyle: 'short' 
      });
    } catch (e) {
      return dateTimeString; // Fallback if date is invalid
    }
  };


  return (
    <div className="container mx-auto p-4 md:p-6">
      <h1 className="text-2xl sm:text-3xl font-bold mb-6">Chat History</h1>

      {isLoadingChatbots ? (
        <p>Loading your chatbots...</p>
      ) : userChatbots.length === 0 ? (
        <p>You don't have any chatbots yet. Create one to see chat history.</p>
      ) : (
        <div className="mb-6">
          <Label htmlFor="chatbot-select" className="text-base font-medium">Select Chatbot</Label>
          <Select
            value={selectedChatbotId || ''}
            onValueChange={(value) => setSelectedChatbotId(value)}
          >
            <SelectTrigger id="chatbot-select" className="w-full md:w-[300px] mt-1">
              <SelectValue placeholder="Select a chatbot" />
            </SelectTrigger>
            <SelectContent>
              {userChatbots.map(bot => (
                <SelectItem key={bot.id} value={bot.id}>{bot.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Sessions List */}
        <Card className="md:col-span-1 h-fit max-h-[70vh]">
          <CardHeader>
            <CardTitle className="flex items-center"><List className="mr-2 h-5 w-5" /> Sessions</CardTitle>
            <CardDescription>Select a session to view messages.</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingSessions ? (
              <p>Loading sessions...</p>
            ) : chatSessions.length === 0 && selectedChatbotId ? (
              <p>No chat sessions found for this chatbot.</p>
            ) : !selectedChatbotId && !isLoadingChatbots ? (
              <p>Please select a chatbot to view sessions.</p>
            ) : (
              <ScrollArea className="h-[50vh] pr-3"> {/* Adjust height as needed */}
                <div className="space-y-2">
                  {chatSessions.map(session => (
                    <Button
                      key={session.session_db_id}
                      variant={selectedSessionId === session.session_db_id ? "default" : "outline"}
                      className="w-full justify-start text-left h-auto py-2"
                      onClick={() => setSelectedSessionId(session.session_db_id)}
                    >
                      <div className="flex flex-col overflow-hidden"> {/* Added overflow-hidden for better truncation */}
                        <span className="font-medium text-sm truncate">
                          {session.user_identifier ?
                            `User: ${session.user_identifier}` :
                            (session.langserve_thread_id ? `Thread: ...${session.langserve_thread_id.slice(-12)}` : `Session started`)}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDateTime(session.start_time)}
                        </span>
                        {/* Removed direct display of user_identifier from here as it's incorporated above */}
                      </div>
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>

        {/* Messages View */}
        <Card className="md:col-span-2 max-h-[70vh] flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center"><MessageSquareText className="mr-2 h-5 w-5" /> Messages</CardTitle>
            {selectedSessionId ? 
              <CardDescription>Messages for session ...{selectedSessionId.slice(-8)}</CardDescription> :
              <CardDescription>Select a session to view its messages.</CardDescription>
            }
          </CardHeader>
          <CardContent className="flex-grow overflow-hidden">
            {isLoadingMessages ? (
              <p>Loading messages...</p>
            ) : !selectedSessionId ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <p>No session selected.</p>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <p>No messages in this session, or session is empty.</p>
              </div>
            ) : (
              <ScrollArea className="h-full pr-3"> {/* Occupy remaining height */}
                <div className="space-y-4">
                  {messages.map(msg => (
                    <div key={msg.message_db_id} className={`flex items-start gap-3 ${msg.sender.toLowerCase() === 'user' ? 'justify-end' : ''}`}>
                      {msg.sender.toLowerCase() === 'agent' && <Bot className="h-6 w-6 text-primary flex-shrink-0 mt-1" />}
                      <div className={`p-3 rounded-lg max-w-[75%] break-words text-sm ${
                        msg.sender.toLowerCase() === 'user' 
                        ? 'bg-primary text-primary-foreground ml-auto' 
                        : 'bg-muted'
                      }`}>
                        <p className="font-semibold mb-1 capitalize">{msg.sender}</p>
                        <p style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</p>
                        <p className="text-xs text-muted-foreground/80 mt-1 text-right">{formatDateTime(msg.timestamp)}</p>
                      </div>
                      {msg.sender.toLowerCase() === 'user' && <UserCircle className="h-6 w-6 text-muted-foreground flex-shrink-0 mt-1" />}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}