'use client';

import React, { ChangeEvent } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Link2, UploadCloud, Trash2, Eye, RefreshCw, Loader2 as SpinnerIcon } from 'lucide-react';
import { KnowledgeSource } from '../page'; // Assuming types are exported from page.tsx or a types file
import { toast } from 'sonner';

// Helper and Constants
const userFriendlyStatusMap: Record<string, string> = {
  'pending': 'Pending', 'uploaded': 'Uploaded', 'crawling_submitted': 'Queued for Crawl',
  'processing': 'Processing', 'active': 'Active', 'error': 'Error',
  'processing_error': 'Processing Error', 'unsupported_file_type': 'Unsupported File',
  'empty_content': 'Empty Content', 'chunking_failed': 'Processing Error (Chunking)',
  'processing_error_pdf': 'PDF Error', 'processing_error_extract': 'Extraction Error',
  'processing_error_unknown': 'Unknown Error', 'crawl_failed': 'Crawl Failed',
  'crawling': 'Gathering Data', 'scraping': 'Gathering Data',
  'scraping (firecrawl job in progress)': 'Gathering Data', 
  'crawling (firecrawl job in progress)': 'Gathering Data',
  'processing_crawl_data': 'Processing Data', 'completed': 'Finalizing',
  'completed (source status: processing_crawl_data)': 'Processing Data',
  'completed (source status: active)': 'Active', 'failed': 'Failed',
};

const getDisplayStatus = (status: string | null | undefined): string => {
  if (!status) return 'N/A';
  const lowerStatus = status.toLowerCase();
  const mappedStatus = userFriendlyStatusMap[lowerStatus];
  return mappedStatus || status;
};

interface KnowledgeTabContentProps {
  scrapeUrl: string;
  setScrapeUrl: (value: string) => void;
  scrapeType: 'FULL_SITE' | 'SINGLE_PAGE';
  setScrapeType: (value: 'FULL_SITE' | 'SINGLE_PAGE') => void;
  isScraping: boolean;
  uploadedFile: File | null;
  isUploading: boolean;
  knowledgeSources: KnowledgeSource[];
  handleScrapeWebsite: () => Promise<void>;
  handleFileUpload: (event: ChangeEvent<HTMLInputElement>) => void;
  handleProcessUpload: () => Promise<void>;
  handleDeleteKnowledgeSource: (sourceId: string) => Promise<void>;
  fetchChatbotData: (isRefresh?: boolean) => Promise<void>;
  setSelectedFileSource: (source: KnowledgeSource | null) => void;
  setIsViewFileModalOpen: (isOpen: boolean) => void;
  setSelectedWebsiteSource: (source: KnowledgeSource | null) => void;
  setIsViewWebsitePagesModalOpen: (isOpen: boolean) => void;
}

export default function KnowledgeTabContent({
  scrapeUrl,
  setScrapeUrl,
  scrapeType,
  setScrapeType,
  isScraping,
  uploadedFile,
  isUploading,
  knowledgeSources,
  handleScrapeWebsite,
  handleFileUpload,
  handleProcessUpload,
  handleDeleteKnowledgeSource,
  fetchChatbotData,
  setSelectedFileSource,
  setIsViewFileModalOpen,
  setSelectedWebsiteSource,
  setIsViewWebsitePagesModalOpen,
}: KnowledgeTabContentProps) {
  return (
    <div className="border rounded-lg p-6 bg-card shadow-lg">
      <h2 className="text-xl font-semibold mb-4">Knowledgebase Management</h2>
      <div className="space-y-6">
        <div className="space-y-3 p-4 border rounded-md">
          <h3 className="text-lg font-medium flex items-center"><Link2 className="mr-2 h-5 w-5" /> Pull Knowledge From Your Website</h3>
          <div className="space-y-2">
            <Label className="text-sm">Scrape Type</Label>
            <Select value={scrapeType} onValueChange={(value) => setScrapeType(value as 'FULL_SITE' | 'SINGLE_PAGE')}>
              <SelectTrigger className="w-[180px] mt-1"><SelectValue placeholder="Select scrape type" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="FULL_SITE">Full Site</SelectItem>
                <SelectItem value="SINGLE_PAGE">Single Page</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="scrapeUrl" className="text-sm">Website Address (URL)</Label>
            <Input id="scrapeUrl" type="url" value={scrapeUrl} onChange={(e) => setScrapeUrl(e.target.value)} placeholder="https://example.com" className="mt-1" disabled={isScraping}/>
          </div>
          <Button onClick={handleScrapeWebsite} disabled={isScraping || !scrapeUrl.trim()}>
            {isScraping ? <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isScraping ? 'Scraping...' : 'Scrape Website'}
          </Button>
        </div>
        <div className="space-y-3 p-4 border rounded-md">
          <h3 className="text-lg font-medium flex items-center"><UploadCloud className="mr-2 h-5 w-5" /> Upload Knowledge Files</h3>
          <div>
            <Label htmlFor="knowledgeFile" className="text-sm">Choose files to Upload (TXT, PDF, MD, DOCX)</Label>
            <Input id="knowledgeFile" type="file" onChange={handleFileUpload} accept=".txt,.pdf,.md,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" className="mt-1 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20" disabled={isUploading}/>
          </div>
          {uploadedFile && <p className="text-sm text-muted-foreground">Selected: {uploadedFile.name}</p>}
          <Button onClick={handleProcessUpload} disabled={isUploading || !uploadedFile}>
            {isUploading ? <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isUploading ? 'Uploading...' : 'Upload & Process File'}
          </Button>
        </div>
        <div className="space-y-3 p-4 border rounded-md">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Current Knowledge Sources</h3>
            <Button variant="ghost" size="icon" onClick={() => fetchChatbotData(true)} title="Refresh Knowledge Sources">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          {knowledgeSources.length === 0 ? (
            <p className="text-sm text-muted-foreground">No knowledge sources added yet.</p>
          ) : (
            <ul className="space-y-2">
              {knowledgeSources.map(source => (
                <li key={source.source_id} className="flex justify-between items-center p-2 border rounded-md bg-background hover:bg-accent/50">
                  <div>
                    <p className="font-medium text-sm">
                      { source.name || (source.type?.toLowerCase() === 'file' && source.metadata_json?.original_filename) || source.location_uri || 'Unknown Source' }
                    </p>
                    <p className="text-xs text-muted-foreground">Type: {source.type} | Status: <span className={`capitalize ${source.status === 'active' ? 'text-green-600' : (source.status?.toLowerCase().includes('error') || source.status?.toLowerCase().includes('fail')) ? 'text-red-600' : 'text-yellow-600'}`}>{getDisplayStatus(source.status)}</span></p>
                    <p className="text-xs text-muted-foreground">Added: {source.created_at ? new Date(source.created_at).toLocaleDateString() : 'N/A'}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="icon" title="View Content" onClick={() => {
                      const sourceTypeUpper = source.type?.toUpperCase();
                      if (sourceTypeUpper === 'FILE' || sourceTypeUpper === 'SINGLE_PAGE') {
                        setSelectedFileSource(source); setIsViewFileModalOpen(true);
                      } else if (sourceTypeUpper === 'WEBSITE') {
                        setSelectedWebsiteSource(source); setIsViewWebsitePagesModalOpen(true);
                      } else {
                        toast.info(`Viewing for type '${source.type}' coming soon.`);
                      }
                    }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" title="Delete Source" onClick={() => handleDeleteKnowledgeSource(source.source_id)}><Trash2 className="h-4 w-4 text-red-500 hover:text-red-700" /></Button>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}