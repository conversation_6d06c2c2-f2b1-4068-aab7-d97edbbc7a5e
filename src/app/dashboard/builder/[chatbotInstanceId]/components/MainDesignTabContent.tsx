'use client';

import React, { FormEvent } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Loader2 as SpinnerIcon } from 'lucide-react';
import { ChatbotUISettings } from '../page'; // Assuming types are exported from page.tsx or a types file

// Constants
const webSafeFonts = [
  { value: 'Arial, Helvetica, sans-serif', label: 'Arial' },
  { value: 'Verdana, Geneva, sans-serif', label: 'Verdana' },
  { value: 'Tahoma, Geneva, sans-serif', label: 'Tahoma' },
  { value: 'Trebuchet MS, Helvetica, sans-serif', label: 'Trebuchet MS' },
  { value: 'Georgia, serif', label: 'Georgia' },
  { value: 'Times New Roman, Times, serif', label: 'Times New Roman' },
  { value: 'Courier New, Courier, monospace', label: 'Courier New' },
  { value: 'Lucida Console, Monaco, monospace', label: 'Lucida Console' },
  { value: 'Inter, sans-serif', label: 'Inter' },
];

interface MainDesignTabContentProps {
  uiSettings: ChatbotUISettings;
  defaultUISettings: ChatbotUISettings;
  currentChatbotType: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
  isSavingDesign: boolean;
  handleUiSettingChange: (key: keyof ChatbotUISettings, value: string | number | boolean | undefined, isFinalValue?: boolean) => void;
  handleSaveDesign: (event: FormEvent) => Promise<void>;
}

export default function MainDesignTabContent({
  uiSettings,
  defaultUISettings,
  currentChatbotType,
  isSavingDesign,
  handleUiSettingChange,
  handleSaveDesign,
}: MainDesignTabContentProps) {
  return (
    <div className="border rounded-lg p-6 bg-card shadow-lg">
      <h2 className="text-xl font-semibold mb-4">Main Chat Design</h2>
      <form onSubmit={handleSaveDesign} className="space-y-6">
        {/* Colors & Opacity Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">Colors & Opacity</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
            <div className="col-span-1 sm:col-span-2">
              <div className="space-y-1">
                <Label htmlFor="headerBackgroundColor" className="text-sm">Header Background Color</Label>
                <Input type="color" id="headerBackgroundColor" value={uiSettings.headerBackgroundColor || defaultUISettings.headerBackgroundColor} onChange={(e) => handleUiSettingChange('headerBackgroundColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('headerBackgroundColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
              </div>
            </div>
            <div className="col-span-1 sm:col-span-2">
              <div className="space-y-1">
                <Label htmlFor="chatWindowBackgroundColor" className="text-sm">Chat Window BG Color</Label>
                <Input type="color" id="chatWindowBackgroundColor" value={uiSettings.chatWindowBackgroundColor || defaultUISettings.chatWindowBackgroundColor} onChange={(e) => handleUiSettingChange('chatWindowBackgroundColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('chatWindowBackgroundColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
              </div>
            </div>
            <div className="col-span-1 sm:col-span-2">
              <div className="space-y-1">
                <Label htmlFor="inputAreaBackgroundColor" className="text-sm">Input Area BG Color</Label>
                <Input type="color" id="inputAreaBackgroundColor" value={uiSettings.inputAreaBackgroundColor || defaultUISettings.inputAreaBackgroundColor} onChange={(e) => handleUiSettingChange('inputAreaBackgroundColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('inputAreaBackgroundColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
              </div>
            </div>
            {[
              { key: 'headerTextColor', label: 'Header Text Color' },
              { key: 'inputAreaTextColor', label: 'Input Area Text Color' },
              { key: 'sendButtonColor', label: 'Send Button BG Color' },
              { key: 'sendButtonIconColor', label: 'Send Button Icon Color' },
              { key: 'userBubbleBackgroundColor', label: 'User Bubble BG Color' },
              { key: 'userBubbleTextColor', label: 'User Bubble Text Color' },
              { key: 'agentBubbleBackgroundColor', label: 'Agent Bubble BG Color' },
              { key: 'agentBubbleTextColor', label: 'Agent Bubble Text Color' },
            ].map(({ key, label }) => (
              <div key={key} className="space-y-1 col-span-1">
                <Label htmlFor={key} className="text-sm">{label}</Label>
                <Input type="color" id={key} value={uiSettings[key as keyof ChatbotUISettings] as string || defaultUISettings[key as keyof ChatbotUISettings] as string || ''} onChange={(e) => handleUiSettingChange(key as keyof ChatbotUISettings, e.target.value, false)} onBlur={(e) => handleUiSettingChange(key as keyof ChatbotUISettings, e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
              </div>
            ))}
            <div className="space-y-1 col-span-1">
              <Label htmlFor="inputFieldBorderColor" className="text-sm">Input Border Color</Label>
              <Input type="color" id="inputFieldBorderColor" value={uiSettings.inputFieldBorderColor || defaultUISettings.inputFieldBorderColor} onChange={(e) => handleUiSettingChange('inputFieldBorderColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('inputFieldBorderColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
            </div>
            <div className="space-y-1 col-span-1">
              <Label htmlFor="inputFieldFocusBorderColor" className="text-sm">Input Focus Border Color</Label>
              <Input type="color" id="inputFieldFocusBorderColor" value={uiSettings.inputFieldFocusBorderColor || defaultUISettings.inputFieldFocusBorderColor} onChange={(e) => handleUiSettingChange('inputFieldFocusBorderColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('inputFieldFocusBorderColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
            </div>
          </div>
        </div>

        {/* Shape & Style Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">Shape & Style</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="windowBorderRadius" className="text-sm">Window Corner Radius: {uiSettings.windowBorderRadius ?? defaultUISettings.windowBorderRadius}px</Label>
              <Slider id="windowBorderRadius" min={0} max={30} step={1} value={[uiSettings.windowBorderRadius ?? defaultUISettings.windowBorderRadius!]} onValueChange={(value) => handleUiSettingChange('windowBorderRadius', value[0], false)} onValueCommit={(value) => handleUiSettingChange('windowBorderRadius', value[0], true)} />
            </div>
            <div>
              <Label htmlFor="bubbleBorderRadius" className="text-sm">Message Bubble Radius: {uiSettings.bubbleBorderRadius ?? defaultUISettings.bubbleBorderRadius}px</Label>
              <Slider id="bubbleBorderRadius" min={0} max={30} step={1} value={[uiSettings.bubbleBorderRadius ?? defaultUISettings.bubbleBorderRadius!]} onValueChange={(value) => handleUiSettingChange('bubbleBorderRadius', value[0], false)} onValueCommit={(value) => handleUiSettingChange('bubbleBorderRadius', value[0], true)} />
            </div>
            <div>
              <Label htmlFor="bubbleTailStyle" className="text-sm">Bubble Tail Style</Label>
              <Select value={uiSettings.bubbleTailStyle ?? defaultUISettings.bubbleTailStyle} onValueChange={(value) => handleUiSettingChange('bubbleTailStyle', value as any)}>
                <SelectTrigger id="bubbleTailStyle" className="mt-1"><SelectValue placeholder="Select tail style" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="TAILED">Tailed</SelectItem>
                  <SelectItem value="ROUNDED">Rounded (No Tail)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Header Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">Header</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="headerText" className="text-sm">Header Text</Label>
              <Input id="headerText" type="text" value={uiSettings.headerText || ''} onChange={(e) => handleUiSettingChange('headerText', e.target.value)} className="mt-1"/>
            </div>
            <div>
              <Label htmlFor="headerHeight" className="text-sm">Header Height: {uiSettings.headerHeight ?? defaultUISettings.headerHeight}px</Label>
              <Slider id="headerHeight" min={30} max={80} step={1} value={[uiSettings.headerHeight ?? defaultUISettings.headerHeight!]} onValueChange={(value) => handleUiSettingChange('headerHeight', value[0], false)} onValueCommit={(value) => handleUiSettingChange('headerHeight', value[0], true)} className="mt-1"/>
            </div>
          </div>
        </div>

        {/* Size Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">Size</h3>
          {currentChatbotType === 'FLOATING_WIDGET' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="floatingWidgetSize" className="text-sm">Widget Size Preset</Label>
                <Select value={uiSettings.floatingWidgetSize ?? defaultUISettings.floatingWidgetSize} onValueChange={(value) => handleUiSettingChange('floatingWidgetSize', value as any)}>
                  <SelectTrigger id="floatingWidgetSize"><SelectValue placeholder="Select size" /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SHORT_NARROW">Short Narrow (300x400px)</SelectItem>
                    <SelectItem value="TALL_NARROW">Tall Narrow (300x550px)</SelectItem>
                    <SelectItem value="SHORT_WIDE">Short Wide (400x400px)</SelectItem>
                    <SelectItem value="TALL_WIDE">Tall Wide (400x550px)</SelectItem>
                    <SelectItem value="CUSTOM">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {uiSettings.floatingWidgetSize === 'CUSTOM' && (
                <div className="grid grid-cols-2 gap-4 p-3 border rounded-md bg-muted/30">
                  <div>
                    <Label htmlFor="customFloatingWidth" className="text-sm">Custom Width (px)</Label>
                    <Input id="customFloatingWidth" type="number" value={uiSettings.customFloatingWidth ?? defaultUISettings.customFloatingWidth} onChange={(e) => handleUiSettingChange('customFloatingWidth', parseInt(e.target.value, 10))} placeholder="350" className="mt-1"/>
                  </div>
                  <div>
                    <Label htmlFor="customFloatingHeight" className="text-sm">Custom Height (px)</Label>
                    <Input id="customFloatingHeight" type="number" value={uiSettings.customFloatingHeight ?? defaultUISettings.customFloatingHeight} onChange={(e) => handleUiSettingChange('customFloatingHeight', parseInt(e.target.value, 10))} placeholder="550" className="mt-1"/>
                  </div>
                </div>
              )}
              <div className="space-y-2">
                <Label htmlFor="launcherPosition" className="text-sm">Launcher Position</Label>
                <Select value={uiSettings.launcherPosition ?? defaultUISettings.launcherPosition} onValueChange={(value) => handleUiSettingChange('launcherPosition', value as any)}>
                  <SelectTrigger id="launcherPosition"><SelectValue placeholder="Select position" /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BOTTOM_RIGHT">Bottom Right</SelectItem>
                    <SelectItem value="BOTTOM_LEFT">Bottom Left</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="launcherOffsetX" className="text-sm">Launcher Offset X (px)</Label>
                  <Input id="launcherOffsetX" type="number" value={uiSettings.launcherOffsetX ?? defaultUISettings.launcherOffsetX} onChange={(e) => handleUiSettingChange('launcherOffsetX', parseInt(e.target.value, 10))} placeholder="20" className="mt-1"/>
                </div>
                <div>
                  <Label htmlFor="launcherOffsetY" className="text-sm">Launcher Offset Y (px)</Label>
                  <Input id="launcherOffsetY" type="number" value={uiSettings.launcherOffsetY ?? defaultUISettings.launcherOffsetY} onChange={(e) => handleUiSettingChange('launcherOffsetY', parseInt(e.target.value, 10))} placeholder="20" className="mt-1"/>
                </div>
              </div>
            </div>
          )}
          {currentChatbotType === 'EMBEDDED_WINDOW' && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="embeddedWidth" className="text-sm">Width (e.g., 350px or 100%)</Label>
                <Input id="embeddedWidth" type="text" value={uiSettings.embeddedWidth || ''} onChange={(e) => handleUiSettingChange('embeddedWidth', e.target.value)} placeholder="350px" className="mt-1"/>
              </div>
              <div>
                <Label htmlFor="embeddedHeight" className="text-sm">Height (e.g., 500px or 80%)</Label>
                <Input id="embeddedHeight" type="text" value={uiSettings.embeddedHeight || ''} onChange={(e) => handleUiSettingChange('embeddedHeight', e.target.value)} placeholder="500px" className="mt-1"/>
              </div>
            </div>
          )}
        </div>

        {/* Fonts Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">Fonts</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="fontFamily" className="text-sm">Font Family</Label>
              <Select value={uiSettings.fontFamily || defaultUISettings.fontFamily} onValueChange={(value) => handleUiSettingChange('fontFamily', value)}>
                <SelectTrigger id="fontFamily" className="mt-1"><SelectValue placeholder="Select font family" /></SelectTrigger>
                <SelectContent>{webSafeFonts.map(font => (<SelectItem key={font.value} value={font.value}>{font.label}</SelectItem>))}</SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="headerFontSize" className="text-sm">Header Font Size: {uiSettings.headerFontSize ?? defaultUISettings.headerFontSize}px</Label>
                <Slider id="headerFontSize" min={12} max={24} step={1} value={[uiSettings.headerFontSize ?? defaultUISettings.headerFontSize!]} onValueChange={(value) => handleUiSettingChange('headerFontSize', value[0], false)} onValueCommit={(value) => handleUiSettingChange('headerFontSize', value[0], true)} className="mt-1"/>
              </div>
              <div>
                <Label htmlFor="bubbleFontSize" className="text-sm">Bubble Font Size: {uiSettings.bubbleFontSize ?? defaultUISettings.bubbleFontSize}px</Label>
                <Slider id="bubbleFontSize" min={10} max={18} step={1} value={[uiSettings.bubbleFontSize ?? defaultUISettings.bubbleFontSize!]} onValueChange={(value) => handleUiSettingChange('bubbleFontSize', value[0], false)} onValueCommit={(value) => handleUiSettingChange('bubbleFontSize', value[0], true)} className="mt-1"/>
              </div>
              <div>
                <Label htmlFor="inputFontSize" className="text-sm">Input Font Size: {uiSettings.inputFontSize ?? defaultUISettings.inputFontSize}px</Label>
                <Slider id="inputFontSize" min={10} max={18} step={1} value={[uiSettings.inputFontSize ?? defaultUISettings.inputFontSize!]} onValueChange={(value) => handleUiSettingChange('inputFontSize', value[0], false)} onValueCommit={(value) => handleUiSettingChange('inputFontSize', value[0], true)} className="mt-1"/>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Effects Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">🎨 Enhanced Effects</h3>
          <div className="space-y-4 p-3 border rounded-md bg-muted/20">
            <div>
              <Label htmlFor="chatWindowEffect" className="text-sm">Window Background Style</Label>
              <Select value={uiSettings.chatWindowEffect ?? defaultUISettings.chatWindowEffect} onValueChange={(value) => handleUiSettingChange('chatWindowEffect', value as any)}>
                <SelectTrigger id="chatWindowEffect" className="mt-1"><SelectValue placeholder="Select effect" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Solid Color</SelectItem>
                  <SelectItem value="frostedGlass">Frosted Glass</SelectItem>
                  <SelectItem value="floatingBubbles">Floating Bubbles (Transparent Window)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="pt-3">
              <Label className="text-sm font-medium">Automatic Gradients</Label>
              <div className="space-y-2 mt-1">
                <div className="flex items-center space-x-2">
                  <Checkbox id="enableHeaderGradient" checked={uiSettings.enableHeaderGradient ?? false} onCheckedChange={(checked) => handleUiSettingChange('enableHeaderGradient', !!checked)} />
                  <Label htmlFor="enableHeaderGradient" className="text-sm font-normal">Enable Header Gradient</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="enableUserBubbleGradient" checked={uiSettings.enableUserBubbleGradient ?? false} onCheckedChange={(checked) => handleUiSettingChange('enableUserBubbleGradient', !!checked)} />
                  <Label htmlFor="enableUserBubbleGradient" className="text-sm font-normal">Enable User Bubble Gradient</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="enableAgentBubbleGradient" checked={uiSettings.enableAgentBubbleGradient ?? false} onCheckedChange={(checked) => handleUiSettingChange('enableAgentBubbleGradient', !!checked)} />
                  <Label htmlFor="enableAgentBubbleGradient" className="text-sm font-normal">Enable Agent Bubble Gradient</Label>
                </div>
                {currentChatbotType === 'FLOATING_WIDGET' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="enableLauncherGradient" checked={uiSettings.enableLauncherGradient ?? false} onCheckedChange={(checked) => handleUiSettingChange('enableLauncherGradient', !!checked)} />
                    <Label htmlFor="enableLauncherGradient" className="text-sm font-normal">Enable Launcher Icon Gradient</Label>
                  </div>
                )}
              </div>
            </div>
            <div className="pt-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableSendButtonAnimation"
                  checked={uiSettings.enableSendButtonAnimation ?? defaultUISettings.enableSendButtonAnimation}
                  onCheckedChange={(checked) => handleUiSettingChange('enableSendButtonAnimation', !!checked)}
                />
                <Label htmlFor="enableSendButtonAnimation" className="text-sm font-normal">
                  Animate Send Button (Hover, Click, Pulse)
                </Label>
              </div>
            </div>
          </div>
        </div>

        {/* Animations Section */}
        <div>
          <h3 className="text-lg font-medium mb-3">Animations</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="widgetOpenAnimation" className="text-sm">Widget Open Animation</Label>
              <Select value={uiSettings.widgetOpenAnimation ?? defaultUISettings.widgetOpenAnimation} onValueChange={(value) => handleUiSettingChange('widgetOpenAnimation', value as any)}>
                <SelectTrigger id="widgetOpenAnimation" className="mt-1"><SelectValue placeholder="Select open animation" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="NONE">None</SelectItem>
                  <SelectItem value="FADE_IN">Fade In</SelectItem>
                  <SelectItem value="SLIDE_UP_FAST">Slide Up</SelectItem>
                  <SelectItem value="SLIDE_IN_RIGHT_FAST">Slide In From Right</SelectItem>
                  <SelectItem value="SCALE_UP_CENTER">Scale Up</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="messageEntranceAnimation" className="text-sm">Message Entrance Animation</Label>
              <Select value={uiSettings.messageEntranceAnimation ?? defaultUISettings.messageEntranceAnimation} onValueChange={(value) => handleUiSettingChange('messageEntranceAnimation', value as any)}>
                <SelectTrigger id="messageEntranceAnimation" className="mt-1"><SelectValue placeholder="Select message animation" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="NONE">None</SelectItem>
                  <SelectItem value="FADE_IN">Fade In</SelectItem>
                  <SelectItem value="SLIDE_UP_MESSAGE">Slide Up</SelectItem>
                  <SelectItem value="SCALE_IN_MESSAGE">Scale In</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="typingIndicatorStyle" className="text-sm">Typing Indicator Style</Label>
              <Select value={uiSettings.typingIndicatorStyle ?? defaultUISettings.typingIndicatorStyle} onValueChange={(value) => handleUiSettingChange('typingIndicatorStyle', value as any)}>
                <SelectTrigger id="typingIndicatorStyle" className="mt-1"><SelectValue placeholder="Select typing indicator" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOTS">Dots</SelectItem>
                  <SelectItem value="WAVE">Wave</SelectItem>
                  <SelectItem value="TEXT">"Typing..." Text</SelectItem>
                  <SelectItem value="NONE">None</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={isSavingDesign}>
            {isSavingDesign ? <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isSavingDesign ? 'Saving...' : 'Save Design'}
          </Button>
        </div>
      </form>
    </div>
  );
}