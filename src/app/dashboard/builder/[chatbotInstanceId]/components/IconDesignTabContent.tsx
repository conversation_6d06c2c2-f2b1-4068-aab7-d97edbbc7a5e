'use client';

import React, { FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 as SpinnerIcon } from 'lucide-react'; // Removed icon imports, they'll come from page.tsx via props
import { ChatbotUISettings } from '../page';

// Type for predefinedIcons prop
interface PredefinedIcon {
  id: string;
  label: string;
  svg: React.ReactElement;
}

interface IconDesignTabContentProps {
  predefinedIcons: PredefinedIcon[]; // Added prop
  uiSettings: ChatbotUISettings;
  defaultUISettings: ChatbotUISettings;
  isSavingDesign: boolean;
  handleUiSettingChange: (key: keyof ChatbotUISettings, value: string | number | boolean | undefined, isFinalValue?: boolean) => void;
  handleSaveDesign: (event: FormEvent) => Promise<void>;
}

export default function IconDesignTabContent({
  predefinedIcons, // Added prop
  uiSettings,
  defaultUISettings,
  isSavingDesign,
  handleUiSettingChange,
  handleSaveDesign,
}: IconDesignTabContentProps) {
  return (
    <div className="border rounded-lg p-6 bg-card shadow-lg">
      <h2 className="text-xl font-semibold mb-4">Start Up Icon Design</h2>
      <form onSubmit={handleSaveDesign} className="space-y-6">
        <div>
          <Label className="text-base font-medium">Icon Selection</Label>
          <Select 
            value={uiSettings.startupIconType ?? defaultUISettings.startupIconType} 
            onValueChange={(value) => handleUiSettingChange('startupIconType', value as any)} 
          >
            <SelectTrigger className="mt-2"><SelectValue placeholder="Select icon type" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="PREDEFINED">Predefined Icons</SelectItem>
              <SelectItem value="CUSTOM_URL">Custom Image URL</SelectItem>
              <SelectItem value="UPLOADED" disabled>Custom Upload (Soon)</SelectItem>
            </SelectContent>
          </Select>
          
          {uiSettings.startupIconType === 'PREDEFINED' && (
            <div className="mt-3">
              <Label htmlFor="predefinedStartupIcon" className="text-sm">Choose an Icon</Label>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3 mt-1 p-2 border rounded-md">
                {predefinedIcons.map(icon => (
                  <button 
                    key={icon.id} 
                    type="button" 
                    onClick={() => handleUiSettingChange('startupIcon', icon.id)} 
                    className={`p-2 border rounded-md flex items-center justify-center hover:bg-accent ${uiSettings.startupIcon === icon.id ? 'ring-2 ring-primary bg-accent' : ''}`} 
                    title={icon.label}
                  >
                    {React.cloneElement(icon.svg, { style: { color: uiSettings.startupIconColor || defaultUISettings.startupIconColor }, className:"w-8 h-8" })}
                  </button>
                ))}
              </div>
            </div>
          )}
          {uiSettings.startupIconType === 'CUSTOM_URL' && (
            <div className="mt-3">
              <Label htmlFor="customStartupIconUrl" className="text-sm">Custom Icon URL</Label>
              <Input 
                id="customStartupIconUrl" 
                type="url" 
                value={uiSettings.startupIcon || ''} 
                onChange={(e) => handleUiSettingChange('startupIcon', e.target.value)} 
                placeholder="https://example.com/icon.svg" 
                className="mt-1"
              />
            </div>
          )}
        </div>
        <div>
          <h3 className="text-lg font-medium mb-3">Icon Colors</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startupIconColor" className="text-sm">Icon Color</Label>
              <Input type="color" id="startupIconColor" value={uiSettings.startupIconColor ?? defaultUISettings.startupIconColor!} onChange={(e) => handleUiSettingChange('startupIconColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('startupIconColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
            </div>
            <div>
              <Label htmlFor="startupIconBackgroundColor" className="text-sm">Icon Background Color</Label>
              <Input type="color" id="startupIconBackgroundColor" value={uiSettings.startupIconBackgroundColor ?? defaultUISettings.startupIconBackgroundColor!} onChange={(e) => handleUiSettingChange('startupIconBackgroundColor', e.target.value, false)} onBlur={(e) => handleUiSettingChange('startupIconBackgroundColor', e.target.value, true)} className="w-full h-10 p-1 border rounded-md cursor-pointer"/>
            </div>
          </div>
        </div>
        <div>
          <h3 className="text-lg font-medium mb-3">Icon Animations</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startupIconHoverAnimation" className="text-sm">On Hover Animation</Label>
              <Select value={uiSettings.startupIconHoverAnimation ?? defaultUISettings.startupIconHoverAnimation} onValueChange={(value) => handleUiSettingChange('startupIconHoverAnimation', value as any)}>
                <SelectTrigger id="startupIconHoverAnimation" className="mt-1"><SelectValue placeholder="Select hover animation" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="NONE">None</SelectItem>
                  <SelectItem value="SCALE">Scale</SelectItem>
                  <SelectItem value="GLOW">Glow</SelectItem>
                  <SelectItem value="ROTATE_SLIGHTLY">Rotate Slightly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="startupIconNotificationAnimation" className="text-sm">New Message Animation</Label>
              <Select value={uiSettings.startupIconNotificationAnimation ?? defaultUISettings.startupIconNotificationAnimation} onValueChange={(value) => handleUiSettingChange('startupIconNotificationAnimation', value as any)}>
                <SelectTrigger id="startupIconNotificationAnimation" className="mt-1"><SelectValue placeholder="Select notification animation" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="NONE">None</SelectItem>
                  <SelectItem value="PULSE">Pulse</SelectItem>
                  <SelectItem value="WIGGLE">Wiggle</SelectItem>
                  <SelectItem value="BADGE">Notification Badge</SelectItem>
                  <SelectItem value="SHAKE">Shake</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={isSavingDesign}>
            {isSavingDesign ? <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isSavingDesign ? 'Saving...' : 'Save Icon Design'}
          </Button>
        </div>
      </form>
    </div>
  );
}