'use client';

import React, { FormEvent } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Info, Loader2 as SpinnerIcon } from 'lucide-react';

// Constants (mirrored from page.tsx for now, consider moving to a shared constants file later)
const PRIMARY_FUNCTIONS = [
  { id: "LEAD_GENERATION", label: "Lead Generation - Capture prospect information, qualify leads" },
  { id: "APPOINTMENT_BOOKING", label: "Appointment Booking - Schedule meetings, consultations, demos" },
  { id: "CUSTOMER_SUPPORT", label: "Customer Support - Answer FAQs, troubleshoot, escalate issues" },
  { id: "SALES_ASSISTANCE", label: "Sales Assistance - Product recommendations, pricing, purchase guidance" },
] as const;

const SECONDARY_FUNCTIONS = [
  { id: "TRAINING_ONBOARDING", label: "Training/Onboarding - Help new users/employees learn processes" },
  { id: "HR_SUPPORT", label: "HR Support - Internal employee questions, policies" },
  { id: "INFORMATION_COLLECTION", label: "Information Collection - Surveys, feedback, data gathering" },
  { id: "CUSTOM_INTEGRATION", label: "Custom Integration - Specific business workflow needs" },
] as const;

const TONE_PREFERENCES = [
  { id: "PROFESSIONAL", label: "Professional" },
  { id: "FRIENDLY", label: "Friendly" },
  { id: "CASUAL", label: "Casual" },
  { id: "HUMOROUS", label: "Humorous" },
] as const;

interface SetupTabContentProps {
  primaryAgentGoals: string[];
  secondaryAgentGoals: string[];
  businessContext: string;
  customGoalDetail: string;
  agentPersonaName: string;
  companyName: string;
  industry: string;
  targetAudience: string;
  tonePreference: string;
  isSavingSetup: boolean;
  currentChatbotType: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';

  handleGoalCheckboxChange: (goalId: string, goalType: 'primary' | 'secondary', isChecked: boolean) => void;
  setCustomGoalDetail: (value: string) => void;
  setBusinessContext: (value: string) => void;
  setAgentPersonaName: (value: string) => void;
  setCompanyName: (value: string) => void;
  setIndustry: (value: string) => void;
  setTargetAudience: (value: string) => void;
  setTonePreference: (value: string) => void;
  handleSaveSetup: (event: FormEvent) => Promise<void>;
}

export default function SetupTabContent({
  primaryAgentGoals,
  secondaryAgentGoals,
  businessContext,
  customGoalDetail,
  agentPersonaName,
  companyName,
  industry,
  targetAudience,
  tonePreference,
  isSavingSetup,
  currentChatbotType,
  handleGoalCheckboxChange,
  setCustomGoalDetail,
  setBusinessContext,
  setAgentPersonaName,
  setCompanyName,
  setIndustry,
  setTargetAudience,
  setTonePreference,
  handleSaveSetup,
}: SetupTabContentProps) {
  return (
    <div className="border rounded-lg p-6 bg-card shadow-lg">
      <h2 className="text-xl font-semibold mb-4">Setup Chatbot</h2>
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <p className="text-sm text-blue-700 flex items-center">
          <Info className="w-4 h-4 mr-2 shrink-0"/>
          Chatbot Type: <span className="font-semibold ml-1">{currentChatbotType === 'FLOATING_WIDGET' ? 'Floating Widget' : 'Embedded Window'}</span>
        </p>
      </div>
      <form onSubmit={handleSaveSetup} className="space-y-8">
        {/* Primary Functions */}
        <div>
          <Label className="text-base font-medium block mb-3">Primary Functions (Select all that apply)</Label>
          <div className="space-y-2">
            {PRIMARY_FUNCTIONS.map((func) => (
              <div key={func.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`primary-${func.id}`}
                  checked={primaryAgentGoals.includes(func.id)}
                  onCheckedChange={(checked: boolean) => {
                    handleGoalCheckboxChange(func.id, 'primary', checked);
                  }}
                />
                <Label htmlFor={`primary-${func.id}`} className="font-normal text-sm">{func.label}</Label>
              </div>
            ))}
          </div>
        </div>

        {/* Secondary Functions */}
        <div>
          <Label className="text-base font-medium block mb-3">Secondary Functions (Optional)</Label>
          <div className="space-y-2">
            {SECONDARY_FUNCTIONS.map((func) => (
              <div key={func.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`secondary-${func.id}`}
                  checked={secondaryAgentGoals.includes(func.id)}
                  onCheckedChange={(checked: boolean) => {
                    handleGoalCheckboxChange(func.id, 'secondary', checked);
                  }}
                />
                <Label htmlFor={`secondary-${func.id}`} className="font-normal text-sm">{func.label}</Label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Custom Goal Detail */}
        <div>
          <Label htmlFor="customGoalDetail" className="text-base font-medium">Additional Instructions / Custom Goal Details</Label>
          <Textarea 
            id="customGoalDetail" 
            value={customGoalDetail} 
            onChange={(e) => setCustomGoalDetail(e.target.value)} 
            placeholder="If you selected a custom goal, or have specific instructions for any goal, describe them here..." 
            className="mt-2 min-h-[80px]" 
            rows={3}
          />
        </div>

        {/* Business Context */}
        <div>
          <Label htmlFor="businessContext" className="text-base font-medium">Tell Us A Little About Your Business</Label>
          <Textarea 
            id="businessContext" 
            value={businessContext} 
            onChange={(e) => setBusinessContext(e.target.value)} 
            placeholder="Describe your business, its products/services, and target audience..." 
            className="mt-2 min-h-[100px]" 
            rows={4}
          />
        </div>

        {/* Agent Persona Name and Company Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="agentPersonaName" className="text-base font-medium">Agent Persona Name</Label>
            <Input
              id="agentPersonaName"
              value={agentPersonaName}
              onChange={(e) => setAgentPersonaName(e.target.value)}
              placeholder="e.g., Marketing Max, Support Sally"
              className="mt-2"
            />
          </div>
          <div>
            <Label htmlFor="companyName" className="text-base font-medium">Company Name</Label>
            <Input
              id="companyName"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              placeholder="e.g., Acme Corp, Your Company Inc."
              className="mt-2"
            />
          </div>
        </div>

        {/* Industry, Target Audience, Tone Preference */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Label htmlFor="industry" className="text-base font-medium">Industry / Business Type</Label>
            <Input 
              id="industry" 
              value={industry} 
              onChange={(e) => setIndustry(e.target.value)} 
              placeholder="e.g., E-commerce, SaaS, Healthcare" 
              className="mt-2"
            />
          </div>
          <div>
            <Label htmlFor="targetAudience" className="text-base font-medium">Target Audience</Label>
            <Input 
              id="targetAudience" 
              value={targetAudience} 
              onChange={(e) => setTargetAudience(e.target.value)} 
              placeholder="e.g., B2B, Developers, Young Adults" 
              className="mt-2"
            />
          </div>
          <div>
            <Label htmlFor="tonePreference" className="text-base font-medium">Tone Preference</Label>
            <Select value={tonePreference} onValueChange={(value) => setTonePreference(value)}>
              <SelectTrigger id="tonePreference" className="mt-2">
                <SelectValue placeholder="Select a tone" />
              </SelectTrigger>
              <SelectContent>
                {TONE_PREFERENCES.map(tone => (
                  <SelectItem key={tone.id} value={tone.id}>{tone.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button type="submit" disabled={isSavingSetup}>
            {isSavingSetup ? <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isSavingSetup ? 'Saving...' : 'Save Setup'}
          </Button>
        </div>
      </form>
    </div>
  );
}