'use client';

import React, {
  useState,
  useEffect,
  useCallback,
  FormEvent,
  ChangeEvent,
  useRef, // Added useRef import
  // useMemo, // Not used in this component, but kept
} from 'react';
import Script from 'next/script';
import { useParams } from 'next/navigation';

// UI Components
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { toast } from 'sonner';

// Icons
import {
  Image as ImageIcon,
  // Palette, // Referenced in commented out predefinedIcons, but not used directly
  Zap,
  Link2,
  UploadCloud,
  Trash2,
  Eye,
  Info,
  MessageCircle,
  RefreshCw,
  SendHorizonal,
  CheckCircle,
  Edit3,
  Loader2 as SpinnerIcon,
  Copy,
  Bot,
  MessageSquareText,
  SparklesIcon
} from 'lucide-react';

// Custom Modals & Hooks
import ViewFileContentModal from '@/components/ViewFileContentModal';
import ViewWebsitePagesModal from '@/components/ViewWebsitePagesModal';
import EmbedSnippetModal from '@/components/EmbedSnippetModal';
import SetupTabContent from './components/SetupTabContent';
import MainDesignTabContent from './components/MainDesignTabContent';
import IconDesignTabContent from './components/IconDesignTabContent';
import KnowledgeTabContent from './components/KnowledgeTabContent'; // Import the new component
// import { useChat, type Message as VercelAIChatMessage } from 'ai/react'; // Used in commented out InteractiveLivePreview

// Type Definitions
interface ChatbotInstanceData {
  id: string;
  name: string;
  type: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  centralConfigId: string;
  primaryAgentGoals?: string[];
  secondaryAgentGoals?: string[];
  businessContext?: string;
  customGoalDetail?: string;
  agentPersonaName?: string;
  companyName?: string;
  uiSettings?: ChatbotUISettings;
  knowledgeSources?: KnowledgeSource[];
  industry?: string;
  targetAudience?: string;
  tonePreference?: string;
  processLeads?: boolean; // New field
  leadSummaryPreference?: 'all_chats' | 'pressing_issues'; // New field
  deliveryMethod?: 'email' | 'webhook'; // New field
  recipientEmail?: string; // New field
  webhookUrl?: string; // New field
}

export interface ChatbotUISettings { // Added export
  // --- Colors ---
  headerBackgroundColor?: string;
  headerTextColor?: string;
  chatWindowBackgroundColor?: string;
  inputAreaBackgroundColor?: string;
  inputAreaTextColor?: string;
  sendButtonColor?: string;
  sendButtonIconColor?: string;
  userBubbleBackgroundColor?: string;
  userBubbleTextColor?: string;
  agentBubbleBackgroundColor?: string;
  agentBubbleTextColor?: string;

  // --- Shape & Borders ---
  windowBorderRadius?: number; // px
  bubbleBorderRadius?: number; // px

  // --- Header ---
  headerText?: string;
  headerHeight?: number; // px

  // --- Sizing ---
  chatbotType?: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
  floatingWidgetSize?: 'SHORT_NARROW' | 'TALL_NARROW' | 'SHORT_WIDE' | 'TALL_WIDE' | 'CUSTOM';
  customFloatingWidth?: number; // px
  customFloatingHeight?: number; // px
  launcherPosition?: 'BOTTOM_RIGHT' | 'BOTTOM_LEFT';
  launcherOffsetX?: number; // px
  launcherOffsetY?: number; // px
  embeddedWidth?: string;
  embeddedHeight?: string;
  useRef?: React.RefObject<HTMLDivElement>; // Added useRef type definition

  // --- Fonts ---
  fontFamily?: string;
  headerFontSize?: number; // px
  bubbleFontSize?: number; // px
  inputFontSize?: number; // px

  // --- Animations ---
  widgetOpenAnimation?: 'NONE' | 'FADE_IN' | 'SLIDE_UP_FAST' | 'SLIDE_IN_RIGHT_FAST' | 'SCALE_UP_CENTER';
  messageEntranceAnimation?: 'NONE' | 'FADE_IN' | 'SLIDE_UP_MESSAGE' | 'SCALE_IN_MESSAGE';
  typingIndicatorStyle?: 'DOTS' | 'WAVE' | 'TEXT' | 'NONE';

  // --- Startup Icon (for Floating Widget) ---
  startupIconType?: 'PREDEFINED' | 'CUSTOM_URL' | 'UPLOADED';
  startupIcon?: string;
  startupIconColor?: string;
  startupIconBackgroundColor?: string;
  startupIconHoverAnimation?: 'NONE' | 'SCALE' | 'GLOW' | 'ROTATE_SLIGHTLY';
  startupIconNotificationAnimation?: 'NONE' | 'PULSE' | 'WIGGLE' | 'BADGE' | 'SHAKE';

  // New properties for input border and bubble tails
  inputFieldBorderColor?: string;
  inputFieldFocusBorderColor?: string;
  bubbleTailStyle?: 'TAILED' | 'ROUNDED';
  chatWindowEffect?: 'none' | 'frostedGlass' | 'floatingBubbles';
  // Gradient settings
  enableHeaderGradient?: boolean;
  enableUserBubbleGradient?: boolean;
  enableAgentBubbleGradient?: boolean;
  enableLauncherGradient?: boolean;
  enableSendButtonAnimation?: boolean;
}

export interface KnowledgeSource { // Added export
  source_id: string;
  chatbot_id: string;
  name: string;
  type: 'WEBSITE' | 'FILE' | 'TEXT' | 'SINGLE_PAGE';
  location_uri?: string;
  status:
    | 'pending' | 'uploaded' | 'crawling_submitted' | 'processing' | 'active'
    | 'error' | 'processing_error' | 'unsupported_file_type' | 'empty_content'
    | 'chunking_failed' | 'processing_error_pdf' | 'processing_error_extract'
    | 'processing_error_unknown' | 'crawl_failed'
    | 'crawling' | 'scraping' | 'scraping (Firecrawl job in progress)' | 'processing_crawl_data'
    | 'completed' | 'failed';
  metadata_json?: Record<string, any>;
  created_at?: string;
  last_updated?: string;
}

// Constants
const PRIMARY_FUNCTIONS = [
  { id: "LEAD_GENERATION", label: "Lead Generation - Capture prospect information, qualify leads" },
  { id: "APPOINTMENT_BOOKING", label: "Appointment Booking - Schedule meetings, consultations, demos" },
  { id: "CUSTOMER_SUPPORT", label: "Customer Support - Answer FAQs, troubleshoot, escalate issues" },
  { id: "SALES_ASSISTANCE", label: "Sales Assistance - Product recommendations, pricing, purchase guidance" },
] as const;

const SECONDARY_FUNCTIONS = [
  { id: "TRAINING_ONBOARDING", label: "Training/Onboarding - Help new users/employees learn processes" },
  { id: "HR_SUPPORT", label: "HR Support - Internal employee questions, policies" },
  { id: "INFORMATION_COLLECTION", label: "Information Collection - Surveys, feedback, data gathering" },
  { id: "CUSTOM_INTEGRATION", label: "Custom Integration - Specific business workflow needs" },
] as const;

const TONE_PREFERENCES = [
  { id: "PROFESSIONAL", label: "Professional" },
  { id: "FRIENDLY", label: "Friendly" },
  { id: "CASUAL", label: "Casual" },
  { id: "HUMOROUS", label: "Humorous" },
] as const;

const defaultUISettings: ChatbotUISettings = {
  headerBackgroundColor: '#32324E',
  headerTextColor: '#FFFFFF',
  chatWindowBackgroundColor: '#28283C',
  inputAreaBackgroundColor: '#28283C',
  inputAreaTextColor: '#FFFFFF',
  sendButtonColor: '#AFFF3C',
  sendButtonIconColor: '#000000',
  userBubbleBackgroundColor: '#8A2BE2',
  userBubbleTextColor: '#FFFFFF',
  agentBubbleBackgroundColor: '#AFFF3C',
  agentBubbleTextColor: '#000000',
  windowBorderRadius: 15,
  bubbleBorderRadius: 18,
  headerText: 'AIQ Assistant',
  headerHeight: 60,
  chatbotType: 'FLOATING_WIDGET',
  floatingWidgetSize: 'TALL_NARROW',
  customFloatingWidth: 350,
  customFloatingHeight: 550,
  launcherPosition: 'BOTTOM_RIGHT',
  launcherOffsetX: 20,
  launcherOffsetY: 20,
  embeddedWidth: '100%',
  embeddedHeight: '600px',
  fontFamily: 'Inter, sans-serif',
  headerFontSize: 18,
  bubbleFontSize: 14,
  inputFontSize: 14,
  widgetOpenAnimation: 'SLIDE_UP_FAST',
  messageEntranceAnimation: 'FADE_IN',
  typingIndicatorStyle: 'DOTS',
  startupIconType: 'PREDEFINED',
  startupIcon: 'default_chat_icon',
  startupIconColor: '#FFFFFF',
  startupIconBackgroundColor: '#8A2BE2',
  startupIconHoverAnimation: 'SCALE',
  startupIconNotificationAnimation: 'PULSE',
  inputFieldBorderColor: "#CCCCCC",
  inputFieldFocusBorderColor: "#00FF00",
  bubbleTailStyle: "TAILED",
  chatWindowEffect: "none",
  enableHeaderGradient: false,
  enableUserBubbleGradient: false,
  enableAgentBubbleGradient: false,
  enableLauncherGradient: false,
  enableSendButtonAnimation: false,
};

// This constant is needed for the Startup Icon Preview section directly in this page component
const predefinedIcons = [
  { id: 'default_chat_icon', label: 'Default Chat Bubble', svg: <MessageCircle /> },
  { id: 'zap_icon', label: 'Lightning Bolt', svg: <Zap /> },
  { id: 'bot_icon', label: 'Robot Icon', svg: <Bot /> },
  { id: 'message_square_icon', label: 'Square Message', svg: <MessageSquareText /> },
  { id: 'sparkles_icon', label: 'Sparkles Icon', svg: <SparklesIcon /> },
];

const userFriendlyStatusMap: Record<string, string> = {
  'pending': 'Pending', 'uploaded': 'Uploaded', 'crawling_submitted': 'Queued for Crawl',
  'processing': 'Processing', 'active': 'Active', 'error': 'Error',
  'processing_error': 'Processing Error', 'unsupported_file_type': 'Unsupported File',
  'empty_content': 'Empty Content', 'chunking_failed': 'Processing Error (Chunking)',
  'processing_error_pdf': 'PDF Error', 'processing_error_extract': 'Extraction Error',
  'processing_error_unknown': 'Unknown Error', 'crawl_failed': 'Crawl Failed',
  'crawling': 'Gathering Data', 'scraping': 'Gathering Data',
  'scraping (firecrawl job in progress)': 'Gathering Data',
  'crawling (firecrawl job in progress)': 'Gathering Data',
  'processing_crawl_data': 'Processing Data', 'completed': 'Finalizing',
  'completed (source status: processing_crawl_data)': 'Processing Data',
  'completed (source status: active)': 'Active', 'failed': 'Failed',
};

// Helper Functions
const getDisplayStatus = (status: string | null | undefined): string => {
  if (!status) return 'N/A';
  const lowerStatus = status.toLowerCase();
  const mappedStatus = userFriendlyStatusMap[lowerStatus];
  return mappedStatus || status;
};

// Global Window Interface Extension
declare global {
  interface Window {
    reinitializeAiqChatWidget?: (settings: ChatbotUISettings) => void;
  }
}

// Commented out InteractiveLivePreview component (as in original)
/*
const InteractiveLivePreview: React.FC<{
  centralConfigId: string;
  uiSettings: ChatbotUISettings;
  defaultUISettings: ChatbotUISettings;
  chatbotType: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
  chatbotName?: string;
  previewWidth: string;
  previewTotalHeight: string;
}> = ({ centralConfigId, uiSettings, defaultUISettings, chatbotType, chatbotName, previewWidth, previewTotalHeight }) => {
  const { messages, input, handleInputChange, handleSubmit, isLoading: isChatLoading, error: chatError, setMessages } = useChat({
    api: '/api/chat-test',
    body: { data: { centralConfigId } },
    initialMessages: [
      { id: 'initial-agent-greeting', role: 'assistant', content: 'Hi there! How can I help you today?' }
    ],
    onError: (error) => {
      toast.error(`Chat Error: ${error.message}`);
    }
  });

  const handleResetConversation = () => {
    setMessages([{ id: 'reset-agent-greeting', role: 'assistant', content: 'Hi there! How can I help you today?' }]);
  };

  return (
    <div
      className="flex flex-col mx-auto shadow-md flex-grow"
      style={{
        width: previewWidth,
        height: previewTotalHeight,
        borderRadius: `${uiSettings.windowBorderRadius || defaultUISettings.windowBorderRadius}px`,
        overflow: 'hidden',
        backgroundColor: uiSettings.chatWindowBackgroundColor || defaultUISettings.chatWindowBackgroundColor,
        fontFamily: uiSettings.fontFamily || defaultUISettings.fontFamily,
      }}
    >
      <div
        className="flex items-center justify-between flex-shrink-0 px-4"
        style={{
          height: `${uiSettings.headerHeight || defaultUISettings.headerHeight}px`,
          backgroundColor: uiSettings.headerBackgroundColor || defaultUISettings.headerBackgroundColor,
          color: uiSettings.headerTextColor || defaultUISettings.headerTextColor,
        }}
      >
        <p
          className="font-semibold truncate text-center w-full"
          style={{ fontSize: `${uiSettings.headerFontSize || defaultUISettings.headerFontSize}px` }}
        >
          {uiSettings.headerText || chatbotName || defaultUISettings.headerText}
        </p>
        <Button variant="ghost" size="icon" onClick={handleResetConversation} title="Reset Conversation"
          style={{ color: uiSettings.headerTextColor || defaultUISettings.headerTextColor }}
          className="hover:bg-white/20"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
      <div
        className="flex-grow p-4 space-y-3 overflow-y-auto custom-chat-preview-scrollbar"
        style={{ paddingBottom: '10px' }}
      >
        {messages.map((m: VercelAIChatMessage) => (
          <div
            key={m.id}
            className={`p-3 max-w-[80%] break-words ${m.role === 'user' ? 'self-end ml-auto' : 'self-start mr-auto'}`}
            style={{
              backgroundColor: m.role === 'user'
                ? (uiSettings.userBubbleBackgroundColor || defaultUISettings.userBubbleBackgroundColor)
                : (uiSettings.agentBubbleBackgroundColor || defaultUISettings.agentBubbleBackgroundColor),
              color: m.role === 'user'
                ? (uiSettings.userBubbleTextColor || defaultUISettings.userBubbleTextColor)
                : (uiSettings.agentBubbleTextColor || defaultUISettings.agentBubbleTextColor),
              borderRadius: `${uiSettings.bubbleBorderRadius || defaultUISettings.bubbleBorderRadius}px`,
              borderBottomLeftRadius: m.role === 'user' ? undefined : '5px',
              borderBottomRightRadius: m.role === 'user' ? '5px' : undefined,
              fontSize: `${uiSettings.bubbleFontSize || defaultUISettings.bubbleFontSize}px`,
            }}
          >
            {m.content}
          </div>
        ))}
        {isChatLoading && <div className="self-start text-xs text-muted-foreground italic px-3">Agent is thinking...</div>}
        {chatError && <div className="p-3 max-w-[80%] break-words self-start mr-auto bg-red-100 text-red-700 rounded-lg" style={{borderRadius: `${uiSettings.bubbleBorderRadius || defaultUISettings.bubbleBorderRadius}px`}}>Error: {chatError.message}</div>}
      </div>
      <form onSubmit={handleSubmit} className="flex items-center p-3 flex-shrink-0"
        style={{
          backgroundColor: uiSettings.inputAreaBackgroundColor || defaultUISettings.inputAreaBackgroundColor,
          borderTop: '1px solid rgba(100, 100, 100, 0.2)',
        }}
      >
        <Input
          value={input}
          onChange={handleInputChange}
          placeholder="Write a message..."
          className="flex-grow mr-2"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            color: uiSettings.inputAreaTextColor || defaultUISettings.inputAreaTextColor,
            borderRadius: '20px',
            fontSize: `${uiSettings.inputFontSize || defaultUISettings.inputFontSize}px`,
            borderColor: 'rgba(255,255,255,0.2)',
          }}
          disabled={isChatLoading}
        />
        <Button
          type="submit" size="icon" className="rounded-full w-10 h-10"
          style={{
            backgroundColor: uiSettings.sendButtonColor || defaultUISettings.sendButtonColor,
          }}
          disabled={isChatLoading}
        >
          <SendHorizonal
            className="h-5 w-5"
            style={{ color: uiSettings.sendButtonIconColor || defaultUISettings.sendButtonIconColor }}
          />
        </Button>
      </form>
    </div>
  );
};
*/

// Main Page Component
export default function ChatbotBuilderPage() {
  const params = useParams();
  const chatbotInstanceId = params.chatbotInstanceId as string;

  // Component State
  const [chatbotData, setChatbotData] = useState<ChatbotInstanceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Setup Tab State
  const [primaryAgentGoals, setPrimaryAgentGoals] = useState<string[]>([]);
  const [secondaryAgentGoals, setSecondaryAgentGoals] = useState<string[]>([]);
  const [businessContext, setBusinessContext] = useState('');
  const [customGoalDetail, setCustomGoalDetail] = useState('');
  const [agentPersonaName, setAgentPersonaName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [industry, setIndustry] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [tonePreference, setTonePreference] = useState('');
  const [isSavingSetup, setIsSavingSetup] = useState(false);

  // Design Tab State
  const [uiSettings, setUiSettings] = useState<ChatbotUISettings>(defaultUISettings);
  const [isSavingDesign, setIsSavingDesign] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isPreviewScriptLoaded, setIsPreviewScriptLoaded] = useState(false);

  // Knowledge Tab State
  const [scrapeUrl, setScrapeUrl] = useState('');
  const [scrapeType, setScrapeType] = useState<'FULL_SITE' | 'SINGLE_PAGE'>('FULL_SITE');
  const [isScraping, setIsScraping] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [knowledgeSources, setKnowledgeSources] = useState<KnowledgeSource[]>([]);


  // Assistant Agent / Connections Tab State
  const [assistantConfig, setAssistantConfig] = useState({
    process_leads: false,
    lead_summary_preference: 'all_chats' as 'all_chats' | 'pressing_issues', // New field
    delivery_method: 'email' as 'email' | 'webhook',
    recipient_email: '',
    webhook_url: ''
  });
  const [isSavingAssistant, setIsSavingAssistant] = useState(false);

  // Modal State
  const [isViewFileModalOpen, setIsViewFileModalOpen] = useState(false);
  const [selectedFileSource, setSelectedFileSource] = useState<KnowledgeSource | null>(null);
  const [isViewWebsitePagesModalOpen, setIsViewWebsitePagesModalOpen] = useState(false);
  const [selectedWebsiteSource, setSelectedWebsiteSource] = useState<KnowledgeSource | null>(null);
  const [isSnippetModalOpen, setIsSnippetModalOpen] = useState(false);

  // Data Fetching and Initialization
  const fetchChatbotData = useCallback(async (isRefresh: boolean = false) => {
    if (!chatbotInstanceId) {
      if (!isRefresh) setIsLoading(false);
      setError("Chatbot ID is missing.");
      return;
    }
    if (!isRefresh) {
        setError(null);
        setIsLoading(true);
    }

    try {
      const response = await fetch(`/api/chatbot-instances/${chatbotInstanceId}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to fetch chatbot data (status: ${response.status})`);
      }
      const initialData: ChatbotInstanceData = await response.json();
      setChatbotData(initialData);

      // Populate setup form fields
      setPrimaryAgentGoals(initialData.primaryAgentGoals || []);
      setSecondaryAgentGoals(initialData.secondaryAgentGoals || []);
      setCustomGoalDetail(initialData.customGoalDetail || '');
      setAgentPersonaName(initialData.agentPersonaName || '');
      setCompanyName(initialData.companyName || '');
      setIndustry(initialData.industry || '');
      setTargetAudience(initialData.targetAudience || '');
      setTonePreference(initialData.tonePreference || '');
      setBusinessContext(initialData.businessContext || '');

      const loadedUiSettings = { ...defaultUISettings, ...(initialData.uiSettings || {}) };
      setUiSettings(loadedUiSettings); // This will trigger the new useEffect for reinitialization

      // Populate assistant configuration fields
      setAssistantConfig({
        process_leads: initialData.processLeads || false,
        lead_summary_preference: initialData.leadSummaryPreference || 'all_chats',
        delivery_method: initialData.deliveryMethod || 'email',
        recipient_email: initialData.recipientEmail || '',
        webhook_url: initialData.webhookUrl || ''
      });

      // Fetch knowledge sources
      const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
      if (centralApiUrl && initialData.id) {
        try {
          const ksResponse = await fetch(`${centralApiUrl}/api/v1/knowledge/sources?chatbot_id=${initialData.id}`);
          if (ksResponse.ok) {
            const ksData: KnowledgeSource[] = await ksResponse.json();
            setKnowledgeSources(ksData);
            if (isRefresh) toast.success("Knowledge sources refreshed!");
          } else {
            const errorText = await ksResponse.text();
            toast.error(`Failed to load knowledge sources: ${ksResponse.status}. ${errorText.substring(0, 100)}`);
          }
        } catch (ksError) {
          toast.error("Error loading knowledge sources.");
          console.error("KS Fetch Error:", ksError);
        }
      } else if (!centralApiUrl) {
        toast.error("Central API URL is not configured. Cannot load knowledge sources.");
      }
    } catch (error: any) { // Explicitly type error as 'any' or 'unknown'
      console.error(error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred fetching main data');
      if (isRefresh) toast.error(`Refresh failed: ${(error as Error).message}`);
    } finally {
      if (!isRefresh) setIsLoading(false);
    }
  }, [chatbotInstanceId, setAssistantConfig]); // chatbotInstanceId is the main dependency

  useEffect(() => {
    fetchChatbotData(false); // Initial fetch
  }, [fetchChatbotData]); // fetchChatbotData depends on chatbotInstanceId

  // New useEffect to handle preview re-initialization with polling
  useEffect(() => {
    if (typeof window === 'undefined' || !chatbotData?.id || !uiSettings) {
      return;
    }

    // Only proceed if the script tag is in the DOM (meaning an attempt to load it has been made)
    // and the preview isn't already marked as loaded.
    if (document.getElementById('aiq-preview-script') && !isPreviewScriptLoaded) {
      let attempts = 0;
      const maxAttempts = 15; // Poll for 3 seconds (15 * 200ms)
      const pollInterval = 200; // ms

      console.log("Builder: Starting polling for reinitializeAiqChatWidget...");

      const intervalId = setInterval(() => {
        attempts++;
        if (window.reinitializeAiqChatWidget) {
          clearInterval(intervalId);
          console.log(`Builder: reinitializeAiqChatWidget found after ${attempts} attempts. Initializing preview.`);
          const currentLoadedUiSettings = { ...defaultUISettings, ...(chatbotData.uiSettings || {}), ...uiSettings };
          window.reinitializeAiqChatWidget(currentLoadedUiSettings);
          setIsPreviewScriptLoaded(true);
          console.log("Builder: Preview reinitialized and isPreviewScriptLoaded set to true.");
        } else if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          console.warn(`Builder: reinitializeAiqChatWidget not found after ${maxAttempts} attempts. Preview initialization failed.`);
          toast.error("Preview widget could not be initialized. Try refreshing the page.");
          setIsPreviewScriptLoaded(false); // Ensure it's false if we failed
        } else {
          console.log(`Builder: Polling for reinitializeAiqChatWidget, attempt ${attempts}`);
        }
      }, pollInterval);

      return () => clearInterval(intervalId); // Cleanup on unmount or re-run
    } else if (isPreviewScriptLoaded && window.reinitializeAiqChatWidget) {
      // If already loaded and function exists (e.g., uiSettings changed), reinitialize directly
      console.log("Builder: Preview script already loaded, reinitializing due to settings change.");
      const currentLoadedUiSettings = { ...defaultUISettings, ...(chatbotData.uiSettings || {}), ...uiSettings };
      window.reinitializeAiqChatWidget(currentLoadedUiSettings);
    }
  }, [chatbotData?.id, uiSettings, chatbotInstanceId, useRef]); // Added useRef to dependencies


  // Effect for dynamically loading the preview script once
  const scriptRef = useRef<HTMLScriptElement | null>(null);
  
  useEffect(() => {
    if (!chatbotData?.id || typeof window === 'undefined') {
      return;
    }

    // Only create the script if it doesn't exist and we haven't attempted to load it
    if (scriptRef.current) {
      if (window.reinitializeAiqChatWidget) {
        // Script is already loaded and ready
        setIsPreviewScriptLoaded(true);
        window.reinitializeAiqChatWidget({ ...defaultUISettings, ...(chatbotData.uiSettings || {}), ...uiSettings });
      }
      return;
    }

    const scriptElement = document.createElement('script');
    scriptElement.id = 'aiq-preview-script';
    scriptElement.src = `/embed/v1/aiq-chat-widget-preview.js`;
    scriptElement.defer = true;
    
    scriptElement.onload = () => {
      console.log("Builder: aiq-preview-script loaded successfully");
      scriptRef.current = scriptElement;
      setIsPreviewScriptLoaded(true);
      if (window.reinitializeAiqChatWidget) {
        window.reinitializeAiqChatWidget({ ...defaultUISettings, ...(chatbotData.uiSettings || {}), ...uiSettings });
      }
    };
    
    scriptElement.onerror = (e) => {
      console.error("Builder: Error loading aiq-preview-script:", e);
      toast.error("Failed to load chat preview script. Please refresh the page.");
      setIsPreviewScriptLoaded(false);
    };

    // Append to body instead of container to prevent duplicate loading
    document.body.appendChild(scriptElement);
    console.log("Builder: Appended aiq-preview-script to document body");

    // Clean up script on component unmount
    return () => {
      if (scriptRef.current) {
        document.body.removeChild(scriptRef.current);
        scriptRef.current = null;
        setIsPreviewScriptLoaded(false);
      }
    };
  }, [chatbotData?.id, uiSettings]); // Depend on chatbotData ID and UI settings


  // Effect for polling knowledge source status
  useEffect(() => {
    const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
    if (!centralApiUrl || !chatbotInstanceId || knowledgeSources.length === 0) return;

    const pollableStatuses: KnowledgeSource['status'][] = [
        'crawling_submitted', 'pending', 'crawling', 
        'scraping (Firecrawl job in progress)', 'processing_crawl_data'
    ];
    const sourcesToPoll = knowledgeSources.filter(s => {
      if (!s.status || !s.metadata_json?.firecrawl_job_id) return false;
      let statusToCheck = s.status as string;
      const match = statusToCheck.match(/^completed \(Source status: (.*)\)$/);
      if (match && match[1]) statusToCheck = match[1];
      return pollableStatuses.includes(statusToCheck as KnowledgeSource['status']);
    });

    if (sourcesToPoll.length === 0) return;

    const intervalIds: NodeJS.Timeout[] = [];
    sourcesToPoll.forEach(source => {
      const firecrawlJobId = source.metadata_json?.firecrawl_job_id;
      if (!firecrawlJobId) return;

      let isPolling = true;
      const pollJobStatus = async () => {
        if (!isPolling) return;
        try {
          const response = await fetch(`${centralApiUrl}/api/v1/knowledge/crawl-jobs/${firecrawlJobId}`);
          if (!response.ok) {
            toast.error(`Error checking status for ${source.name}. Polling stopped.`);
            isPolling = false;
            return;
          }
          const jobStatusData = await response.json();
          let firecrawlInternalStatus = jobStatusData.status;
          let actualSourceStatus = firecrawlInternalStatus;
          const match = firecrawlInternalStatus?.match(/^completed \(Source status: (.*)\)$/);
          if (match && match[1]) actualSourceStatus = match[1];

          const terminalStatuses: KnowledgeSource['status'][] = [
            'active', 'error', 'processing_error', 'unsupported_file_type', 
            'empty_content', 'chunking_failed', 'processing_error_pdf', 
            'processing_error_extract', 'processing_error_unknown', 'crawl_failed'
          ];
          
          if (actualSourceStatus && terminalStatuses.includes(actualSourceStatus)) {
            isPolling = false;
            setKnowledgeSources(prev => prev.map(s_item => s_item.source_id === source.source_id ? { ...s_item, status: actualSourceStatus as KnowledgeSource['status'] } : s_item));
            toast.info(`Job for "${source.name}" status: ${getDisplayStatus(actualSourceStatus)}. Refreshing list soon.`);
            
            setTimeout(async () => { // Refresh the whole list after a short delay
              if (!chatbotInstanceId) return;
              try {
                const ksResponse = await fetch(`${centralApiUrl}/api/v1/knowledge/sources?chatbot_id=${chatbotInstanceId}`);
                if (ksResponse.ok) {
                  const ksData: KnowledgeSource[] = await ksResponse.json();
                  setKnowledgeSources(ksData);
                  const targetSourceInFetchedData = ksData.find(s => s.source_id === source.source_id);
                  toast.success(`Knowledge list updated. ${source.name} status: ${getDisplayStatus(targetSourceInFetchedData?.status || 'N/A')}.`);
                } else { 
                  toast.error(`Failed to refresh knowledge sources: ${ksResponse.status}`);
                }
              } catch (e) { 
                toast.error("Error during knowledge source refresh."); 
              }
            }, 3000);
          } else if (firecrawlInternalStatus && source.status !== firecrawlInternalStatus) {
            setKnowledgeSources(prev => prev.map(s_item => s_item.source_id === source.source_id ? { ...s_item, status: firecrawlInternalStatus as KnowledgeSource['status'] } : s_item));
          } else if (!firecrawlInternalStatus) {
            console.warn(`Polling for job ${firecrawlJobId} encountered an unexpected null/undefined status. Stopping poll.`);
            isPolling = false;
          }
        } catch (err) {
          toast.error(`Polling error for ${source.name}.`);
          isPolling = false;
        }
      };

      pollJobStatus(); // Initial check
      const intervalId = setInterval(() => {
        if (isPolling) pollJobStatus();
        else clearInterval(intervalId);
      }, 15000); // Poll every 15 seconds
      intervalIds.push(intervalId);
    });

    return () => { // Cleanup function
      intervalIds.forEach(id => clearInterval(id));
    };
  }, [knowledgeSources, chatbotInstanceId]); // Re-run if knowledgeSources or chatbotInstanceId changes

  // Event Handlers
  const handleGoalCheckboxChange = (
    goalId: string,
    goalType: 'primary' | 'secondary',
    isChecked: boolean
  ) => {
    const setter = goalType === 'primary' ? setPrimaryAgentGoals : setSecondaryAgentGoals;
    if (isChecked) {
      setter(prevGoals => [...prevGoals, goalId]);
    } else {
      setter(prevGoals => prevGoals.filter(g => g !== goalId));
    }
  };

  const handleSaveSetup = async (event: FormEvent) => {
    event.preventDefault();
    setIsSavingSetup(true);
    const payload = {
      primaryAgentGoals, secondaryAgentGoals, businessContext, customGoalDetail,
      agentPersonaName, companyName, industry, targetAudience, tonePreference
    };
    try {
      const response = await fetch(`/api/chatbot-instances/${chatbotInstanceId}`, {
        method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload),
      });
      if (!response.ok) throw new Error((await response.json()).message || `Failed to save setup`);
      const updatedData = await response.json();
      toast.success('Setup information saved!');
      setChatbotData(updatedData); // Update local chatbot data
      // Re-populate state from response to ensure consistency if backend modifies data
      setPrimaryAgentGoals(updatedData.primaryAgentGoals || []);
      setSecondaryAgentGoals(updatedData.secondaryAgentGoals || []);
    } catch (e) {
      toast.error((e as Error).message);
    } finally {
      setIsSavingSetup(false);
    }
  };

  const handleUiSettingChange = (
    key: keyof ChatbotUISettings,
    value: string | number | boolean | undefined,
    isFinalValue: boolean = true // To avoid too many re-renders for sliders
  ) => {
    const newSettings = { ...uiSettings, [key]: value };
    setUiSettings(newSettings);
    if (isFinalValue && window.reinitializeAiqChatWidget) {
      window.reinitializeAiqChatWidget(newSettings);
    }
  };

  const handleSaveDesign = async (event: FormEvent) => {
    event.preventDefault();
    setIsSavingDesign(true);
    try {
      const response = await fetch(`/api/chatbot-instances/${chatbotInstanceId}`, {
        method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ uiSettings }),
      });
      if (!response.ok) throw new Error((await response.json()).message || `Failed to save design`);
      const updatedInstance = await response.json();
      toast.success('Design settings saved!');
      setChatbotData(prev => prev ? {...prev, uiSettings: updatedInstance.uiSettings } : null);
      setUiSettings(updatedInstance.uiSettings || defaultUISettings); // Ensure UI settings are updated from response
    } catch (e) {
      toast.error((e as Error).message);
    } finally {
      setIsSavingDesign(false);
    }
  };

  const handleScrapeWebsite = async () => {
    if (!scrapeUrl.trim()) { toast.error("Please enter a URL."); return; }
    if (!chatbotInstanceId) { toast.error("Chatbot ID missing."); return; }
    setIsScraping(true);
    toast.info(`Initiating ${scrapeType === 'SINGLE_PAGE' ? 'page' : 'site'} scrape: ${scrapeUrl}`);
    const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
    if (!centralApiUrl) { toast.error("API URL missing."); setIsScraping(false); return; }
    
    const payload = { 
      chatbot_id: chatbotInstanceId, 
      name: scrapeUrl, 
      type: scrapeType === 'SINGLE_PAGE' ? 'single_page' : 'website', 
      location_uri: scrapeUrl, 
      metadata_json: { scrape_type: scrapeType }
    };

    try {
      const createRes = await fetch(`${centralApiUrl}/api/v1/knowledge/sources`, { 
        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) 
      });
      if (!createRes.ok) throw new Error((await createRes.json()).message || `Failed to create source`);
      const createdSrc: KnowledgeSource = await createRes.json();
      toast.success(`Source "${scrapeUrl}" created. Starting process...`);
      setKnowledgeSources(prev => [...prev, { ...createdSrc }]);
      setScrapeUrl(''); // Clear input

      const processRes = await fetch(`${centralApiUrl}/api/v1/knowledge/sources/${createdSrc.source_id}/process`, { method: 'POST' });
      if (!processRes.ok) {
        setKnowledgeSources(prev => prev.map(s => s.source_id === createdSrc.source_id ? { ...s, status: 'error' } : s));
        throw new Error((await processRes.json()).message || `Failed to start processing`);
      }
      const procResult = await processRes.json();
      toast.success(`Processing for "${createdSrc.name}" started. Status: ${getDisplayStatus(procResult.status)}.`);
      setKnowledgeSources(prev => prev.map(s => s.source_id === createdSrc.source_id ? { ...s, status: (procResult.status || 'crawling_submitted'), metadata_json: {...s.metadata_json, firecrawl_job_id: procResult.firecrawl_job_id } } : s ));
    } catch (e) {
      toast.error((e as Error).message);
    } finally {
      setIsScraping(false);
    }
  };

  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setUploadedFile(event.target.files[0]);
    }
  };

  const handleProcessUpload = async () => {
    if (!uploadedFile) { toast.error("Please select a file."); return; }
    if (!chatbotInstanceId) { toast.error("Chatbot ID missing."); return; }
    setIsUploading(true); 
    toast.info(`Uploading: ${uploadedFile.name}...`);
    
    const formData = new FormData();
    formData.append('chatbot_id', chatbotInstanceId);
    formData.append('file', uploadedFile);

    try {
      const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
      if (!centralApiUrl) { toast.error("API URL missing."); setIsUploading(false); return; }

      const uploadRes = await fetch(`${centralApiUrl}/api/v1/knowledge/sources/upload-file`, { method: 'POST', body: formData });
      if (!uploadRes.ok) throw new Error((await uploadRes.json()).message || `Upload failed`);
      const createdSrc: KnowledgeSource = await uploadRes.json();
      toast.success(`"${createdSrc.name}" uploaded. Status: ${getDisplayStatus(createdSrc.status)}. Processing...`);
      setKnowledgeSources(prev => [...prev, { ...createdSrc }]);
      setUploadedFile(null); // Clear selection
      const fileInput = document.getElementById('knowledgeFile') as HTMLInputElement; 
      if (fileInput) fileInput.value = ''; // Reset file input

      const processRes = await fetch(`${centralApiUrl}/api/v1/knowledge/sources/${createdSrc.source_id}/process`, { method: 'POST' });
      if (!processRes.ok) {
        setKnowledgeSources(prev => prev.map(s => s.source_id === createdSrc.source_id ? {...s, status: 'error'} : s));
        throw new Error((await processRes.json()).message || `Processing failed to start`);
      }
      const procResult = await processRes.json();
      toast.success(`Processing for "${createdSrc.name}" started. Status: ${getDisplayStatus(procResult.status)}.`);
      setKnowledgeSources(prev => prev.map(s => s.source_id === createdSrc.source_id ? {...s, status: (procResult.status || 'processing') } : s));
    } catch (e) {
      toast.error((e as Error).message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteKnowledgeSource = async (sourceId: string) => {
    const sourceName = knowledgeSources.find(s => s.source_id === sourceId)?.name || sourceId;
    toast.info(`Deleting: ${sourceName}...`);
    const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
    if (!centralApiUrl) { toast.error("API URL missing."); return; }
    try {
      const response = await fetch(`${centralApiUrl}/api/v1/knowledge/sources/${sourceId}`, { method: 'DELETE' });
      if (!response.ok) throw new Error((await response.json()).detail || `Failed to delete`);
      setKnowledgeSources(prev => prev.filter(s => s.source_id !== sourceId));
      toast.success(`"${sourceName}" deleted.`);
    } catch (e) {
      toast.error((e as Error).message);
    }
  };

  const handlePublish = async () => {
    if (!chatbotData) return;
    setIsPublishing(true);
    const newStatus = chatbotData.status === 'PUBLISHED' ? 'DRAFT' : 'PUBLISHED';
    try {
      const response = await fetch(`/api/chatbot-instances/${chatbotInstanceId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${newStatus === 'PUBLISHED' ? 'publish' : 'unpublish'} chatbot`);
      }
      const updatedData: ChatbotInstanceData = await response.json();
      setChatbotData(updatedData);
      toast.success(`Chatbot successfully ${newStatus === 'PUBLISHED' ? 'published' : 'unpublished'}!`);
    } catch (e) {
      toast.error((e as Error).message);
    } finally {
      setIsPublishing(false);
    }
  };


  const handleSaveAssistantConfig = async () => {
    setIsSavingAssistant(true);

    const payload = {
      processLeads: assistantConfig.process_leads,
      leadSummaryPreference: assistantConfig.lead_summary_preference,
      deliveryMethod: assistantConfig.delivery_method,
      recipientEmail: assistantConfig.recipient_email,
      webhookUrl: assistantConfig.webhook_url
    };

    try {
      const response = await fetch(`/api/chatbot-instances/${chatbotInstanceId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save assistant configuration');
      }

      const updatedData = await response.json();
      setChatbotData(updatedData); // Update local chatbot data
      toast.success('Assistant configuration saved successfully!');
    } catch (e) {
      toast.error((e as Error).message);
    } finally {
      setIsSavingAssistant(false);
    }
  };


  // Conditional Rendering for Loading/Error States
  if (isLoading) return <div className="container mx-auto p-4 text-center">Loading chatbot builder...</div>;
  if (error) return <div className="container mx-auto p-4 text-center text-red-500">Error: {error}</div>;
  if (!chatbotData) return <div className="container mx-auto p-4 text-center">Chatbot not found.</div>;

  // Derived values (calculated after chatbotData is confirmed)
  const currentChatbotType = chatbotData.type || 'FLOATING_WIDGET';
  const previewWidth = currentChatbotType === 'EMBEDDED_WINDOW'
    ? (uiSettings.embeddedWidth || defaultUISettings.embeddedWidth)
    : (uiSettings.floatingWidgetSize === 'SHORT_NARROW' || uiSettings.floatingWidgetSize === 'TALL_NARROW' ? '300px' : '400px');
  const previewTotalHeight = currentChatbotType === 'EMBEDDED_WINDOW'
    ? (uiSettings.embeddedHeight || defaultUISettings.embeddedHeight)
    : (uiSettings.floatingWidgetSize === 'SHORT_NARROW' || uiSettings.floatingWidgetSize === 'SHORT_WIDE' ? '400px' : '550px');

  // Main JSX
  return (
    <div className="container mx-auto p-4 md:p-6">
      <style jsx global>{`
        .preview-launcher-hover-scale { animation: previewLauncherScaleAnimation 0.3s ease forwards; }
        @keyframes previewLauncherScaleAnimation { from { transform: scale(1); } to { transform: scale(1.15); } }
        
        .preview-launcher-hover-glow { animation: previewLauncherGlowAnimation 1.5s infinite alternate; }
        @keyframes previewLauncherGlowAnimation {
          from { box-shadow: 0 0 5px ${uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor}, 0 0 10px ${uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor}; }
          to { box-shadow: 0 0 20px ${uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor}, 0 0 30px ${uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor}; }
        }

        .preview-launcher-hover-rotate-slightly { animation: previewLauncherRotateAnimation 0.5s ease-in-out; }
        @keyframes previewLauncherRotateAnimation {
          0% { transform: rotate(0deg); }
          50% { transform: rotate(10deg); }
          100% { transform: rotate(0deg); }
        }

        .preview-launcher-notify-pulse { animation: previewLauncherPulseAnimation 1.2s infinite cubic-bezier(0.66, 0, 0, 1); }
        @keyframes previewLauncherPulseAnimation {
          to { box-shadow: 0 0 0 18px rgba(${uiSettings.startupIconBackgroundColor ? ((c: string) => { const r = parseInt(c.slice(1,3),16), g = parseInt(c.slice(3,5),16), b = parseInt(c.slice(5,7),16); return `${r},${g},${b}`; })(uiSettings.startupIconBackgroundColor) : '138,43,226'}, 0); }
        }

        .preview-launcher-notify-wiggle { animation: previewLauncherWiggleAnimation 0.8s ease-in-out infinite; }
        @keyframes previewLauncherWiggleAnimation {
          0%, 100% { transform: translateX(0) rotate(0); }
          25% { transform: translateX(-3px) rotate(-3deg); }
          75% { transform: translateX(3px) rotate(3deg); }
        }
        
        .preview-launcher-notify-shake { animation: previewLauncherShakeAnimation 0.5s cubic-bezier(.36,.07,.19,.97) both infinite; }
        @keyframes previewLauncherShakeAnimation {
          10%, 90% { transform: translate3d(-1px, 0, 0); }
          20%, 80% { transform: translate3d(2px, 0, 0); }
          30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
          40%, 60% { transform: translate3d(3px, 0, 0); }
        }

        .preview-notification-badge {
          position: absolute;
          top: -5px;
          right: -5px;
          background-color: red;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
        }
      `}</style>

      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold">Build: {chatbotData.name}</h1>
        {chatbotData.status && (
          <span className={`px-3 py-1 text-xs font-semibold rounded-full ${
            chatbotData.status === 'PUBLISHED' ? 'bg-green-100 text-green-700' :
            chatbotData.status === 'DRAFT' ? 'bg-yellow-100 text-yellow-700' :
            'bg-gray-100 text-gray-700'
          }`}>
            Status: {chatbotData.status.charAt(0).toUpperCase() + chatbotData.status.slice(1).toLowerCase()}
          </span>
        )}
      </div>

      {/* Main Tabs Layout */}
      <Tabs defaultValue="setup" className="w-full">
        <TabsList className="grid w-full grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-1 mb-4">
          <TabsTrigger value="setup" className="px-2 py-1 text-xs sm:text-sm">Setup</TabsTrigger>
          <TabsTrigger value="mainDesign" className="px-2 py-1 text-xs sm:text-sm">Design</TabsTrigger>
          {currentChatbotType === 'FLOATING_WIDGET' && (
            <TabsTrigger value="iconDesign" className="px-2 py-1 text-xs sm:text-sm">Icon</TabsTrigger>
          )}
          <TabsTrigger value="knowledge" className="px-2 py-1 text-xs sm:text-sm">Knowledge</TabsTrigger>
          <TabsTrigger value="integrations" className="px-2 py-1 text-xs sm:text-sm">Integrations</TabsTrigger>
          <TabsTrigger value="notifications" className="px-2 py-1 text-xs sm:text-sm">Notifications</TabsTrigger>
        </TabsList>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Left Column: Tab Content */}
          <div className="lg:w-2/3">
            {/* Setup Tab */}
            <TabsContent value="setup">
              <SetupTabContent
                primaryAgentGoals={primaryAgentGoals}
                secondaryAgentGoals={secondaryAgentGoals}
                businessContext={businessContext}
                customGoalDetail={customGoalDetail}
                agentPersonaName={agentPersonaName}
                companyName={companyName}
                industry={industry}
                targetAudience={targetAudience}
                tonePreference={tonePreference}
                isSavingSetup={isSavingSetup}
                currentChatbotType={currentChatbotType}
                handleGoalCheckboxChange={handleGoalCheckboxChange}
                setCustomGoalDetail={setCustomGoalDetail}
                setBusinessContext={setBusinessContext}
                setAgentPersonaName={setAgentPersonaName}
                setCompanyName={setCompanyName}
                setIndustry={setIndustry}
                setTargetAudience={setTargetAudience}
                setTonePreference={setTonePreference}
                handleSaveSetup={handleSaveSetup}
              />
            </TabsContent>

            {/* Main Design Tab */}
            <TabsContent value="mainDesign">
              <MainDesignTabContent
                uiSettings={uiSettings}
                defaultUISettings={defaultUISettings}
                currentChatbotType={currentChatbotType}
                isSavingDesign={isSavingDesign}
                handleUiSettingChange={handleUiSettingChange}
                handleSaveDesign={handleSaveDesign}
              />
            </TabsContent>
            
            {/* Icon Design Tab (Conditional) */}
            {currentChatbotType === 'FLOATING_WIDGET' && (
              <TabsContent value="iconDesign">
                <IconDesignTabContent
                  predefinedIcons={predefinedIcons}
                  uiSettings={uiSettings}
                  defaultUISettings={defaultUISettings}
                  isSavingDesign={isSavingDesign}
                  handleUiSettingChange={handleUiSettingChange}
                  handleSaveDesign={handleSaveDesign}
                />
              </TabsContent>
            )}

            {/* Knowledge Tab */}
            <TabsContent value="knowledge">
              <KnowledgeTabContent
                scrapeUrl={scrapeUrl}
                setScrapeUrl={setScrapeUrl}
                scrapeType={scrapeType}
                setScrapeType={setScrapeType}
                isScraping={isScraping}
                uploadedFile={uploadedFile}
                isUploading={isUploading}
                knowledgeSources={knowledgeSources}
                handleScrapeWebsite={handleScrapeWebsite}
                handleFileUpload={handleFileUpload}
                handleProcessUpload={handleProcessUpload}
                handleDeleteKnowledgeSource={handleDeleteKnowledgeSource}
                fetchChatbotData={fetchChatbotData}
                setSelectedFileSource={setSelectedFileSource}
                setIsViewFileModalOpen={setIsViewFileModalOpen}
                setSelectedWebsiteSource={setSelectedWebsiteSource}
                setIsViewWebsitePagesModalOpen={setIsViewWebsitePagesModalOpen}
              />
            </TabsContent>

            {/* Connections Tab */}
            <TabsContent value="integrations">
              <div className="border rounded-lg p-6 bg-card shadow-lg">
                <h2 className="text-xl font-semibold mb-4">Integrations</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Shopify Card */}
                  <div className="border rounded-lg p-4 flex flex-col items-center">
                    <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16 flex items-center justify-center mb-2">
                      <span className="text-xs text-center">Shopify Icon</span>
                    </div>
                    <h3 className="text-lg font-medium mb-2">Shopify</h3>
                    <p className="text-muted-foreground text-center mb-4">Connect your Shopify store to sync products and orders.</p>
                    <Button>Connect</Button>
                  </div>
                  {/* WordPress Card */}
                  <div className="border rounded-lg p-4 flex flex-col items-center">
                    <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16 flex items-center justify-center mb-2">
                      <span className="text-xs text-center">WordPress Icon</span>
                    </div>
                    <h3 className="text-lg font-medium mb-2">WordPress</h3>
                    <p className="text-muted-foreground text-center mb-4">Connect your WordPress site to sync content and users.</p>
                    <Button>Connect</Button>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="notifications">
              <div className="border rounded-lg p-6 bg-card shadow-lg">
                <h2 className="text-xl font-semibold mb-4">Notifications</h2>
                <p className="text-muted-foreground mb-6">
                  Configure how you receive notifications and lead information from the assistant agent.
                </p>
                
                <div className="space-y-8"> {/* Increased spacing between major sections */}
                  
                  {/* Master switch for processing leads/notifications */}
                  <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="process_leads_master"
                          checked={assistantConfig.process_leads}
                          onCheckedChange={(checked) =>
                            setAssistantConfig(prev => ({ ...prev, process_leads: checked as boolean }))
                          }
                        />
                        <Label htmlFor="process_leads_master" className="text-md font-medium">
                          Enable Post-Chat Processing by Assistant
                        </Label>
                      </div>
                      <p className="text-sm text-muted-foreground ml-6">
                        If enabled, the assistant will analyze completed chats based on the settings below.
                      </p>
                  </div>

                  {/* Email Address for Notifications */}
                  <div className="space-y-2">
                    <Label htmlFor="recipient_email_notifications" className="text-lg font-medium block">
                      Email address for notifications
                    </Label>
                    <Input
                      id="recipient_email_notifications"
                      type="email"
                      placeholder="<EMAIL>"
                      value={assistantConfig.recipient_email}
                      onChange={(e) =>
                        setAssistantConfig(prev => ({ ...prev, recipient_email: e.target.value }))
                      }
                      className="max-w-md"
                      disabled={!assistantConfig.process_leads} // Disable if master switch is off
                    />
                     <p className="text-xs text-muted-foreground">
                      This email will be used if "By email" is selected below.
                    </p>
                  </div>

                  {/* How would you like your chat leads sent? */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">How would you like your chat leads sent?</h3>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="delivery_method_email"
                          name="delivery_method_group"
                          value="email"
                          checked={assistantConfig.delivery_method === 'email'}
                          onChange={(e) =>
                            setAssistantConfig(prev => ({ ...prev, delivery_method: e.target.value as 'email' | 'webhook' }))
                          }
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          disabled={!assistantConfig.process_leads}
                        />
                        <Label htmlFor="delivery_method_email" className="text-sm font-medium">
                          By email
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="delivery_method_webhook"
                          name="delivery_method_group"
                          value="webhook"
                          checked={assistantConfig.delivery_method === 'webhook'}
                          onChange={(e) =>
                            setAssistantConfig(prev => ({ ...prev, delivery_method: e.target.value as 'email' | 'webhook' }))
                          }
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          disabled={!assistantConfig.process_leads}
                        />
                        <Label htmlFor="delivery_method_webhook" className="text-sm font-medium">
                          By Webhook
                        </Label>
                      </div>
                    </div>
                    
                    {assistantConfig.delivery_method === 'webhook' && assistantConfig.process_leads && (
                      <div className="ml-6 space-y-2">
                        <Label htmlFor="webhook_url_leads" className="text-sm">
                          Webhook URL
                        </Label>
                        <Input
                          id="webhook_url_leads"
                          type="url"
                          placeholder="https://your-webhook-endpoint.com/webhook"
                          value={assistantConfig.webhook_url}
                          onChange={(e) =>
                            setAssistantConfig(prev => ({ ...prev, webhook_url: e.target.value }))
                          }
                          className="max-w-md"
                        />
                        <p className="text-xs text-muted-foreground">
                          This URL will receive POST requests with the assistant's analysis data.
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Notification Options For Chat History */}
                  {assistantConfig.process_leads && ( // This whole section is conditional on process_leads
                    <div className="space-y-4 border-t pt-6">
                      <h3 className="text-lg font-medium">Notification Options For Chat History</h3>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="summary_pref_all_chats"
                            name="lead_summary_preference_group"
                            value="all_chats"
                            checked={assistantConfig.lead_summary_preference === 'all_chats'}
                            onChange={(e) =>
                              setAssistantConfig(prev => ({ ...prev, lead_summary_preference: e.target.value as 'all_chats' | 'pressing_issues' }))
                            }
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <Label htmlFor="summary_pref_all_chats" className="text-sm font-medium">
                            Full Summaries Of Every Chat
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="summary_pref_pressing_issues"
                            name="lead_summary_preference_group"
                            value="pressing_issues"
                            checked={assistantConfig.lead_summary_preference === 'pressing_issues'}
                            onChange={(e) =>
                              setAssistantConfig(prev => ({ ...prev, lead_summary_preference: e.target.value as 'all_chats' | 'pressing_issues' }))
                            }
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <Label htmlFor="summary_pref_pressing_issues" className="text-sm font-medium">
                            Just Customer Issues And Negative Sentiment
                          </Label>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Save Button */}
                  <div className="border-t pt-8"> {/* Increased pt for more space */}
                    <Button
                      onClick={handleSaveAssistantConfig}
                      disabled={isSavingAssistant}
                      className="flex items-center space-x-2"
                    >
                      {isSavingAssistant ? (
                        <>
                          <SpinnerIcon className="w-4 h-4 animate-spin" />
                          <span>Saving...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4" />
                          <span>Save Assistant Configuration</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div> {/* Closes space-y-6 */}
              </div> {/* Closes border rounded-lg p-6 bg-card shadow-lg */}
            </TabsContent>
          </div> {/* End of Left Column (lg:w-2/3) */}

          {/* Right Column: Live Preview Pane & Actions */}
          {/* Make this entire column sticky and allow its content to scroll if it overflows viewport height */}
          {/* Using max-h-[calc(100vh-Xrem)] where Xrem is approx header height. Adjust 'top-6' and 'max-h' as needed. */}
          <div className="lg:w-1/3 space-y-6 lg:sticky lg:top-6 max-h-[calc(100vh-4rem)] overflow-y-auto"> {/* Assuming header is roughly 4rem (64px) */}
            <div id="live-preview-box" className="border rounded-lg p-6 bg-card shadow-lg min-h-[700px] flex flex-col z-30"> {/* REMOVED sticky top-6 */}
              <h2 className="text-xl font-semibold mb-3">Live & Interactive Preview</h2>
              
              {/* Container for the aiq-chat-widget.js script */}
              <div className="flex-grow flex flex-col justify-center items-center">
                <div
                  id="aiq-chatbot-container"
                  data-chatbot-id={chatbotData?.id || chatbotInstanceId}
                  className="w-full flex justify-center items-center"
                  style={{ minHeight: previewTotalHeight || '500px', backgroundColor: 'transparent' }}
                >
                  <div id="aiq-chatbot-widget-actual-container" className="w-full max-w-full flex justify-center items-center" style={{ height: previewTotalHeight }}>
                    {!isPreviewScriptLoaded && (
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        <SpinnerIcon className="w-6 h-6 mr-2 animate-spin" />
                        Loading Preview...
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {currentChatbotType === 'FLOATING_WIDGET' && (
                <div className="mt-auto pt-4 text-center">
                  <h4 className="text-md font-medium mb-2">Startup Icon Preview</h4>
                  <div
                    id="startup-icon-preview-interactive"
                    className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg mx-auto relative"
                    style={
                      uiSettings.enableLauncherGradient
                      ? {
                          backgroundImage: `linear-gradient(135deg, ${uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor!}, color-mix(in srgb, ${uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor!} 70%, black 30%))`,
                          cursor: 'pointer',
                          transition: 'transform 0.2s ease-in-out',
                        }
                      : {
                          backgroundColor: uiSettings.startupIconBackgroundColor || defaultUISettings.startupIconBackgroundColor,
                          cursor: 'pointer',
                          transition: 'transform 0.2s ease-in-out',
                        }
                    }
                    onMouseEnter={() => {
                      const iconPreview = document.getElementById('startup-icon-preview-interactive');
                      if (iconPreview && uiSettings.startupIconHoverAnimation && uiSettings.startupIconHoverAnimation !== 'NONE') {
                        iconPreview.classList.add(`preview-launcher-hover-${uiSettings.startupIconHoverAnimation.toLowerCase().replace(/_/g, '-')}`);
                      }
                    }}
                    onMouseLeave={() => {
                      const iconPreview = document.getElementById('startup-icon-preview-interactive');
                      if (iconPreview && uiSettings.startupIconHoverAnimation && uiSettings.startupIconHoverAnimation !== 'NONE') {
                        iconPreview.classList.remove(`preview-launcher-hover-${uiSettings.startupIconHoverAnimation.toLowerCase().replace(/_/g, '-')}`);
                        void iconPreview.offsetWidth; // Force reflow for re-trigger
                      }
                    }}
                    onClick={() => { // Simulate notification trigger
                      const iconPreview = document.getElementById('startup-icon-preview-interactive');
                      if (iconPreview && uiSettings.startupIconNotificationAnimation && uiSettings.startupIconNotificationAnimation !== 'NONE') {
                        const notifClass = `preview-launcher-notify-${uiSettings.startupIconNotificationAnimation.toLowerCase().replace(/_/g, '-')}`;
                        
                        let badge = iconPreview.querySelector('.preview-notification-badge') as HTMLElement;
                        if (uiSettings.startupIconNotificationAnimation === 'BADGE') {
                          if (!badge) {
                            badge = document.createElement('span');
                            badge.className = 'preview-notification-badge';
                            badge.textContent = '1';
                            iconPreview.appendChild(badge);
                          }
                          badge.style.display = 'flex';
                        } else if (badge) {
                            badge.style.display = 'none';
                        }

                        if (uiSettings.startupIconNotificationAnimation !== 'BADGE') {
                            iconPreview.classList.remove(notifClass); 
                            void iconPreview.offsetWidth; 
                            iconPreview.classList.add(notifClass);
                            
                            const infiniteAnimations = ['PULSE', 'WIGGLE', 'SHAKE'];
                            if (infiniteAnimations.includes(uiSettings.startupIconNotificationAnimation)) {
                                setTimeout(() => {
                                    if (iconPreview) iconPreview.classList.remove(notifClass);
                                }, 2400); 
                            } else {
                                iconPreview.addEventListener('animationend', () => iconPreview.classList.remove(notifClass), { once: true });
                            }
                        }
                      }
                    }}
                  >
                    {uiSettings.startupIconType === 'CUSTOM_URL' && uiSettings.startupIcon ? (
                      <img src={uiSettings.startupIcon} alt="Custom Icon" className="w-8 h-8 object-contain" />
                    ) : ( React.cloneElement(predefinedIcons.find(icon => icon.id === uiSettings.startupIcon)?.svg || <ImageIcon />, { style: { color: uiSettings.startupIconColor || defaultUISettings.startupIconColor }, className:"w-8 h-8" })
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">(Hover or Click to see animations)</p>
                </div>
              )}
            </div>
      
            {/* Actions Box - will now scroll with the right column if content overflows */}
            <div id="actions-box" className="border rounded-lg p-4 bg-card shadow-lg">
              <h3 className="text-lg font-semibold mb-3">Actions</h3>
              <div className="flex flex-col space-y-2">
                <Button
                  className="w-full"
                  onClick={handlePublish}
                  disabled={isPublishing || !chatbotData}
                >
                  {isPublishing ? (
                    <SpinnerIcon className="w-4 h-4 mr-2 animate-spin" />
                  ) : chatbotData?.status === 'PUBLISHED' ? (
                    <Edit3 className="w-4 h-4 mr-2" />
                  ) : (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  )}
                  {isPublishing ? 'Updating...' : chatbotData?.status === 'PUBLISHED' ? 'Unpublish (Set to Draft)' : 'Publish Chatbot'}
                </Button>
                <Button
                  variant="secondary"
                  className="w-full"
                  onClick={() => setIsSnippetModalOpen(true)}
                  disabled={chatbotData?.status !== 'PUBLISHED' || !chatbotData?.id}
                >
                  <Copy className="w-4 h-4 mr-2" /> Get Snippet Code
                </Button>
              </div>
            </div>
          </div> {/* End of Right Column (lg:w-1/3) */}
        </div> {/* End of flex lg:flex-row */}
      </Tabs>

      {/* Modals */}
      {selectedFileSource && (
        <ViewFileContentModal
          isOpen={isViewFileModalOpen}
          onClose={() => { setIsViewFileModalOpen(false); setSelectedFileSource(null); }}
          source={selectedFileSource}
        />
      )}
      {selectedWebsiteSource && (
        <ViewWebsitePagesModal
          isOpen={isViewWebsitePagesModalOpen}
          onClose={() => { setIsViewWebsitePagesModalOpen(false); setSelectedWebsiteSource(null); }}
          websiteSource={selectedWebsiteSource}
        />
      )}
      {chatbotData && (
        <EmbedSnippetModal
          isOpen={isSnippetModalOpen}
          onClose={() => setIsSnippetModalOpen(false)}
          chatbotInstanceId={chatbotData.id}
          chatbotName={chatbotData.name}
        />
      )}

      {/* Script tag is loaded dynamically via useEffect based on chatbotData */}
    </div>
  );
}