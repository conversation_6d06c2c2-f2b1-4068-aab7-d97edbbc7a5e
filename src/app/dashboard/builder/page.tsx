"use client"; 

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation"; 
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { PlusCircle, BotMessageSquare, Loader2, Trash2 } from "lucide-react"; // Added Trash2
import type { ChatbotInstance } from "@prisma/client"; 
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Added AlertDialog imports
import { toast } from 'sonner'; // For notifications

type FetchedChatbot = Pick<ChatbotInstance, "id" | "name" | "createdAt">;

export default function ChatbotListPageClient() {
  const router = useRouter();
  const [chatbots, setChatbots] = useState<FetchedChatbot[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null); // To track which bot is being deleted

  const fetchChatbots = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/chatbot-instances");
      if (!response.ok) {
        throw new Error("Failed to fetch chatbots");
      }
      const data = await response.json();
      setChatbots(data);
    } catch (error) {
      console.error("Error fetching chatbots:", error);
      toast.error("Could not load your chatbots.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchChatbots();
  }, []);

  const navigateToNewChatbotPage = () => {
    router.push('/dashboard/builder/new');
  };

  const handleDeleteChatbot = async (chatbotId: string) => {
    setIsDeleting(chatbotId);
    try {
      const response = await fetch(`/api/chatbot-instances/${chatbotId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete chatbot.' }));
        throw new Error(errorData.message || 'Failed to delete chatbot.');
      }

      toast.success('Chatbot deleted successfully.');
      setChatbots((prevChatbots) => prevChatbots.filter((bot) => bot.id !== chatbotId));
    } catch (error) {
      console.error('Error deleting chatbot:', error);
      toast.error(error instanceof Error ? error.message : 'An unknown error occurred while deleting.');
    } finally {
      setIsDeleting(null);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8 flex justify-center items-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
          Your Chatbots
        </h1>
        <Button onClick={navigateToNewChatbotPage}>
          <PlusCircle className="w-5 h-5 mr-2" />
          Create New Chatbot
        </Button>
      </div>

      {chatbots.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {chatbots.map((bot) => (
            <Card key={bot.id} className="flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <BotMessageSquare className="w-6 h-6 mr-2 text-blue-600 dark:text-blue-400" />
                    {bot.name}
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive-foreground hover:bg-destructive/90">
                        {isDeleting === bot.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete the chatbot
                          "{bot.name}".
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteChatbot(bot.id)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          disabled={isDeleting === bot.id}
                        >
                          {isDeleting === bot.id ? 'Deleting...' : 'Yes, delete chatbot'}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Created: {new Date(bot.createdAt).toLocaleDateString()}
                </p>
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push(`/dashboard/builder/${bot.id}`)}
                >
                  Edit Chatbot
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        !isLoading && ( 
          <div className="text-center py-12">
            <BotMessageSquare className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No chatbots yet</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by creating a new chatbot using the button above.
            </p>
          </div>
        )
      )}
    </div>
  );
}