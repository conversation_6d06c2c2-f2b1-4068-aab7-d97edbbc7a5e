'use client';

import React, { useState, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Loader2, Bot } from 'lucide-react';
// We need to import the ChatbotType enum if we want type safety from Prisma.
// However, since we couldn't find where it's used, we'll define a string literal type for now.
// If @prisma/client is available client-side, we could import it.
// For now, let's assume direct string values as per the schema.
type ChatbotTypeOption = 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';

export default function NewChatbotPage() {
  const router = useRouter();
  const [name, setName] = useState('');
  const [type, setType] = useState<ChatbotTypeOption>('FLOATING_WIDGET');
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!name.trim()) {
      toast.error('Please enter a name for your chatbot.');
      return;
    }
    setIsCreating(true);
    try {
      const response = await fetch('/api/chatbot-instances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, type }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to create chatbot. Please try again.' }));
        throw new Error(errorData.message || 'Failed to create chatbot.');
      }

      const newChatbot = await response.json();
      toast.success(`Chatbot "${newChatbot.name}" created successfully!`);
      router.push(`/dashboard/builder/${newChatbot.id}`);
    } catch (error) {
      console.error('Error creating chatbot:', error);
      toast.error(error instanceof Error ? error.message : 'An unknown error occurred while creating the chatbot.');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8 flex justify-center items-start">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bot className="mr-2 h-6 w-6" />
            Create New Chatbot
          </CardTitle>
          <CardDescription>
            Give your new chatbot a name and choose its type to get started.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="chatbotName">Chatbot Name</Label>
              <Input
                id="chatbotName"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Customer Support Bot"
                required
                className="mt-1"
              />
            </div>
            <div>
              <Label>Chatbot Type</Label>
              <RadioGroup
                value={type}
                onValueChange={(value: ChatbotTypeOption) => setType(value)}
                className="mt-1 space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="FLOATING_WIDGET" id="floating_widget" />
                  <Label htmlFor="floating_widget" className="font-normal">Floating Widget</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="EMBEDDED_WINDOW" id="embedded_window" />
                  <Label htmlFor="embedded_window" className="font-normal">Embedded Window</Label>
                </div>
              </RadioGroup>
            </div>
            <Button type="submit" className="w-full" disabled={isCreating}>
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Chatbot...
                </>
              ) : (
                'Create and Continue'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}