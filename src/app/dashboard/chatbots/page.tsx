// src/app/dashboard/chatbots/page.tsx
'use client'; // This page will involve client-side interactions for creating/listing

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Added Select
import { toast } from 'sonner';
import { PlusCircle } from 'lucide-react'; // For an icon

// Assuming ChatbotInstance type matches Prisma model (adjust if necessary)
interface ChatbotInstance {
  id: string;
  name: string;
  type: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  createdAt: string; // Or Date
  centralConfigId: string;
}

export default function ChatbotsPage() {
  const router = useRouter();
  const [instances, setInstances] = useState<ChatbotInstance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newInstanceName, setNewInstanceName] = useState('');
  const [newInstanceType, setNewInstanceType] = useState<'FLOATING_WIDGET' | 'EMBEDDED_WINDOW'>('FLOATING_WIDGET'); // Added state for type
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    const fetchInstances = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/chatbot-instances');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to fetch chatbot instances');
        }
        const data = await response.json();
        setInstances(data);
      } catch (error) {
        console.error(error);
        toast.error(error instanceof Error ? error.message : 'Could not load your chatbots.');
      } finally {
        setIsLoading(false);
      }
    };
    fetchInstances();
  }, []);

  const handleCreateInstance = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!newInstanceName.trim()) {
      toast.error('Please enter a name for your chatbot.');
      return;
    }
    setIsCreating(true);
    try {
      const response = await fetch('/api/chatbot-instances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newInstanceName, type: newInstanceType }), // Added type to request body
      });
      const newInstance = await response.json();
      if (!response.ok) {
        // Attempt to parse error message from backend if available
        let errorMessage = 'Failed to create chatbot instance.';
        try {
            const errorData = newInstance; // newInstance is already parsed if response not ok
            if (errorData && errorData.message) {
                errorMessage = errorData.message;
            } else if (errorData && errorData.errors) {
                 errorMessage = Object.values(errorData.errors).flat().join(' ');
            }
        } catch (e) { /* ignore parsing error, use default message */ }
        throw new Error(errorMessage);
      }
      toast.success(`Chatbot "${newInstance.name}" created!`);
      // Redirect to the builder page for the new instance
      router.push(`/dashboard/builder/${newInstance.id}`);
    } catch (error) {
      console.error(error);
      toast.error(error instanceof Error ? error.message : 'Could not create chatbot.');
    } finally {
      setIsCreating(false);
    }
  };

  if (isLoading) {
    return <div className='container mx-auto p-4 text-center'>Loading your chatbots...</div>;
  }

  return (
    <div className='container mx-auto p-4 md:p-6'>
      <div className='flex flex-col sm:flex-row justify-between items-center mb-6 gap-4'>
        <h1 className='text-2xl sm:text-3xl font-bold'>Your Chatbots</h1>
        <Button onClick={() => setShowCreateForm(!showCreateForm)} size="lg">
          <PlusCircle className='mr-2 h-5 w-5' /> Create New Chatbot
        </Button>
      </div>

      {showCreateForm && (
        <div className='mb-8 p-4 sm:p-6 border rounded-lg shadow-lg bg-card'>
          <form onSubmit={handleCreateInstance} >
            <h2 className="text-xl font-semibold mb-4">New Chatbot</h2>
            <div className='space-y-3'>
              <div>
                <Label htmlFor='newInstanceName' className="text-sm font-medium">Chatbot Name</Label>
                <Input
                  id='newInstanceName'
                  type='text'
                  value={newInstanceName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewInstanceName(e.target.value)}
                  placeholder='My Awesome Chatbot'
                  required
                  disabled={isCreating}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor='newInstanceType' className="text-sm font-medium">Chatbot Type</Label>
                <Select
                  value={newInstanceType}
                  onValueChange={(value: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW') => setNewInstanceType(value)}
                  disabled={isCreating}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FLOATING_WIDGET">Floating Widget</SelectItem>
                    <SelectItem value="EMBEDDED_WINDOW">Embedded Window</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button type='submit' disabled={isCreating} className='mt-5 w-full sm:w-auto'>
              {isCreating ? 'Creating...' : 'Create & Go to Builder'}
            </Button>
          </form>
        </div>
      )}

      {instances.length === 0 && !showCreateForm && (
         <div className="text-center py-10">
            <PlusCircle className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No chatbots</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new chatbot.</p>
            <div className="mt-6">
                <Button onClick={() => setShowCreateForm(true)}>
                    <PlusCircle className='mr-2 h-4 w-4' /> Create New Chatbot
                </Button>
            </div>
        </div>
      )}

      <div className='space-y-4'>
        {instances.map((instance) => (
          <div key={instance.id} className='p-4 border rounded-lg shadow-sm bg-card hover:shadow-md transition-shadow flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3'>
            <div className="flex-grow">
              <h3 className='text-lg sm:text-xl font-semibold text-primary'>{instance.name}</h3>
              <p className='text-xs sm:text-sm text-muted-foreground'>
                ID: {instance.id}
              </p>
              <p className='text-xs sm:text-sm text-muted-foreground capitalize'>
                Type: {instance.type.replace('_', ' ').toLowerCase()} | Status: {instance.status.toLowerCase()}
              </p>
              <p className='text-xs text-muted-foreground'>Created: {new Date(instance.createdAt).toLocaleDateString()}</p>
            </div>
            <Link href={`/dashboard/builder/${instance.id}`} className="mt-2 sm:mt-0">
              <Button variant='outline' size="sm">Open Builder</Button>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}