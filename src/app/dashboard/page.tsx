import { auth } from "@/auth"; // Correct import for Auth.js v5
import { redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import pg from 'pg';
const { Pool } = pg;
import { getUserSubscriptionPlan } from "@/lib/subscription";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ListChecks, MessageSquareText, BotMessageSquare, CreditCard, UserCircle } from "lucide-react";

export default async function DashboardPage() {
  const session = await auth(); // Correct way to get session in Auth.js v5

  if (!session || !session.user || !session.user.id) {
    redirect("/api/auth/signin?callbackUrl=/dashboard");
    return null; // Or a loading state
  }

  const { user } = session;

  let subscriptionPlan;
  if (user && user.id && (user.role === "USER" || user.role === "ADMIN")) {
    subscriptionPlan = await getUserSubscriptionPlan(user.id);
  } else {
    redirect("/login");
  }
  
  // Fetch chatbot data
  const chatbotCount = await prisma.chatbotInstance.count({
    where: { userId: user.id },
  });

  const recentChatbots = await prisma.chatbotInstance.findMany({
    where: { userId: user.id },
    orderBy: { createdAt: "desc" },
    take: 3,
    select: { id: true, name: true, createdAt: true },
  });

  // Initialize recent sessions array
  let recentSessions: {
    id: string;
    start_time: string;
    user_identifier?: string;
    chatbot_name?: string;
  }[] = [];

  try {
    // Fetch recent chat sessions from RAG database (last 24 hours)
    const ragDbUrl = process.env.RAG_DATABASE_URL;
    console.log('RAG_DATABASE_URL:', ragDbUrl ? '*****' : 'NOT SET');
    
    if (!ragDbUrl) {
      throw new Error('RAG_DATABASE_URL environment variable is not set');
    }

    const pool = new Pool({ connectionString: ragDbUrl });
    const client = await pool.connect();
    console.log('Fetching recent sessions for user:', user.id);
    
    const query = `
      SELECT
        cs.id,
        cs.start_time,
        cs.user_identifier,
        ci.name AS chatbot_name
      FROM chat_sessions cs
      JOIN chatbot_instances ci ON cs.chatbot_instance_id = ci.id
      WHERE cs.user_id = $1
        AND cs.start_time >= NOW() - INTERVAL '24 HOURS'
      ORDER BY cs.start_time DESC
      LIMIT 5
    `;
    console.log('Executing query:', query);
    
    const result = await client.query(query, [user.id]);
    
    console.log('Query result:', result.rows);
    console.log('Found sessions:', result.rows.length);
    
    recentSessions = result.rows;
    client.release();
    await pool.end();
  } catch (error: any) {
    console.error('Error fetching recent sessions:', error);
    // Add error to UI for visibility
    recentSessions = [{
      id: 'error',
      start_time: new Date().toISOString(),
      chatbot_name: 'Error',
      user_identifier: error.message
    }];
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
          Welcome back, {user.name || "User"}!
        </p>
      </div>

      <div className="mb-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{user.name}</h2>
            <div className="mt-1 flex items-center space-x-2">
              <Badge variant="outline" className="text-sm">
                <UserCircle className="w-4 h-4 mr-1.5" />
                Role: {user.role || "USER"}
              </Badge>
              <Badge variant="secondary" className="text-sm">
                <CreditCard className="w-4 h-4 mr-1.5" />
                Tier: {subscriptionPlan.name || "FREE"}
              </Badge>
            </div>
          </div>
          <Link href="/dashboard/builder">
            <Button className="mt-4 sm:mt-0">
              <BotMessageSquare className="w-5 h-5 mr-2" /> Manage Chatbots
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Chatbots Overview Card */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <ListChecks className="w-6 h-6 mr-2 text-blue-600 dark:text-blue-400" />
              Your Chatbots
            </CardTitle>
            <CardDescription>
              You currently have {chatbotCount} chatbot
              {chatbotCount === 1 ? "" : "s"}.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentChatbots.length > 0 ? (
              <ul className="space-y-3">
                {recentChatbots.map((bot) => (
                  <li
                    key={bot.id}
                    className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600/60 transition-colors"
                  >
                    <Link href={`/dashboard/builder/${bot.id}`} className="block">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-80 dark:text-gray-100">{bot.name}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Created: {new Date(bot.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                You haven't created any chatbots yet.
              </p>
            )}
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/builder" passHref legacyBehavior>
              <Button variant="outline" className="w-full">
                View All Chatbots
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Plan/Subscription Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="w-6 h-6 mr-2 text-green-600 dark:text-green-400" />
              Current Plan
            </CardTitle>
            <CardDescription>Your current subscription tier.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-4xl font-bold text-gray-900 dark:text-white">
                {subscriptionPlan.name || "FREE"}
              </p>
              {/* Add more details based on tier if available */}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="default" className="w-full" disabled> {/* Disabled for now */}
              Upgrade Plan (Coming Soon)
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Chats Card - Placeholder */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquareText className="w-6 h-6 mr-2 text-purple-600 dark:text-purple-400" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Overview of your recent chat sessions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentSessions.length > 0 ? (
              <ul className="space-y-2">
                {recentSessions.map((session) => (
                  <li key={session.id} className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600/60 transition-colors">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{session.chatbot_name || 'Unknown Chatbot'}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {session.user_identifier ? `User: ${session.user_identifier}` : `Session started`}
                        </p>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(session.start_time).toLocaleString(undefined, {
                          dateStyle: 'medium',
                          timeStyle: 'short',
                        })}
                      </span>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-center py-6">
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                  No recent chat activity to display yet.
                </p>
                <Link href="/dashboard/chat-history">
                  <Button variant="outline" size="sm">View All Chat History</Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}