import { redirect } from "next/navigation";

import { getCurrentUser } from "@/lib/session";
import { getUserSubscriptionPlan } from "@/lib/subscription";
import { constructMetadata } from "@/lib/utils";
import { DashboardHeader } from "@/components/dashboard/header";
import { PricingCards } from "@/components/pricing/pricing-cards";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export const metadata = constructMetadata({
  title: "Billing – AgentiveAIQ",
  description: "Manage billing and your subscription plan.",
});

export default async function BillingPage() {
  const user = await getCurrentUser();

  let userSubscriptionPlan;
  if (user && user.id && (user.role === "USER" || user.role === "ADMIN")) {
    userSubscriptionPlan = await getUserSubscriptionPlan(user.id);
  } else {
    redirect("/login");
  }

  return (
    <>
      <DashboardHeader
        heading="Billing"
        text="Manage billing and your subscription plan."
      />
      <div className="grid gap-8">
        <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
          <h3 className="mb-2 text-xl font-semibold">Current Subscription</h3>
          <p className="mb-4 text-muted-foreground">
            You are currently on the{" "}
            <span className="font-medium text-foreground">
              {userSubscriptionPlan.name || "Free"}
            </span>{" "}
            plan.
          </p>
          {!userSubscriptionPlan.isFree && (
            <Button asChild>
              <Link href="/api/polar/customer-portal">Manage Subscription</Link>
            </Button>
          )}
        </div>

        <PricingCards userId={user.id} subscriptionPlan={userSubscriptionPlan} />
      </div>
    </>
  );
}