@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* PRD Colors */
  --color-lime-green: #32CD32;
  --color-dark-grey: #333333;
  --color-black: #000000;
  --color-white: #FFFFFF;
  --color-light-grey: #F0F0F0;

  /* Mapped to existing variables for Tailwind integration */
  --background: var(--color-white); /* Main Background */
  --foreground: var(--color-black); /* Primary Text Color */
  --primary: var(--color-lime-green); /* Primary Accent/Button Background */
  --primary-foreground: var(--color-black); /* Text on Primary */
  --secondary: var(--color-dark-grey); /* Secondary Background/Button */
  --secondary-foreground: var(--color-white); /* Text on Secondary */
  --muted: var(--color-light-grey); /* Muted backgrounds like section 3 */
  --muted-foreground: var(--color-dark-grey); /* Text on muted backgrounds */
  --accent: var(--color-lime-green); /* General accent */
  --accent-foreground: var(--color-black); /* Text on accent */
  --border: var(--color-light-grey); /* Border colors */
  --input: var(--color-light-grey);
  --ring: var(--color-lime-green); /* Focus ring */

  /* Keep existing chart/sidebar colors if not specified in PRD */
  --card: var(--color-white);
  --card-foreground: var(--color-black);
  --popover: var(--color-white);
  --popover-foreground: var(--color-black);
  --destructive: oklch(0.577 0.245 27.325); /* No PRD equivalent, keep default */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* PRD Colors for Dark Mode (adjust as needed, assuming dark grey background) */
  --background: var(--color-dark-grey); /* Main Background */
  --foreground: var(--color-white); /* Primary Text Color */
  --primary: var(--color-lime-green); /* Primary Accent/Button Background */
  --primary-foreground: var(--color-black); /* Text on Primary */
  --secondary: var(--color-white); /* Secondary Background/Button */
  --secondary-foreground: var(--color-dark-grey); /* Text on Secondary */
  --muted: var(--color-dark-grey); /* Muted backgrounds */
  --muted-foreground: var(--color-light-grey); /* Text on muted backgrounds */
  --accent: var(--color-lime-green); /* General accent */
  --accent-foreground: var(--color-black); /* Text on accent */
  --border: var(--color-dark-grey); /* Border colors */
  --input: var(--color-dark-grey);
  --ring: var(--color-lime-green); /* Focus ring */

  /* Keep existing chart/sidebar colors if not specified in PRD */
  --card: var(--color-dark-grey);
  --card-foreground: var(--color-white);
  --popover: var(--color-dark-grey);
  --popover-foreground: var(--color-white);
  --destructive: oklch(0.704 0.191 22.216); /* No PRD equivalent, keep default */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  /* Keyframes for the wave animation */
  @keyframes wave {
    0% {
      transform: translateX(0) translateY(0);
    }
    50% {
      transform: translateX(-5%) translateY(-5%);
    }
    100% {
      transform: translateX(0) translateY(0);
    }
  }
  
  /* Apply wave animation to the primary background */
  .animate-wave {
    animation: wave 15s ease-in-out infinite alternate;
    background: linear-gradient(135deg, var(--color-lime-green) 0%, #6af06a 100%); /* Subtle gradient for depth */
    clip-path: ellipse(75% 50% at 50% 50%); /* Creates a wave-like shape */
  }
  
  /* Keyframes for pulse animation (for CTA button) */
  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(50, 205, 50, 0.7);
    }
    70% {
      transform: scale(1.05);
      box-shadow: 0 0 0 20px rgba(50, 205, 50, 0);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(50, 205, 50, 0);
    }
  }
  
  .animate-pulse {
    animation: pulse 2s infinite;
  }
  
  /* Navbar scroll effect */
  .navbar-scrolled {
    background-color: var(--primary); /* Lime Green */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar for Chat Preview */
.custom-chat-preview-scrollbar::-webkit-scrollbar {
    width: 6px;
}
.custom-chat-preview-scrollbar::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1); /* Or a themeable variable later */
    border-radius: 10px;
}
.custom-chat-preview-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3); /* Or a themeable variable later */
    border-radius: 10px;
}
.custom-chat-preview-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5); /* Or a themeable variable later */
}
/* Basic Firefox scrollbar styling */
.custom-chat-preview-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255,255,255,0.3) rgba(0,0,0,0.1); /* thumb track */
}
