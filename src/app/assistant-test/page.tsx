'use client';

import React, { useRef } from 'react';
import { AssistantRuntimeProvider } from '@assistant-ui/react'; 
import { Thread } from '@/components/assistant-ui/thread'; 

import { useLangGraphRuntime } from '@assistant-ui/react-langgraph';
import type { LangGraphStreamCallback, LangChainMessage, LangGraphMessagesEvent } from '@assistant-ui/react-langgraph';

// Mocked Adapter for useLangGraphRuntime
const createMockLangGraphStreamCallback = (configId: string): LangGraphStreamCallback<LangChainMessage> => {
  return async function* (inputMessages: LangChainMessage[]) { 
    console.log(`[[Mock Stream Callback Fired for ${configId}]] Input:`, inputMessages);
    
    const lastUserMessage = inputMessages[inputMessages.length - 1];
    let displayQuery = "a query";

    if (lastUserMessage?.content) {
      if (typeof lastUserMessage.content === 'string') {
        displayQuery = lastUserMessage.content;
      } else if (Array.isArray(lastUserMessage.content)) {
        const textPart = lastUserMessage.content.find(part => part.type === 'text');
        if (textPart && 'text' in textPart && typeof textPart.text === 'string') {
          displayQuery = textPart.text;
        } else {
          displayQuery = JSON.stringify(lastUserMessage.content); 
        }
      }
    }
    
    const mockReply = `Mocked LangGraph reply to: "${displayQuery}" for ${configId}. `;
    const assistantMessageId = `assistant_${Date.now()}`;

    const initialAssistantMessage: LangChainMessage = { 
      id: assistantMessageId, 
      type: 'ai', 
      content: "", 
      // tool_calls: [], // Optional, can be omitted
      // tool_call_chunks: [], // Optional, can be omitted
    };
    yield { event: "messages", data: [initialAssistantMessage] } as LangGraphMessagesEvent<LangChainMessage>;
    
    let accumulatedContent = "";
    for (let i = 0; i < mockReply.length; i++) {
      const char = mockReply[i];
      accumulatedContent += char;
      const partialUpdateMessage: LangChainMessage = {
        ...initialAssistantMessage,
        content: accumulatedContent,
      };
      yield { 
        event: "messages/partial", 
        data: [partialUpdateMessage] 
      } as LangGraphMessagesEvent<LangChainMessage>; 
      await new Promise(r => setTimeout(r, 50)); 
    }
    
    yield { event: "on_stream_end" } as any; 
  };
};

const AssistantTestPage = () => {
  const threadIdRef = useRef<string | undefined>(); 
  
  const runtime = useLangGraphRuntime({
    threadId: threadIdRef.current, 
    stream: createMockLangGraphStreamCallback("test-config-id"),
  });

  if (!runtime) {
    return <div>Initializing LangGraph runtime...</div>;
  }

  return (
    <div className="flex flex-col h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">Assistant UI LangGraph Test Page</h1>
      <div className="flex-grow border rounded-md h-full flex flex-col">
        <AssistantRuntimeProvider runtime={runtime}>
          <Thread /> 
        </AssistantRuntimeProvider>
      </div>
    </div>
  );
};

export default AssistantTestPage;