"use client"; // For usePathname

import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner"; // Import Toaster from sonner
import AuthButton from "@/components/auth/AuthButton";
import Sidebar from "@/components/layout/Sidebar"; // To be created
import { usePathname } from "next/navigation";
import HeaderNav from "@/components/layout/HeaderNav";
import Providers from "./providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Metadata has been removed as it's not allowed in "use client" components.
// Global metadata can be handled differently if needed, e.g. via a template.tsx or direct <title> tags.

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const showSidebar = pathname.startsWith("/dashboard");

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          <header className="p-4 flex justify-between items-center border-b sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div>AgentiveAIQ</div> {/* Logo Placeholder */}
            <HeaderNav />
          </header>
          <div className="flex min-h-[calc(100vh-65px)]">{/* Adjusted for header height */}
            {showSidebar && <Sidebar />}
            <main className={`flex-grow p-4 ${showSidebar ? 'md:ml-64' : ''}`}> {/* Removed mt-16 as min-h on parent should handle layout */}
              {children}
            </main>
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
