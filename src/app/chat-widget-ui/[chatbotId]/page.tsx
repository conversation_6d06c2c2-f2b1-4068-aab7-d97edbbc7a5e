'use client';

import { useParams } from 'next/navigation';
import { useChat, type Message as VercelAIChatMessage } from 'ai/react';
import { useEffect, useState, CSSProperties, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SendHorizonal, RefreshCw, Loader2, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

// --- UI Settings Types (rolled back) ---
interface ChatbotUISettings {
  // Colors
  headerBackgroundColor?: string;
  headerTextColor?: string;
  chatWindowBackgroundColor?: string;
  inputAreaBackgroundColor?: string;
  inputAreaTextColor?: string;
  sendButtonColor?: string;
  sendButtonIconColor?: string;
  userBubbleBackgroundColor?: string;
  userBubbleTextColor?: string;
  agentBubbleBackgroundColor?: string;
  agentBubbleTextColor?: string;
  // <PERSON>hape & <PERSON>
  windowBorderRadius?: number;
  bubbleBorderRadius?: number;
  // Header
  headerText?: string;
  headerHeight?: number;
  // Sizing
  chatbotType?: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
  // Fonts
  fontFamily?: string;
  headerFontSize?: number;
  bubbleFontSize?: number;
  inputFontSize?: number;
  // Animations
  widgetOpenAnimation?: 'NONE' | 'FADE_IN' | 'SLIDE_UP_FAST' | 'SLIDE_IN_RIGHT_FAST' | 'SCALE_UP_CENTER';
  messageEntranceAnimation?: 'NONE' | 'FADE_IN' | 'SLIDE_UP_MESSAGE' | 'SCALE_IN_MESSAGE';
  typingIndicatorStyle?: 'DOTS' | 'WAVE' | 'TEXT' | 'NONE';
  // Startup Icon
  startupIconType?: 'PREDEFINED' | 'CUSTOM_URL' | 'UPLOADED';
  startupIcon?: string;
  startupIconColor?: string;
  startupIconBackgroundColor?: string;
  startupIconHoverAnimation?: 'NONE' | 'SCALE' | 'GLOW' | 'ROTATE_SLIGHTLY';
  startupIconNotificationAnimation?: 'NONE' | 'PULSE' | 'WIGGLE' | 'BADGE' | 'SHAKE';
}

const defaultUISettings: ChatbotUISettings = {
  headerBackgroundColor: '#32324E', headerTextColor: '#FFFFFF',
  chatWindowBackgroundColor: '#28283C',
  inputAreaBackgroundColor: '#28283C', inputAreaTextColor: '#FFFFFF',
  sendButtonColor: '#AFFF3C', sendButtonIconColor: '#000000',
  userBubbleBackgroundColor: '#8A2BE2', userBubbleTextColor: '#FFFFFF',
  agentBubbleBackgroundColor: '#AFFF3C', agentBubbleTextColor: '#000000',
  windowBorderRadius: 15, bubbleBorderRadius: 18,
  headerText: 'AI Assistant', headerHeight: 60,
  chatbotType: 'FLOATING_WIDGET',
  fontFamily: 'Inter, sans-serif', headerFontSize: 18, bubbleFontSize: 14, inputFontSize: 14,
  widgetOpenAnimation: 'SLIDE_UP_FAST', messageEntranceAnimation: 'FADE_IN', typingIndicatorStyle: 'DOTS',
  startupIconType: 'PREDEFINED', startupIcon: 'default_chat_icon',
  startupIconColor: '#FFFFFF', startupIconBackgroundColor: '#8A2BE2',
  startupIconHoverAnimation: 'SCALE', startupIconNotificationAnimation: 'PULSE',
};

interface PublicConfig {
  name: string;
  uiSettings: Partial<ChatbotUISettings>;
  type: 'FLOATING_WIDGET' | 'EMBEDDED_WINDOW';
}

export default function ChatWidgetUIPage() {
  const params = useParams();
  const chatbotId = params.chatbotId as string;

  const [config, setConfig] = useState<PublicConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(true);
  const [configError, setConfigError] = useState<string | null>(null);

  const uiSettings = useMemo(() => {
    return { ...defaultUISettings, ...(config?.uiSettings || {}) };
  }, [config]);

  useEffect(() => {
    if (chatbotId) {
      setIsLoadingConfig(true);
      setConfigError(null);
      fetch(`/api/chatbot-instances/${chatbotId}/public-config`)
        .then(async res => {
          if (!res.ok) {
            const errorData = await res.json().catch(() => ({ message: `HTTP error! status: ${res.status}` }));
            throw new Error(errorData.message || `Failed to fetch config (${res.status})`);
          }
          return res.json();
        })
        .then((data: PublicConfig) => {
          setConfig(data);
        })
        .catch(err => {
          console.error("Failed to fetch widget config:", err);
          setConfigError(err.message || "Could not load widget configuration.");
          toast.error(`Widget Load Error: ${err.message}`);
        })
        .finally(() => {
          setIsLoadingConfig(false);
        });
    } else {
      setConfigError("Chatbot ID not provided.");
      setIsLoadingConfig(false);
    }
  }, [chatbotId]);

  const { messages, input, handleInputChange, handleSubmit, isLoading: isChatLoading, error: chatError, setMessages } = useChat({
    api: '/api/chat-public',
    body: { data: { centralConfigId: chatbotId } },
    initialMessages: [
      { id: 'initial-widget-greeting', role: 'assistant', content: 'Hello! How can I assist you?' }
    ],
    onError: (err) => {
      console.error("Chat widget error:", err);
      toast.error(`Chat Error: ${err.message}`);
    }
  });

  const handleResetConversation = () => {
    setMessages([{ id: 'reset-widget-greeting', role: 'assistant', content: 'Hello! How can I assist you?' }]);
  };

  // --- Dynamic Styles ---
  const IframeWrapperStyles: CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    width: '100vw',
    margin: 0,
    padding: 0,
    boxSizing: 'border-box',
    fontFamily: uiSettings.fontFamily,
    backgroundColor: uiSettings.chatWindowBackgroundColor,
    borderRadius: `${uiSettings.windowBorderRadius}px`,
    overflow: 'hidden',
  };

  const HeaderStyles: CSSProperties = {
    backgroundColor: uiSettings.headerBackgroundColor,
    color: uiSettings.headerTextColor,
    padding: '0 15px',
    height: `${uiSettings.headerHeight}px`,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexShrink: 0,
    fontSize: `${uiSettings.headerFontSize}px`,
    fontWeight: 'bold',
  };

  const MessageListStyles: CSSProperties = {
    flexGrow: 1,
    overflowY: 'auto',
    padding: '15px',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
  };

  const MessageBubbleStyles = (isUser: boolean): CSSProperties => ({
    padding: '8px 12px',
    borderRadius: `${uiSettings.bubbleBorderRadius}px`,
    maxWidth: '80%',
    wordBreak: 'break-word',
    fontSize: `${uiSettings.bubbleFontSize}px`,
    alignSelf: isUser ? 'flex-end' : 'flex-start',
    backgroundColor: isUser ? uiSettings.userBubbleBackgroundColor : uiSettings.agentBubbleBackgroundColor,
    color: isUser ? uiSettings.userBubbleTextColor : uiSettings.agentBubbleTextColor,
    borderBottomLeftRadius: !isUser ? '5px' : undefined,
    borderBottomRightRadius: isUser ? '5px' : undefined,
  });

  const InputAreaStyles: CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    padding: '10px 15px',
    backgroundColor: uiSettings.inputAreaBackgroundColor,
    borderTop: `1px solid rgba(100,100,100,0.2)`, // Default border color
    flexShrink: 0,
  };

  const InputStyles: CSSProperties = {
    flexGrow: 1,
    marginRight: '8px',
    borderRadius: `20px`, // Default border radius
    padding: '8px 12px',
    border: `1px solid rgba(255,255,255,0.2)`, // Default border color
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    color: uiSettings.inputAreaTextColor,
    fontSize: `${uiSettings.inputFontSize}px`,
  };
  
  const SendButtonStyles: CSSProperties = {
    borderRadius: '50%',
    width: '40px',
    height: '40px',
    backgroundColor: uiSettings.sendButtonColor,
    color: uiSettings.sendButtonIconColor,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
  };


  if (isLoadingConfig) {
    return (
      <div style={{ ...IframeWrapperStyles, justifyContent: 'center', alignItems: 'center', backgroundColor: '#e0e0e0' }}>
        <Loader2 className="h-8 w-8 animate-spin text-gray-600" />
        <p style={{ marginTop: '10px', color: '#555' }}>Loading Chat Experience...</p>
      </div>
    );
  }

  if (configError || !config) {
    return (
      <div style={{ ...IframeWrapperStyles, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fdecea', color: '#912323', padding: '20px', textAlign: 'center' }}>
        <AlertTriangle className="h-8 w-8 mb-2" />
        <p style={{ fontWeight: 'bold' }}>Widget Error</p>
        <p>{configError || 'Could not load widget.'}</p>
        <p style={{ fontSize: '0.8em', marginTop: '10px' }}>Chatbot ID: {chatbotId || 'N/A'}</p>
      </div>
    );
  }

  return (
    <div style={IframeWrapperStyles} className={`font-${uiSettings.fontFamily?.split(',')[0].toLowerCase().replace(/\s+/g, '-')}`}>
      <div style={HeaderStyles}>
        <span>{uiSettings.headerText || config.name}</span>
        <Button variant="ghost" size="icon" onClick={handleResetConversation} title="Reset Conversation" style={{ color: uiSettings.headerTextColor, padding: '4px' }} className="hover:bg-white/20">
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      <div style={MessageListStyles} className="custom-chat-preview-scrollbar">
        {messages.map((m: VercelAIChatMessage) => (
          <div key={m.id} style={MessageBubbleStyles(m.role === 'user')}>
            {m.content}
          </div>
        ))}
        {isChatLoading && messages.length > 0 && messages[messages.length -1].role === 'user' && (
            <div style={{...MessageBubbleStyles(false), opacity: 0.7, fontStyle: 'italic'}}>
                {uiSettings.typingIndicatorStyle === 'TEXT' ? 'Agent is typing...' : 
                 uiSettings.typingIndicatorStyle === 'DOTS' ? '● ● ●' : 
                 uiSettings.typingIndicatorStyle === 'WAVE' ? '~ ~ ~' : ''}
            </div>
        )}
        {chatError && (
          <div style={{...MessageBubbleStyles(false), backgroundColor: '#ffdddd', color: '#d8000c'}}>
            Error: {chatError.message}
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} style={InputAreaStyles}>
        <Input
          value={input}
          onChange={handleInputChange}
          placeholder="Type your message..."
          disabled={isChatLoading}
          style={InputStyles}
        />
        <Button type="submit" size="icon" disabled={isChatLoading} style={SendButtonStyles}>
          {isChatLoading ? <Loader2 className="h-5 w-5 animate-spin" /> : <SendHorizonal className="h-5 w-5" />}
        </Button>
      </form>
    </div>
  );
}