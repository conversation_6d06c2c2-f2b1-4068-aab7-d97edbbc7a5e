"use client";

import Link from "next/link";
import { useState, useEffect } from "react";

interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  createdAt: string;
}

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  categoryId: string;
  createdAt: string;
  updatedAt: string;
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    // Fetch blog posts
    fetch('/api/admin/blog/posts')
      .then(res => res.json())
      .then((data: BlogPost[]) => setPosts(data.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )));

    // Fetch blog categories
    fetch('/api/admin/blog/categories')
      .then(res => res.json())
      .then((data: BlogCategory[]) => setCategories(data));
  }, []);

  const filteredPosts = selectedCategory
    ? posts.filter(post => post.categoryId === selectedCategory)
    : posts;

  return (
    <div className="container mx-auto py-12 px-4">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Category Sidebar */}
        <div className="w-full md:w-1/4">
          <h2 className="text-xl font-bold mb-4 text-gray-800">Categories</h2>
          <div className="space-y-2">
            <button
              onClick={() => setSelectedCategory(null)}
              className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                selectedCategory === null
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              All Posts
            </button>
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
        
        {/* Blog Posts */}
        <div className="w-full md:w-3/4">
          <h1 className="text-3xl font-bold mb-8 text-gray-900">Latest Articles</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map(post => (
              <div
                key={post.id}
                className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="p-6">
                  <span className="inline-block px-3 py-1 text-xs font-semibold text-blue-600 bg-blue-100 rounded-full mb-3">
                    {categories.find(c => c.id === post.categoryId)?.name || 'Uncategorized'}
                  </span>
                  <h2 className="text-xl font-bold mb-2 text-gray-800">{post.title}</h2>
                  <p className="text-gray-600 mb-4">{post.excerpt || 'Read more about this topic...'}</p>
                  <Link
                    href={`/blog/${post.slug}`}
                    className="text-blue-600 font-medium hover:text-blue-800 transition-colors"
                  >
                    Read Full Article
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
