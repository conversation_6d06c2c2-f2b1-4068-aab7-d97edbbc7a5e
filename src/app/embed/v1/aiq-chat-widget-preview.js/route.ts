import { NextResponse } from 'next/server';

export async function GET() {
  const appUrl = process.env.NEXT_PUBLIC_APP_URL || '';
  
  const scriptContent = `
(function() {
  console.log("AIQ Chatbot (Preview Script) Loader Initialized.");
  const container = document.getElementById('aiq-chatbot-container');
  if (!container) {
    console.error('AIQ Chatbot (Preview Script): Container div with id "aiq-chatbot-container" not found.');
    return;
  }

  const chatbotId = container.getAttribute('data-chatbot-id');
  if (!chatbotId) {
    console.error('AIQ Chatbot (Preview Script): data-chatbot-id attribute not found on container.');
    container.innerHTML = '<p style="color:red; font-family:sans-serif;">AIQ Chatbot Error: data-chatbot-id missing.</p>';
    return;
  }

  if (!"${appUrl}") {
    console.error("AIQ Chatbot (Preview Script): NEXT_PUBLIC_APP_URL is not set. Cannot load widget UI.");
    container.innerHTML = '<p style="color:red; font-family:sans-serif;">Chat widget configuration error: App URL not set.</p>';
    return;
  }

  console.log('AIQ Chatbot (Preview Script): Loading for chatbotId:', chatbotId);

  // launcherButton is not used in preview-only script if always embedded
  // let launcherButton = null; 
  let chatWidgetContainer = null;
  // widgetVisible is not used in preview-only script if always embedded and visible
  // let widgetVisible = false; 

  // --- Base CSS with Custom Properties ---
  const baseCss = \`
    :root { /* Default fallback values for CSS variables */
      --aiq-font-family: Arial, sans-serif;
      --aiq-widget-opacity: 1;
      --aiq-chat-window-bg-color: rgba(40, 40, 60, 0.85);
      --aiq-window-border-radius: 30px;
      
      --aiq-widget-width: 350px; /* Default for TALL_NARROW */
      --aiq-widget-height: 550px; /* Default for TALL_NARROW */
      --aiq-embedded-width: "100%";
      --aiq-embedded-height: "500px";

      /* Header */
      --aiq-header-text: 'AIQ Assistant';
      --aiq-header-height: 60px;
      --aiq-header-font-size: 18px;
      --aiq-header-text-color: #FFFFFF;
      --aiq-header-bg-color: #32324E;
      --aiq-close-button-icon-color: #FFFFFF;
      --aiq-close-button-hover-icon-color: #AFFF3C; 
      --aiq-close-button-icon-size: 20px;

      /* Launcher (Styles kept for potential future use, but launcher itself removed for preview) */
      --aiq-launcher-offset-x: 20px;
      --aiq-launcher-offset-y: 20px;
      --aiq-startup-icon-color: #FFFFFF;
      --aiq-startup-icon-bg-color: #8A2BE2;
      --aiq-launcher-icon-size: 28px;
      --aiq-launcher-button-size: 60px;

      /* Messages Area */
      --aiq-bubble-font-size: 14px;
      --aiq-bubble-border-radius: 20px;
      --aiq-user-bubble-text-color: #FFFFFF;
      --aiq-user-bubble-bg-color: #8A2BE2;
      --aiq-agent-bubble-text-color: #000000;
      --aiq-agent-bubble-bg-color: #AFFF3C;

      /* Input Area */
      --aiq-input-font-size: 14px;
      --aiq-input-text-color: #FFFFFF; 
      --aiq-input-placeholder-color: rgba(255, 255, 255, 0.7);
      --aiq-input-area-bg-color: rgba(40, 40, 60, 0.85);
      --aiq-input-field-border-color: rgba(255, 255, 255, 0.2); 
      --aiq-input-field-focus-border-color: #AFFF3C; 
      --aiq-input-border-radius: 20px; 
      --aiq-send-button-bg-color: #000000;
      --aiq-send-button-icon-color: #FF0000;
      --aiq-send-button-size: 40px;
      --aiq-send-button-icon-svg-size: 20px;
      --aiq-send-button-bg-color-rgb: 0,0,0; /* For pulse animation */

      /* Scrollbar */
      --aiq-scrollbar-width: 6px;
      --aiq-scrollbar-track-color: rgba(0,0,0,0.1);
      --aiq-scrollbar-thumb-color: rgba(255,255,255,0.3);
      --aiq-scrollbar-thumb-hover-color: rgba(255,255,255,0.5);
    }

    /* Launcher styles removed as launcher button is not created for preview */

    #aiq-chat-widget-container {
      /* position: fixed; */ /* Changed for embedded */
      z-index: 9999; /* May not be needed if embedded within a specific stacking context */
      background-color: var(--aiq-chat-window-bg-color);
      opacity: var(--aiq-widget-opacity);
      border-radius: var(--aiq-window-border-radius);
      box-shadow: 0 8px 25px rgba(0,0,0,0.3);
      width: var(--aiq-widget-width); /* Will be overridden by aiq-embedded */
      height: var(--aiq-widget-height); /* Will be overridden by aiq-embedded */
      /* display: none; */ /* Changed for embedded */
      flex-direction: column;
      overflow: hidden;
      box-sizing: border-box;
      font-family: var(--aiq-font-family);
      transition: opacity 0.3s ease, transform 0.3s ease; 
    }

    #aiq-chat-widget-container.aiq-embedded {
      position: relative;
      width: var(--aiq-embedded-width);
      height: var(--aiq-embedded-height);
      display: flex; /* Show by default if embedded */
      box-shadow: 0 2px 10px rgba(0,0,0,0.15); 
      z-index: auto;
    }
    
    #aiq-chat-header {
      background-image: var(--aiq-header-bg-image);
      background-color: var(--aiq-header-bg-color);
      color: var(--aiq-header-text-color);
      height: var(--aiq-header-height);
      min-height: var(--aiq-header-height);
      padding: 0 15px 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      flex-shrink: 0; 
    }
    #aiq-chat-header h2 {
      margin: 0;
      font-size: var(--aiq-header-font-size);
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    #aiq-close-chat-btn {
      background: none;
      border: none;
      color: var(--aiq-close-button-icon-color);
      cursor: pointer;
      padding: 8px; 
      display: flex;
      align-items: center;
      justify-content: center;
      transition: color 0.2s ease;
      /* visibility: hidden; */ /* Close button might not be desired in preview */
    }
    #aiq-close-chat-btn svg {
      width: var(--aiq-close-button-icon-size);
      height: var(--aiq-close-button-icon-size);
      stroke: currentColor;
    }
    #aiq-close-chat-btn:hover {
      color: var(--aiq-close-button-hover-icon-color);
    }

    #aiq-chat-messages {
      flex-grow: 1;
      padding: 15px 20px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    .aiq-chat-message {
      padding: 10px 15px;
      border-radius: var(--aiq-bubble-border-radius);
      max-width: 85%;
      word-wrap: break-word;
      font-size: var(--aiq-bubble-font-size);
      line-height: 1.45;
      box-sizing: border-box;
    }
    .aiq-user-message {
      background-image: var(--aiq-user-bubble-bg-image);
      background-color: var(--aiq-user-bubble-bg-color);
      color: var(--aiq-user-bubble-text-color);
      align-self: flex-end;
    }
    .aiq-agent-message {
      background-image: var(--aiq-agent-bubble-bg-image);
      background-color: var(--aiq-agent-bubble-bg-color);
      color: var(--aiq-agent-bubble-text-color);
      align-self: flex-start;
    }
    .aiq-message-tailed.aiq-user-message {
      border-bottom-right-radius: 5px;
    }
    .aiq-message-tailed.aiq-agent-message {
      border-bottom-left-radius: 5px;
    }

    .aiq-typing-indicator {
      align-self: flex-start;
      padding: 10px 15px; /* Ensures bubble padding */
      background-image: var(--aiq-agent-bubble-bg-image); /* Typing indicator uses agent style */
      background-color: var(--aiq-agent-bubble-bg-color); /* Typing indicator uses agent style */
      color: var(--aiq-agent-bubble-text-color);
      border-radius: var(--aiq-bubble-border-radius);
      font-size: calc(var(--aiq-bubble-font-size) * 0.95);
      font-style: italic;
      max-width: 70%;
      margin-bottom: 8px;
      box-sizing: border-box;
      line-height: 1.4; /* For text version */
      min-height: 20px; /* Ensure a minimum height for animations */
      display: flex; /* Helps center content if it's just text */
      align-items: center; /* Helps center content if it's just text */
    }
    .aiq-typing-indicator.dots {
        padding-top: 12px; /* Adjust padding for dots specifically if needed */
        padding-bottom: 12px;
    }
    .aiq-typing-indicator.dots span {
      display: inline-block;
      width: 7px;
      height: 7px;
      margin-right: 4px;
      background-color: currentColor;
      border-radius: 50%;
      animation: aiqTypingDotsPreview 1.4s infinite ease-in-out both;
    }
    .aiq-typing-indicator.dots span:nth-child(1) { animation-delay: -0.32s; }
    .aiq-typing-indicator.dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes aiqTypingDotsPreview { /* Renamed to avoid conflict if both scripts somehow load on one page */
      0%, 80%, 100% { transform: scale(0); }
      40% { transform: scale(1.0); }
    }

    .aiq-typing-indicator.wave {
      display: flex;
      align-items: center; /* Center vertically for better control */
      justify-content: center; /* Center horizontally */
      /* height: 20px; Remove fixed height */
      /* padding-top: 10px; Rely on base padding or adjust if needed */
      /* padding-bottom: 10px; */
    }
    .aiq-typing-indicator.wave span {
      display: inline-block;
      width: 5px;
      height: 5px;
      margin: 0 2px;
      background-color: currentColor;
      border-radius: 2px;
      animation: aiqTypingWavePreview 1.2s infinite ease-in-out; /* Renamed animation */
    }
    .aiq-typing-indicator.wave span:nth-child(1) { animation-delay: -0.4s; }
    .aiq-typing-indicator.wave span:nth-child(2) { animation-delay: -0.2s; }
    .aiq-typing-indicator.wave span:nth-child(3) { animation-delay: 0s; }

    @keyframes aiqTypingWavePreview { /* Renamed animation */
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-4px); } /* Further reduced upward movement */
    }

    #aiq-chat-input-container {
      display: flex;
      align-items: center; 
      padding: 12px 15px;
      background-color: var(--aiq-input-area-bg-color);
      box-sizing: border-box;
      flex-shrink: 0; 
    }
    #aiq-chat-input {
      flex-grow: 1;
      padding: 10px 15px;
      border: 1px solid var(--aiq-input-field-border-color);
      border-radius: var(--aiq-input-border-radius);
      background-color: transparent; 
      color: var(--aiq-input-text-color);
      font-size: var(--aiq-input-font-size);
      font-family: var(--aiq-font-family);
      outline: none;
      transition: border-color 0.2s ease;
      box-sizing: border-box;
    }
    #aiq-chat-input::placeholder {
      color: var(--aiq-input-placeholder-color);
      opacity: 1; 
    }
    #aiq-chat-input:focus {
      border-color: var(--aiq-input-field-focus-border-color);
    }
    #aiq-send-chat-btn {
      background-color: var(--aiq-send-button-bg-color);
      color: var(--aiq-send-button-icon-color); 
      border: none;
      width: var(--aiq-send-button-size);
      height: var(--aiq-send-button-size);
      border-radius: 50%;
      margin-left: 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease, transform 0.1s ease;
      padding:0;
      box-sizing: border-box;
      flex-shrink: 0;
    }
    #aiq-send-chat-btn:hover {
      filter: brightness(1.2); /* Default hover */
      transform: scale(1.05); /* Default hover scale */
    }
    #aiq-send-chat-btn:active {
      transform: scale(0.92); /* Default active scale */
      filter: brightness(0.9);
    }

    /* Animated Send Button Styles */
    #aiq-send-chat-btn.aiq-send-btn-animated {
      animation: aiqSendButtonPulsePreview 2s infinite ease-in-out;
    }
    #aiq-send-chat-btn.aiq-send-btn-animated:hover {
      transform: scale(1.12); /* More pronounced hover for animated version */
      filter: brightness(1.3);
    }
    #aiq-send-chat-btn.aiq-send-btn-animated:active {
      transform: scale(0.9); /* More pronounced active state for animated version */
      filter: brightness(0.8);
    }

    @keyframes aiqSendButtonPulsePreview { /* Renamed for preview */
      0% { box-shadow: 0 0 0 0 rgba(var(--aiq-send-button-bg-color-rgb), 0.5); }
      70% { box-shadow: 0 0 0 8px rgba(var(--aiq-send-button-bg-color-rgb), 0); }
      100% { box-shadow: 0 0 0 0 rgba(var(--aiq-send-button-bg-color-rgb), 0); }
    }
    
    #aiq-send-chat-btn svg {
      width: var(--aiq-send-button-icon-svg-size);
      height: var(--aiq-send-button-icon-svg-size);
      stroke: currentColor; 
      fill: currentColor;
    }

    #aiq-chat-messages::-webkit-scrollbar {
      width: var(--aiq-scrollbar-width);
    }
    #aiq-chat-messages::-webkit-scrollbar-track {
      background: var(--aiq-scrollbar-track-color);
      border-radius: calc(var(--aiq-scrollbar-width) / 2);
    }
    #aiq-chat-messages::-webkit-scrollbar-thumb {
      background: var(--aiq-scrollbar-thumb-color);
      border-radius: calc(var(--aiq-scrollbar-width) / 2);
    }
    #aiq-chat-messages::-webkit-scrollbar-thumb:hover {
      background: var(--aiq-scrollbar-thumb-hover-color);
    }
    #aiq-chat-messages {
        scrollbar-width: thin;
        scrollbar-color: var(--aiq-scrollbar-thumb-color) var(--aiq-scrollbar-track-color);
    }

    /* Animation classes */
    .aiq-fade-in { animation: aiqFadeInAnimation 0.3s ease forwards; }
    @keyframes aiqFadeInAnimation { from { opacity: 0; } to { opacity: 1; } }
    
    .aiq-slide-up-fast { animation: aiqSlideUpFastAnimation 0.3s ease forwards; }
    @keyframes aiqSlideUpFastAnimation {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .aiq-slide-in-right-fast { animation: aiqSlideInRightFastAnimation 0.3s ease forwards; }
    @keyframes aiqSlideInRightFastAnimation {
      from { opacity: 0; transform: translateX(20px); }
      to { opacity: 1; transform: translateX(0); }
    }

    .aiq-scale-up-center { animation: aiqScaleUpCenterAnimation 0.3s ease forwards; }
    @keyframes aiqScaleUpCenterAnimation {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }

    /* Message Entrance Animations */
    .aiq-slide-up-message { animation: aiqSlideUpMessageAnimation 0.3s ease forwards; }
    @keyframes aiqSlideUpMessageAnimation {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .aiq-scale-in-message { animation: aiqScaleInMessageAnimation 0.3s ease forwards; }
    @keyframes aiqScaleInMessageAnimation {
      from { opacity: 0; transform: scale(0.95); }
      to { opacity: 1; transform: scale(1); }
    }
  \`;

  function injectCss(css) {
    const styleSheet = document.createElement("style");
    styleSheet.type = "text/css";
    styleSheet.innerText = css;
    document.head.appendChild(styleSheet);
  }

  const defaultLoaderUISettings = {
    fontFamily: "Arial, sans-serif",
    headerText: "AIQ Assistant",
    // chatbotType: "FLOATING_WIDGET", // Not used by preview script, always embedded
    startupIcon: "default_chat_icon", 
    headerHeight: 60, 
    inputFontSize: 14, 
    widgetOpacity: 1, 
    bubbleFontSize: 14, 
    headerFontSize: 18, 
    headerTextColor: "#FFFFFF",
    // launcherOffsetX: 20, // Launcher specific
    // launcherOffsetY: 20, // Launcher specific
    sendButtonColor: "#AFFF3C", 
    // startupIconType: "PREDEFINED", // Launcher specific
    // launcherPosition: "BOTTOM_RIGHT", // Launcher specific
    messageAnimation: "FADE_IN",
    // startupIconColor: "#FFFFFF", // Launcher specific
    bubbleBorderRadius: 20, 
    // floatingWidgetSize: "TALL_NARROW", // Launcher specific
    inputAreaTextColor: "#FFFFFF", 
    windowBorderRadius: 30, 
    // customFloatingWidth: 350, // Launcher specific
    sendButtonIconColor: "#000000", 
    userBubbleTextColor: "#FFFFFF",
    // widgetOpenAnimation: "SLIDE_UP_FAST", // Launcher specific
    agentBubbleTextColor: "#000000",
    // customFloatingHeight: 550, // Launcher specific
    typingIndicatorStyle: "DOTS",
    headerBackgroundColor: "#32324E",
    inputAreaBackgroundColor: "rgba(40, 40, 60, 0.85)",
    inputFieldBorderColor: "rgba(128, 128, 128, 0.5)", 
    inputFieldFocusBorderColor: "#00FF00",
    messageEntranceAnimation: "FADE_IN",
    chatWindowBackgroundColor: "rgba(40, 40, 60, 0.85)",
    chatWindowEffect: "none", // 'none', 'frostedGlass', or 'floatingBubbles'
    enableHeaderGradient: false,
    enableUserBubbleGradient: false,
    enableAgentBubbleGradient: false,
    // enableLauncherGradient: false, // Not applicable to preview-only script
    // startupIconHoverAnimation: "SCALE", // Launcher specific
    userBubbleBackgroundColor: "#8A2BE2",
    agentBubbleBackgroundColor: "#AFFF3C",
    // startupIconBackgroundColor: "#8A2BE2", // Launcher specific
    // startupIconNotificationAnimation: "PULSE", // Launcher specific
    inputPlaceholder: "Type your message...",
    // launcherAriaLabel: "Open Chat", // Launcher specific
    embeddedWidth: "100%",
    embeddedHeight: "500px",
    bubbleTailStyle: "TAILED", 
  };

  function applyStyles(element, styles) {
    for (const property in styles) {
      if (styles.hasOwnProperty(property)) {
        element.style[property] = styles[property];
      }
    }
  }

  // getWidgetDimensions is not strictly needed if always embedded and using embeddedWidth/Height vars
  // but keeping it for now as uiSettings might still contain floating dimensions
  function getWidgetDimensions(settings) {
    if (settings.floatingWidgetSize === 'CUSTOM') {
      return { width: \`\${settings.customFloatingWidth || defaultLoaderUISettings.customFloatingWidth}px\`, height: \`\${settings.customFloatingHeight || defaultLoaderUISettings.customFloatingHeight}px\` };
    }
    switch (settings.floatingWidgetSize) {
      case 'SHORT_NARROW': return { width: '300px', height: '400px' };
      case 'TALL_NARROW': return { width: '300px', height: '550px' };
      case 'SHORT_WIDE': return { width: '400px', height: '400px' };
      case 'TALL_WIDE': return { width: '400px', height: '550px' };
      default: return { width: '300px', height: '550px' };
    }
  }

  function parseColor(colorString) {
    if (!colorString) {
      console.warn('[AIQ_Preview_Debug] parseColor received null or undefined. Defaulting to black.');
      return { r: 0, g: 0, b: 0, a: 1 };
    }
    if (colorString.startsWith('#')) {
        let hex = colorString.slice(1);
        if (hex.length === 3) {
            hex = hex.split('').map(char => char + char).join('');
        }
        if (hex.length === 6) {
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);
            return { r, g, b, a: 1 };
        }
    } else if (colorString.startsWith('rgb(')) {
        const parts = colorString.match(/[\d.]+/g);
        if (parts && parts.length === 3) {
            return { r: parseInt(parts[0]), g: parseInt(parts[1]), b: parseInt(parts[2]), a: 1 };
        }
    } else if (colorString.startsWith('rgba(')) {
        const parts = colorString.match(/[\d.]+/g);
        if (parts && parts.length === 4) {
            return { r: parseInt(parts[0]), g: parseInt(parts[1]), b: parseInt(parts[2]), a: parseFloat(parts[3]) };
        }
    } else if (colorString.toLowerCase() === 'transparent') {
        return { r: 0, g: 0, b: 0, a: 0 };
    }
    console.warn('[AIQ_Preview_Debug] Could not parse color:', colorString, '. Defaulting to black.');
    return { r: 0, g: 0, b: 0, a: 1 }; // Fallback for unparseable, non-transparent strings
  }

  function getSolidColorOrDefault(userColorStr, defaultColorStr) {
    console.log('[AIQ_Preview_Color_Debug] getSolidColorOrDefault - Input userColorStr:', userColorStr, 'Input defaultColorStr:', defaultColorStr);
    if (!userColorStr || typeof userColorStr !== 'string' || userColorStr.trim() === '') {
      console.log('[AIQ_Preview_Color_Debug] getSolidColorOrDefault: userColorStr is empty or invalid, returning default:', defaultColorStr);
      return defaultColorStr;
    }
    const parsedUserColor = parseColor(userColorStr);
    console.log('[AIQ_Preview_Color_Debug] getSolidColorOrDefault: userColorStr:', userColorStr, 'parsed:', JSON.stringify(parsedUserColor));

    if (parsedUserColor.a < 0.1) { // If alpha is very low, treat as transparent
      console.log('[AIQ_Preview_Color_Debug] getSolidColorOrDefault: userColor alpha (' + parsedUserColor.a + ') is < 0.1, returning default:', defaultColorStr);
      return defaultColorStr;
    }
    console.log('[AIQ_Preview_Color_Debug] getSolidColorOrDefault: userColor is sufficiently opaque (alpha: ' + parsedUserColor.a + '), returning userColorStr:', userColorStr);
    return userColorStr; // User's color is non-empty and sufficiently opaque
  }

  function adjustRgbColor(rgbColor, amount, lighten = false) {
    let { r, g, b, a } = rgbColor;
    const factor = lighten ? 1 + amount / 100 : 1 - amount / 100;
    
    r = Math.max(0, Math.min(255, Math.round(r * factor)));
    g = Math.max(0, Math.min(255, Math.round(g * factor)));
    b = Math.max(0, Math.min(255, Math.round(b * factor)));
    
    return \`rgba(\${r}, \${g}, \${b}, \${a})\`; // Preserve original alpha for the variant
  }
  
  const predefinedIcons = {
    default_chat_icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>',
    speech_bubble_1: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg>',
    help_icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>',
    send_icon: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path></svg>',
    close_icon: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>'
  };

  injectCss(baseCss);

  // Function to initialize and render the widget with given settings
  function initializeWidget(settings) { 
      console.log('AIQ Chatbot (Preview Script): Initializing with settings:', settings);
      const uiSettings = { ...defaultLoaderUISettings, ...(settings || {}) };
      const chatbotType = 'EMBEDDED_WINDOW'; // Always embedded for preview script
      
      console.log('AIQ Chatbot (Preview Script): Effective chatbotType for rendering:', chatbotType);

      // Clear previous widget if any (for re-initialization in preview)
      if (chatWidgetContainer && chatWidgetContainer.parentNode) {
        chatWidgetContainer.parentNode.removeChild(chatWidgetContainer);
        chatWidgetContainer = null;
      }

      chatWidgetContainer = document.createElement('div');
      chatWidgetContainer.id = 'aiq-chat-widget-container';
      
      const rootStyle = chatWidgetContainer.style; // CSS Vars set on chatWidgetContainer for preview script

      if (uiSettings.fontFamily) rootStyle.setProperty('--aiq-font-family', uiSettings.fontFamily);

      console.log('[AIQ_Preview_Debug] Initial chatWindowBackgroundColor from uiSettings:', uiSettings.chatWindowBackgroundColor);
      console.log('[AIQ_Preview_Debug] Initial chatWindowEffect from uiSettings:', uiSettings.chatWindowEffect);

      let finalChatWindowBgColor = uiSettings.chatWindowBackgroundColor || defaultLoaderUISettings.chatWindowBackgroundColor;
      console.log('[AIQ_Preview_Debug] Base finalChatWindowBgColor (after default fallback):', finalChatWindowBgColor);
      
      if (uiSettings.chatWindowEffect === 'frostedGlass') {
        console.log('[AIQ_Preview_Debug] Applying frostedGlass effect.');
        chatWidgetContainer.style.backdropFilter = 'blur(10px) saturate(180%)';
        chatWidgetContainer.style.webkitBackdropFilter = 'blur(10px) saturate(180%)';
        if (finalChatWindowBgColor.startsWith('#') && finalChatWindowBgColor.length === 7) {
            finalChatWindowBgColor = finalChatWindowBgColor + 'CC';
        } else if (finalChatWindowBgColor.startsWith('rgb(')) {
            finalChatWindowBgColor = finalChatWindowBgColor.replace('rgb(', 'rgba(').replace(')', ', 0.8)');
        }
      } else if (uiSettings.chatWindowEffect === 'floatingBubbles') {
        console.log('[AIQ_Preview_Debug] Applying floatingBubbles effect.');
        finalChatWindowBgColor = 'transparent';
        chatWidgetContainer.style.backdropFilter = 'none';
        chatWidgetContainer.style.webkitBackdropFilter = 'none';
        // chatWidgetContainer.style.boxShadow = 'none'; // Optional for preview
      } else { // 'none' or any other undefined effect
        console.log('[AIQ_Preview_Debug] Applying NO chatWindowEffect (solid color).');
        chatWidgetContainer.style.backdropFilter = 'none';
        chatWidgetContainer.style.webkitBackdropFilter = 'none';
      }
      console.log('[AIQ_Preview_Debug] finalChatWindowBgColor before setting CSS var:', finalChatWindowBgColor);
      rootStyle.setProperty('--aiq-chat-window-bg-color', finalChatWindowBgColor);
      console.log('[AIQ_Preview_Debug] CSS var --aiq-chat-window-bg-color SET TO:', finalChatWindowBgColor);
      
      // If frosted glass is active, or if solid color mode is chosen, widget opacity should be 1.
      if (uiSettings.chatWindowEffect === 'frostedGlass' || uiSettings.chatWindowEffect === 'none') {
        rootStyle.setProperty('--aiq-widget-opacity', '1');
        console.log('[AIQ_Preview_Debug] Effect is ' + uiSettings.chatWindowEffect + ', forcing --aiq-widget-opacity to 1.');
      } else {
        // This 'else' block might be for future effects that respect uiSettings.widgetOpacity differently.
        if (uiSettings.widgetOpacity !== undefined) {
          rootStyle.setProperty('--aiq-widget-opacity', uiSettings.widgetOpacity);
        } else {
          rootStyle.setProperty('--aiq-widget-opacity', defaultLoaderUISettings.widgetOpacity);
        }
      }
      console.log('[AIQ_Preview_Debug] CSS var --aiq-widget-opacity SET TO:', rootStyle.getPropertyValue('--aiq-widget-opacity'));

      if (uiSettings.windowBorderRadius !== undefined) rootStyle.setProperty('--aiq-window-border-radius', \`\${uiSettings.windowBorderRadius}px\`);
      
      let effectiveEmbeddedWidth = defaultLoaderUISettings.embeddedWidth || "100%";
      let effectiveEmbeddedHeight = defaultLoaderUISettings.embeddedHeight || "500px";

      // The preview script always renders as embedded.
      // If the uiSettings indicate the chatbot is conceptually FLOATING_WIDGET,
      // derive the preview's embedded dimensions from floatingWidgetSize.
      // Otherwise, use the explicit embeddedWidth/Height from uiSettings if provided.
      if (uiSettings.chatbotType === 'FLOATING_WIDGET') {
          const floatingDims = getWidgetDimensions(uiSettings); // This uses floatingWidgetSize
          effectiveEmbeddedWidth = floatingDims.width;
          effectiveEmbeddedHeight = floatingDims.height;
          console.log("AIQ Chatbot (Preview Script): Using floating dimensions for embedded preview:", effectiveEmbeddedWidth, effectiveEmbeddedHeight);
      } else { // Conceptually EMBEDDED_WINDOW or type not specified
          if (uiSettings.embeddedWidth) effectiveEmbeddedWidth = uiSettings.embeddedWidth;
          if (uiSettings.embeddedHeight) effectiveEmbeddedHeight = uiSettings.embeddedHeight;
          console.log("AIQ Chatbot (Preview Script): Using explicit embedded dimensions for preview:", effectiveEmbeddedWidth, effectiveEmbeddedHeight);
      }

      rootStyle.setProperty('--aiq-embedded-width', \`\${effectiveEmbeddedWidth}\`);
      rootStyle.setProperty('--aiq-embedded-height', \`\${effectiveEmbeddedHeight}\`);

      // These are less critical for embedded preview but set them for completeness
      // using the same logic as effectiveEmbeddedWidth/Height for consistency in preview.
      rootStyle.setProperty('--aiq-widget-width', \`\${effectiveEmbeddedWidth}\`);
      rootStyle.setProperty('--aiq-widget-height', \`\${effectiveEmbeddedHeight}\`);

      if (uiSettings.headerHeight !== undefined) rootStyle.setProperty('--aiq-header-height', \`\${uiSettings.headerHeight}px\`);
      if (uiSettings.headerFontSize !== undefined) rootStyle.setProperty('--aiq-header-font-size', \`\${uiSettings.headerFontSize}px\`);
      if (uiSettings.headerTextColor) rootStyle.setProperty('--aiq-header-text-color', uiSettings.headerTextColor);

      const hBgColorSetting = uiSettings.headerBackgroundColor;
      const defaultHBgColor = defaultLoaderUISettings.headerBackgroundColor;
      // CSS variables for header are set first.
      // Direct style manipulation will happen after chatHeader element is created.
      if (uiSettings.enableHeaderGradient) {
        const baseGradientColor = getSolidColorOrDefault(hBgColorSetting, defaultHBgColor);
        const parsedColor = parseColor(baseGradientColor);
        const adjustedColor = adjustRgbColor(parsedColor, 35);
        rootStyle.setProperty('--aiq-header-bg-image', \`linear-gradient(to bottom right, \${baseGradientColor}, \${adjustedColor})\`);
        rootStyle.setProperty('--aiq-header-bg-color', baseGradientColor);
      } else {
        const solidColor = getSolidColorOrDefault(hBgColorSetting, defaultHBgColor);
        rootStyle.setProperty('--aiq-header-bg-image', 'none');
        rootStyle.setProperty('--aiq-header-bg-color', solidColor);
      }

      if (uiSettings.bubbleFontSize !== undefined) rootStyle.setProperty('--aiq-bubble-font-size', \`\${uiSettings.bubbleFontSize}px\`);
      if (uiSettings.bubbleBorderRadius !== undefined) rootStyle.setProperty('--aiq-bubble-border-radius', \`\${uiSettings.bubbleBorderRadius}px\`);
      
      if (uiSettings.userBubbleTextColor) rootStyle.setProperty('--aiq-user-bubble-text-color', uiSettings.userBubbleTextColor);
      const uBubbleBgColorSetting = uiSettings.userBubbleBackgroundColor;
      const defaultUBubbleBgColor = defaultLoaderUISettings.userBubbleBackgroundColor;
      // CSS variables are set first. Direct style manipulation will happen in appendMessage.
      if (uiSettings.enableUserBubbleGradient) {
        const baseGradientColor = getSolidColorOrDefault(uBubbleBgColorSetting, defaultUBubbleBgColor);
        const parsedColor = parseColor(baseGradientColor);
        const adjustedColor = adjustRgbColor(parsedColor, 30);
        rootStyle.setProperty('--aiq-user-bubble-bg-image', \`linear-gradient(to bottom right, \${baseGradientColor}, \${adjustedColor})\`);
        rootStyle.setProperty('--aiq-user-bubble-bg-color', baseGradientColor);
      } else {
        const solidColor = getSolidColorOrDefault(uBubbleBgColorSetting, defaultUBubbleBgColor);
        rootStyle.setProperty('--aiq-user-bubble-bg-image', 'none');
        rootStyle.setProperty('--aiq-user-bubble-bg-color', solidColor);
      }

      if (uiSettings.agentBubbleTextColor) rootStyle.setProperty('--aiq-agent-bubble-text-color', uiSettings.agentBubbleTextColor);
      const aBubbleBgColorSetting = uiSettings.agentBubbleBackgroundColor;
      const defaultABubbleBgColor = defaultLoaderUISettings.agentBubbleBackgroundColor;
      // CSS variables are set first. Direct style manipulation will happen in appendMessage / showTypingIndicator.
      if (uiSettings.enableAgentBubbleGradient) {
        const baseGradientColor = getSolidColorOrDefault(aBubbleBgColorSetting, defaultABubbleBgColor);
        const parsedColor = parseColor(baseGradientColor);
        const adjustedColor = adjustRgbColor(parsedColor, 30);
        rootStyle.setProperty('--aiq-agent-bubble-bg-image', \`linear-gradient(to bottom right, \${baseGradientColor}, \${adjustedColor})\`);
        rootStyle.setProperty('--aiq-agent-bubble-bg-color', baseGradientColor);
      } else {
        const solidColor = getSolidColorOrDefault(aBubbleBgColorSetting, defaultABubbleBgColor);
        rootStyle.setProperty('--aiq-agent-bubble-bg-image', 'none');
        rootStyle.setProperty('--aiq-agent-bubble-bg-color', solidColor);
      }

      // Launcher gradient is not applicable to preview script as it doesn't create a launcher button.

      if (uiSettings.inputFontSize !== undefined) rootStyle.setProperty('--aiq-input-font-size', \`\${uiSettings.inputFontSize}px\`);
      if (uiSettings.inputAreaTextColor) rootStyle.setProperty('--aiq-input-text-color', uiSettings.inputAreaTextColor);
      if (uiSettings.inputAreaBackgroundColor) rootStyle.setProperty('--aiq-input-area-bg-color', uiSettings.inputAreaBackgroundColor);
      if (uiSettings.inputBorderRadius !== undefined) rootStyle.setProperty('--aiq-input-border-radius', \`\${uiSettings.inputBorderRadius}px\`);
      if (uiSettings.inputFieldBorderColor) rootStyle.setProperty('--aiq-input-field-border-color', uiSettings.inputFieldBorderColor);
      if (uiSettings.inputFieldFocusBorderColor) rootStyle.setProperty('--aiq-input-field-focus-border-color', uiSettings.inputFieldFocusBorderColor);
      
      const sendButtonBgColor = getSolidColorOrDefault(uiSettings.sendButtonColor, defaultLoaderUISettings.sendButtonColor);
      rootStyle.setProperty('--aiq-send-button-bg-color', sendButtonBgColor);
      try {
        const parsedSendBg = parseColor(sendButtonBgColor);
        if (parsedSendBg.a > 0) {
          rootStyle.setProperty('--aiq-send-button-bg-color-rgb', \`\${parsedSendBg.r},\${parsedSendBg.g},\${parsedSendBg.b}\`);
        } else {
          const defaultParsedSendBg = parseColor(defaultLoaderUISettings.sendButtonColor);
          rootStyle.setProperty('--aiq-send-button-bg-color-rgb', \`\${defaultParsedSendBg.r},\${defaultParsedSendBg.g},\${defaultParsedSendBg.b}\`);
        }
      } catch (e) {
        console.warn("AIQ_Preview_Debug: Could not parse sendButtonColor for RGB", sendButtonBgColor, e);
        const defaultParsedSendBg = parseColor(defaultLoaderUISettings.sendButtonColor);
        rootStyle.setProperty('--aiq-send-button-bg-color-rgb', \`\${defaultParsedSendBg.r},\${defaultParsedSendBg.g},\${defaultParsedSendBg.b}\`);
      }
      if (uiSettings.sendButtonIconColor) rootStyle.setProperty('--aiq-send-button-icon-color', uiSettings.sendButtonIconColor);

      const chatHeader = document.createElement('div');
      chatHeader.id = 'aiq-chat-header';
      const chatTitle = document.createElement('h2');
      chatTitle.id = 'aiq-chat-title';
      chatTitle.textContent = uiSettings.headerText || defaultLoaderUISettings.headerText;
      const closeChatBtn = document.createElement('button');
      closeChatBtn.id = 'aiq-close-chat-btn';
      closeChatBtn.setAttribute('aria-label', 'Close Chat');
      closeChatBtn.innerHTML = predefinedIcons.close_icon || '&#x2715;';
      // In embedded preview, close button might be non-functional or hidden.
      // For now, it's created but has no click handler assigned here.
      // To hide: closeChatBtn.style.visibility = 'hidden';
      chatHeader.appendChild(chatTitle);
      chatHeader.appendChild(closeChatBtn);
      chatWidgetContainer.appendChild(chatHeader);

      // Apply direct styles to header
      if (!uiSettings.enableHeaderGradient) {
        const solidColor = getSolidColorOrDefault(uiSettings.headerBackgroundColor, defaultLoaderUISettings.headerBackgroundColor);
        chatHeader.style.backgroundImage = 'none';
        chatHeader.style.backgroundColor = solidColor;
        console.log('[AIQ_Preview_DirectStyle] Header direct style - BG color:', solidColor);
      } else {
        // Ensure direct styles are cleared if gradient is on, so CSS vars from rootStyle take over for the element
        chatHeader.style.backgroundImage = '';
        chatHeader.style.backgroundColor = '';
      }

      const chatMessagesDiv = document.createElement('div');
      chatMessagesDiv.id = 'aiq-chat-messages';
      chatWidgetContainer.appendChild(chatMessagesDiv);

      const chatInputContainer = document.createElement('div');
      chatInputContainer.id = 'aiq-chat-input-container';
      const chatInput = document.createElement('input');
      chatInput.id = 'aiq-chat-input';
      chatInput.type = 'text';
      chatInput.placeholder = uiSettings.inputPlaceholder || defaultLoaderUISettings.inputPlaceholder;
      const sendChatBtn = document.createElement('button');
      sendChatBtn.id = 'aiq-send-chat-btn';
      sendChatBtn.setAttribute('aria-label', 'Send Message');
      sendChatBtn.innerHTML = predefinedIcons.send_icon || 'Send';
      if (uiSettings.enableSendButtonAnimation) {
        sendChatBtn.classList.add('aiq-send-btn-animated');
      } else {
        sendChatBtn.classList.remove('aiq-send-btn-animated');
      }
      chatInputContainer.appendChild(chatInput);
      chatInputContainer.appendChild(sendChatBtn);
      chatWidgetContainer.appendChild(chatInputContainer);

      let messageHistory = [];
      let typingIndicatorElement = null;
      let currentThreadId = null; // NEW: Initialize currentThreadId

      function showTypingIndicator(show) {
        if (typingIndicatorElement && typingIndicatorElement.parentNode) {
          typingIndicatorElement.parentNode.removeChild(typingIndicatorElement);
          typingIndicatorElement = null;
        }

        if (show) {
          typingIndicatorElement = document.createElement('div');
          typingIndicatorElement.classList.add('aiq-typing-indicator', 'aiq-agent-message');
          if (uiSettings.bubbleTailStyle === "TAILED") {
            typingIndicatorElement.classList.add('aiq-message-tailed');
          }

          if (uiSettings.typingIndicatorStyle === 'DOTS') {
            typingIndicatorElement.classList.add('dots');
            typingIndicatorElement.innerHTML = '<span></span><span></span><span></span>';
          } else if (uiSettings.typingIndicatorStyle === 'WAVE') {
            typingIndicatorElement.classList.add('wave');
            // Assuming 3 spans for the wave animation, matching the CSS.
            // Adjust if your final wave design requires a different structure.
            typingIndicatorElement.innerHTML = '<span></span><span></span><span></span>';
          } else { // Default to TEXT (handles 'TEXT' or any other unspecified value)
            const agentName = uiSettings.headerText ? uiSettings.headerText.split(' ')[0] : 'Agent';
            typingIndicatorElement.textContent = agentName + ' is typing...';
          }
          
          // Apply direct styles to typing indicator if agent bubble gradient is off
          if (!uiSettings.enableAgentBubbleGradient) {
            const solidColor = getSolidColorOrDefault(uiSettings.agentBubbleBackgroundColor, defaultLoaderUISettings.agentBubbleBackgroundColor);
            typingIndicatorElement.style.backgroundImage = 'none';
            typingIndicatorElement.style.backgroundColor = solidColor;
            console.log('[AIQ_Preview_DirectStyle] Typing indicator direct style - BG color:', solidColor);
          } else {
            // Clear direct styles if gradient is on
            typingIndicatorElement.style.backgroundImage = '';
            typingIndicatorElement.style.backgroundColor = '';
          }
          chatMessagesDiv.appendChild(typingIndicatorElement);
          chatMessagesDiv.scrollTop = chatMessagesDiv.scrollHeight;
        }
      }

      function appendMessage(text, type) {
        const messageEl = document.createElement('div');
        messageEl.classList.add('aiq-chat-message', type === 'user' ? 'aiq-user-message' : 'aiq-agent-message');
        if (uiSettings.bubbleTailStyle === "TAILED") {
            messageEl.classList.add('aiq-message-tailed');
        }
        messageEl.textContent = text;
        const entranceAnimationClass = getAnimationClassName(uiSettings.messageEntranceAnimation);
        if (entranceAnimationClass) {
            messageEl.classList.add(entranceAnimationClass);
            messageEl.addEventListener('animationend', () => messageEl.classList.remove(entranceAnimationClass), { once: true });
        }

        // Apply direct styles to message bubbles if their respective gradients are off
        if (type === 'user') {
            if (!uiSettings.enableUserBubbleGradient) {
                const solidColor = getSolidColorOrDefault(uiSettings.userBubbleBackgroundColor, defaultLoaderUISettings.userBubbleBackgroundColor);
                messageEl.style.backgroundImage = 'none';
                messageEl.style.backgroundColor = solidColor;
                console.log('[AIQ_Preview_DirectStyle] User bubble direct style - BG color:', solidColor);
            } else {
                // Clear direct styles if gradient is on
                messageEl.style.backgroundImage = '';
                messageEl.style.backgroundColor = '';
            }
        } else if (type === 'agent') {
            if (!uiSettings.enableAgentBubbleGradient) { // Covers actual agent messages
                const solidColor = getSolidColorOrDefault(uiSettings.agentBubbleBackgroundColor, defaultLoaderUISettings.agentBubbleBackgroundColor);
                messageEl.style.backgroundImage = 'none';
                messageEl.style.backgroundColor = solidColor;
                console.log('[AIQ_Preview_DirectStyle] Agent bubble direct style - BG color:', solidColor);
            } else {
                // Clear direct styles if gradient is on
                messageEl.style.backgroundImage = '';
                messageEl.style.backgroundColor = '';
            }
        }
        chatMessagesDiv.appendChild(messageEl);
        chatMessagesDiv.scrollTop = chatMessagesDiv.scrollHeight;
      }

      async function handleSendMessage() {
        const messageText = chatInput.value.trim();
        if (!messageText) return;

        appendMessage(messageText, 'user');
        // Do not add user message to history here, it's included in the 'messages' payload to API
        chatInput.value = '';
        chatInput.disabled = true;
        sendChatBtn.disabled = true;
        
        showTypingIndicator(true);

        const messagesForApi = messageHistory.map(m => ({ role: m.role, content: m.content }));
        messagesForApi.push({role: 'user', content: messageText});

        try {
          const response = await fetch(\`\${"${appUrl}"}/api/chat-public\`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              messages: messagesForApi,
              data: {
                centralConfigId: chatbotId,
                threadId: currentThreadId // MODIFIED: Send currentThreadId
              }
            }),
          });

          // NEW: Read header and update currentThreadId
          const serverThreadId = response.headers.get('X-Chat-Thread-ID');
          if (serverThreadId) { // Check if the header exists
              if (!currentThreadId || currentThreadId !== serverThreadId) { // Update if not set or different
                  currentThreadId = serverThreadId;
                  console.log('AIQ Chatbot (Preview Script) Client: Updated/set threadId to:', currentThreadId);
              }
          }
          // END NEW

          showTypingIndicator(false);

          if (!response.ok) {
            const errorText = await response.text();
            console.error("AIQ Chatbot (Preview Script): API Error Response Text:", errorText);
            let errorData;
            try {
                errorData = JSON.parse(errorText);
            } catch (e) {
                errorData = { message: errorText || 'Unknown API error (non-JSON response)' };
            }
            throw new Error(errorData.message || \`API Error: \${response.status}\`);
          }
          
          if (response.body) {
            const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
            let accumulatedResponse = "";
            let firstChunk = true;
            
            messageHistory.push({ role: 'user', content: messageText });

            while (true) {
              const { value, done } = await reader.read();
              if (done) {
                if (accumulatedResponse) {
                     messageHistory.push({ role: 'assistant', content: accumulatedResponse });
                }
                break;
              }
              const chunkLines = value.split('\\n');
              for (const line of chunkLines) {
                if (line.startsWith('0:')) {
                  try {
                    const chunkContent = JSON.parse(line.substring(2));
                    if (firstChunk) {
                      appendMessage(chunkContent, 'agent');
                      accumulatedResponse = chunkContent;
                      firstChunk = false;
                    } else {
                      const lastMessageEl = chatMessagesDiv.querySelector('.aiq-agent-message:last-child');
                      if (lastMessageEl) {
                        lastMessageEl.textContent += chunkContent;
                        accumulatedResponse += chunkContent;
                        chatMessagesDiv.scrollTop = chatMessagesDiv.scrollHeight;
                      }
                    }
                  } catch (e) {
                    console.warn("AIQ Chatbot (Preview Script): Error parsing stream chunk", line, e);
                  }
                }
              }
            }
          } else {
            const botResult = await response.json();
            const botResponseText = botResult.reply || botResult.text || "Sorry, I received an unexpected response.";
            appendMessage(botResponseText, 'agent');
            messageHistory.push({ role: 'user', content: messageText });
            messageHistory.push({ role: 'assistant', content: botResponseText });
          }

        } catch (error) {
          showTypingIndicator(false);
          console.error("AIQ Chatbot (Preview Script): Error sending message or processing response", error);
          appendMessage(error.message || "Sorry, an error occurred while connecting.", 'agent');
          const userMessageExists = messageHistory.some(m => m.role === 'user' && m.content === messageText);
          if (!userMessageExists) {
            messageHistory.push({ role: 'user', content: messageText });
          }
          messageHistory.push({ role: 'assistant', content: error.message || "Connection error" });
        } finally {
          chatInput.disabled = false;
          sendChatBtn.disabled = false;
          chatInput.focus();
        }
      }

      sendChatBtn.onclick = handleSendMessage;
      chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleSendMessage();
      });
      
      function getAnimationClassName(animationName) {
        if (!animationName || animationName === "NONE" || animationName === "") return '';
        return \`aiq-\${animationName.toLowerCase().replace(/_/g, '-')}\`;
      }
      
      // Simplified widget display logic for always embedded
      chatWidgetContainer.classList.add('aiq-embedded');
      chatWidgetContainer.style.display = 'flex'; // Embedded is visible by default

      if (container) {
          container.innerHTML = ''; // Clear the user-provided container
          container.appendChild(chatWidgetContainer);
          console.log('AIQ Chatbot (Preview Script): Embedded widget (DOM) appended to container.');
          // Apply open animation if specified, now that it's in DOM
          const openAnimationClass = getAnimationClassName(uiSettings.widgetOpenAnimation); // Though widgetOpenAnimation is more for floating
          if (openAnimationClass) {
              chatWidgetContainer.classList.add(openAnimationClass);
              chatWidgetContainer.addEventListener('animationend', () => chatWidgetContainer.classList.remove(openAnimationClass), { once: true });
          } else {
              chatWidgetContainer.style.opacity = '1'; // Ensure visible if no animation
          }
          chatInput.focus();
      } else {
          console.error('AIQ Chatbot (Preview Script): Container not found for appending embedded widget.');
      }
  } // End of initializeWidget function definition

  // This script is for preview only. It relies on the parent page (builder) to call reinitializeAiqChatWidget.
  console.log("AIQ Chatbot (Preview Script): Ready. Waiting for reinitializeAiqChatWidget to be called by the builder page.");
  // Optional: Display a placeholder if the container is empty and no error has been shown yet.
  // if (container && !container.hasChildNodes() && !container.textContent.includes("Error")) {
  //   const placeholder = document.createElement('p');
  //   placeholder.textContent = 'Loading preview settings...';
  //   placeholder.style.cssText = 'color:gray; font-family:sans-serif; text-align:center; padding-top:20px;';
  //   container.appendChild(placeholder);
  // }

  window.reinitializeAiqChatWidget = function(newSettings) {
    console.log("AIQ Chatbot (Preview Script): Re-initializing with new preview settings:", newSettings);
    if (!newSettings || typeof newSettings !== 'object') {
        console.error('AIQ Chatbot (Preview Script): Invalid or missing settings passed to reinitializeAiqChatWidget.');
        if (container) {
            // Clear previous content before showing error
            container.innerHTML = '<p style="color:red; font-family:sans-serif;">Preview Error: Invalid settings provided for re-initialization.</p>';
        }
        return;
    }
    // Store the settings on the window object for easier debugging or if other parts expect it.
    window.aiqChatPreviewSettings = newSettings;
    initializeWidget(newSettings); // This function will clear the container and build the widget
  };

})();
  `;

  return new NextResponse(scriptContent, {
    headers: {
      'Content-Type': 'application/javascript',
      'Cache-Control': 'public, max-age=60, stale-while-revalidate=300', 
    },
  });
}