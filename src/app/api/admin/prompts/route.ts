import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const promptSnippets = await prisma.promptSnippet.findMany();
    return NextResponse.json(promptSnippets);
  } catch (error) {
    console.error('Error fetching prompt snippets:', error);
    return NextResponse.json({ message: 'Failed to fetch prompt snippets' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const { id, name, content, type, isActive } = await request.json();

    if (!id) {
      return NextResponse.json({ message: 'Prompt snippet ID is required for update' }, { status: 400 });
    }

    const updatedSnippet = await prisma.promptSnippet.update({
      where: { id },
      data: { name, content, type, isActive },
    });

    return NextResponse.json(updatedSnippet);
  } catch (error) {
    console.error('Error updating prompt snippet:', error);
    return NextResponse.json({ message: 'Failed to update prompt snippet' }, { status: 500 });
  }
}