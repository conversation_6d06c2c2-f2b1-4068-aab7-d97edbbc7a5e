import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { slugify } from "@/lib/utils";

export async function GET() {
  try {
    const categories = await prisma.blogCategory.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name } = await request.json();
    
    if (!name) {
      return NextResponse.json(
        { message: "Category name is required" },
        { status: 400 }
      );
    }
    
    const slug = slugify(name);
    
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { slug },
    });
    
    if (existingCategory) {
      return NextResponse.json(
        { message: "Category with this slug already exists" },
        { status: 400 }
      );
    }
    
    const newCategory = await prisma.blogCategory.create({
      data: { name, slug },
    });
    
    return NextResponse.json(newCategory, { status: 201 });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, name } = await request.json();
    
    if (!id || !name) {
      return NextResponse.json(
        { message: "ID and name are required" },
        { status: 400 }
      );
    }
    
    const slug = slugify(name);
    
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { slug },
    });
    
    if (existingCategory && existingCategory.id !== id) {
      return NextResponse.json(
        { message: "Category with this slug already exists" },
        { status: 400 }
      );
    }
    
    const updatedCategory = await prisma.blogCategory.update({
      where: { id },
      data: { name, slug },
    });
    
    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { message: "Category ID is required" },
        { status: 400 }
      );
    }
    
    // Check if any posts are using this category
    const postsCount = await prisma.blogPost.count({
      where: { categoryId: id },
    });
    
    if (postsCount > 0) {
      return NextResponse.json(
        { message: "Cannot delete category with associated posts" },
        { status: 400 }
      );
    }
    
    await prisma.blogCategory.delete({
      where: { id },
    });
    
    return NextResponse.json(
      { message: "Category deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
