import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { slugify } from "@/lib/utils";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const post = await prisma.blogPost.findUnique({
      where: { id: params.id },
      include: {
        category: true,
      },
    });
    
    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(post);
  } catch (error) {
    console.error("Error fetching post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { 
      title, 
      excerpt, 
      content, 
      status, 
      categoryId, 
      metaTitle, 
      metaDescription 
    } = await request.json();
    
    if (!title || !content || !status || !categoryId) {
      return NextResponse.json(
        { message: "Title, content, status, and category are required" },
        { status: 400 }
      );
    }
    
    const slug = slugify(title);
    
    const existingPost = await prisma.blogPost.findFirst({
      where: {
        slug,
        id: { not: params.id }
      },
    });
    
    if (existingPost) {
      return NextResponse.json(
        { message: "Another post with this slug already exists" },
        { status: 400 }
      );
    }
    
    const updatedPost = await prisma.blogPost.update({
      where: { id: params.id },
      data: { 
        title,
        slug,
        excerpt: excerpt || null,
        content,
        status,
        categoryId,
        metaTitle: metaTitle || null,
        metaDescription: metaDescription || null
      },
    });
    
    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error("Error updating post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await prisma.blogPost.delete({
      where: { id: params.id },
    });
    
    return NextResponse.json(
      { message: "Post deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
