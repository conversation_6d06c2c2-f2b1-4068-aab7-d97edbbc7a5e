import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { slugify } from "@/lib/utils";

export async function GET(request: NextRequest) {
  try {
    const posts = await prisma.blogPost.findMany({
      include: {
        category: true,
      },
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(posts);
  } catch (error) {
    console.error("Error fetching posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { 
      title, 
      excerpt, 
      content, 
      status, 
      categoryId, 
      metaTitle, 
      metaDescription 
    } = await request.json();
    
    if (!title || !content || !status || !categoryId) {
      return NextResponse.json(
        { message: "Title, content, status, and category are required" },
        { status: 400 }
      );
    }
    
    const slug = slugify(title);
    
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug },
    });
    
    if (existingPost) {
      return NextResponse.json(
        { message: "Post with this slug already exists" },
        { status: 400 }
      );
    }
    
    const newPost = await prisma.blogPost.create({
      data: { 
        title,
        slug,
        excerpt: excerpt || null,
        content,
        status,
        categoryId,
        metaTitle: metaTitle || null,
        metaDescription: metaDescription || null
      },
    });
    
    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error("Error creating post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
