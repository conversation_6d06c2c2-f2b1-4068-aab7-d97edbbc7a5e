// src/app/api/chat-public/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma'; // Adjust path if necessary
import { ChatbotStatus } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

export const dynamic = 'force-dynamic';

const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*', // Adjust for production as needed
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Accept',
  'Access-Control-Expose-Headers': 'X-Chat-Thread-ID', // Allow client to read this custom header
};

export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, { status: 204, headers: CORS_HEADERS });
}

function formatVercelChunk(data: string): string {
  return `0:${JSON.stringify(data)}\n`;
}

// Helper to safely extract text from various possible response structures
function extractTextFromData(jsonData: any): string | null {
  // Debug: Log the structure we're working with
  if (process.env.NODE_ENV === 'development') {
    // console.log('[Stream Parser Debug] Raw JSON data:', JSON.stringify(jsonData, null, 2));
  }

  const possiblePaths = [
    'data.chunk.llm_response_text',
    'data.output.llm_response_text',
    'data.chunk.content',
    'data.chunk.text',
    'data.output.content',
    'data.output.text',
    'data.chunk',
    'data.output',
    'content',
    'text',
    'message',
    'response',
    'data.chunk.message.content',
    'data.output.message.content',
    'data.chunk.message.text',
    'data.output.message.text',
  ];

  for (const path of possiblePaths) {
    const value = getNestedValue(jsonData, path);
    if (typeof value === 'string' && value.trim()) {
      // console.log(`[Stream Parser] Found text at path: ${path}`);
      return value;
    }
  }
  return null;
}

// Helper to get nested object values by string path
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// Simple hash function for content deduplication
function hashContent(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString();
}

// Helper to check if this is a meaningful streaming event
function shouldProcessEvent(jsonData: any): boolean {
  const eventName = jsonData.event ? String(jsonData.event).trim() : '';
  const nodeName = jsonData.name ? String(jsonData.name).trim() : '';
  
  if (process.env.NODE_ENV === 'development') {
    // console.log(`[Stream Parser Debug] Event: "${eventName}", Node: "${nodeName}"`);
  }

  if (eventName.includes('stream') || eventName.includes('chunk')) return true;
  if (eventName.includes('end') || eventName.includes('finish')) return true;
  if (nodeName.includes('llm') || nodeName.includes('chat') || nodeName.includes('agent')) return true;
  if (extractTextFromData(jsonData)) return true;
  
  return false;
}

export async function POST(req: NextRequest) {
  let threadId: string | undefined; // Initialize as potentially undefined

  try {
    const body = await req.json();
    const { messages, data } = body;
    const centralConfigId = data?.centralConfigId;

    if (data?.threadId && typeof data.threadId === 'string' && data.threadId.trim() !== '') {
        threadId = data.threadId;
    } else {
        threadId = uuidv4(); 
        console.log(`[API Chat Public] New Thread ID generated: ${threadId}`);
    }

    const responseHeaders: Record<string, string> = { ...CORS_HEADERS };
    if (threadId) {
        responseHeaders['X-Chat-Thread-ID'] = threadId;
    }

    if (!centralConfigId || typeof centralConfigId !== 'string') {
      return NextResponse.json({ message: 'Missing or invalid centralConfigId' }, { status: 400, headers: responseHeaders });
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ message: 'Missing or invalid messages payload' }, { status: 400, headers: responseHeaders });
    }

    try {
      const chatbotInstance = await prisma.chatbotInstance.findUnique({
        where: { id: centralConfigId },
        select: { status: true },
      });
      if (!chatbotInstance) {
        return NextResponse.json({ message: 'Chatbot configuration not found.' }, { status: 404, headers: responseHeaders });
      }
      if (chatbotInstance.status !== ChatbotStatus.PUBLISHED) {
        return NextResponse.json({ message: 'This chatbot is not currently published.' }, { status: 403, headers: responseHeaders });
      }
    } catch (dbError) {
      console.error('Error verifying chatbot status:', dbError);
      return NextResponse.json({ message: 'Could not verify chatbot status.' }, { status: 500, headers: responseHeaders });
    }

    const centralApiServiceUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
    if (!centralApiServiceUrl) {
      console.error('NEXT_PUBLIC_CENTRAL_API_URL is not set');
      return NextResponse.json({ message: 'Chat service is not configured' }, { status: 503, headers: responseHeaders });
    }
    
    const targetUrl = `${centralApiServiceUrl}/api/v1/lg_chat/agent/stream_events`;
    
    console.log(`[API Chat Public] Forwarding request to: ${targetUrl}`);
    console.log(`[API Chat Public] Using Thread ID: ${threadId}`);

    const pythonResponse = await fetch(targetUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        input: {
          config_id: centralConfigId,
          thread_id: threadId,
          messages: messages.map((m: any) => ({ type: m.role, content: m.content })),
        },
        config: {
          configurable: {
            thread_id: threadId
          }
        }
      }),
    });

    if (!pythonResponse.ok) {
      const errorBody = await pythonResponse.text();
      console.error(`Error from Python service (${pythonResponse.status}): ${errorBody}`);
      return NextResponse.json({ 
        message: `Error from chat engine: ${pythonResponse.statusText}`, 
        detail: errorBody 
      }, { status: pythonResponse.status, headers: responseHeaders });
    }
    
    if (pythonResponse.body) {
      console.log('[API Chat Public] Python service returned a stream. Setting up transform.');
      const textDecoderStream = new TextDecoderStream();
      const readableStream = pythonResponse.body.pipeThrough(textDecoderStream);
      
      const customTransformStream = new TransformStream<string, string>({
        async start(controller) {
          console.log('[API Chat Public Stream Parser] TransformStream started.');
          const reader = readableStream.getReader();
          let totalEventsProcessed = 0;
          let textChunksSent = 0;
          let lastEventType = '';
          let accumulatedContent = '';
          let buffer = ''; 
          let sentContentHashes = new Set<string>();
          let hasStreamedContent = false;

          try {
            while (true) {
              const { value, done } = await reader.read();
              if (done) {
                console.log(`[API Chat Public Stream Parser] Stream finished. Events processed: ${totalEventsProcessed}, Text chunks sent: ${textChunksSent}`);
                console.log(`[API Chat Public Stream Parser] Last event type: ${lastEventType}`);
                
                if (buffer.trim()) {
                  console.log('[API Chat Public Stream Parser] Processing remaining buffer content');
                  processBufferContent(buffer); // Call processBufferContent, don't assign its result to buffer here
                }
                
                if (accumulatedContent && textChunksSent === 0) {
                  const cleanContent = accumulatedContent.replace(/<think>[\s\S]*?<\/think>\s*\n*/g, '').trim();
                  if (cleanContent) {
                    const contentHash = hashContent(cleanContent);
                    if (!sentContentHashes.has(contentHash)) {
                      console.log('[API Chat Public Stream Parser] Sending accumulated content as fallback');
                      controller.enqueue(formatVercelChunk(cleanContent));
                      sentContentHashes.add(contentHash);
                      textChunksSent++;
                    }
                  }
                }
                
                if (textChunksSent === 0) {
                  console.warn('[API Chat Public Stream Parser] No text content was extracted and sent during the stream.');
                }
                controller.terminate();
                break;
              }

              buffer += value;
              const unprocessedAfterProcessing = processBufferContent(buffer); // Process and get remaining
              buffer = unprocessedAfterProcessing; // Update buffer with unprocessed part
            }

            function processBufferContent(currentBuffer: string): string {
              const normalizedContent = currentBuffer.replace(/\r\n/g, '\n');
              let unprocessedPortion = '';
              
              const parts = normalizedContent.split('\n\n');
              
              if (parts.length > 1 && !normalizedContent.endsWith('\n\n')) { // If there's at least one complete block and the content doesn't end with separator
                unprocessedPortion = parts.pop() || '';
              } else if (parts.length === 1 && !normalizedContent.endsWith('\n\n')) { // Only one part, and it might be incomplete
                 return currentBuffer; // Keep everything in buffer
              } else if (parts.length > 1 && normalizedContent.endsWith('\n\n')) { // Ends with separator, all parts are complete
                // No unprocessed portion if it ends with \n\n
              } else if (parts.length === 1 && normalizedContent.endsWith('\n\n')) {
                // Single complete message, process it
              }


              const completePartsToProcess = (normalizedContent.endsWith('\n\n')) ? parts.filter(p => p.trim() !== '') : parts.slice(0, -1).filter(p => p.trim() !== '');


              for (const sseMessageBlock of completePartsToProcess) {
                if (sseMessageBlock.trim() === '') continue;
                
                const messageLines = sseMessageBlock.split('\n');
                for (const dataLine of messageLines) {
                  if (dataLine.startsWith('data: ')) {
                    const jsonDataString = dataLine.substring(5).trim();
                    if (jsonDataString && jsonDataString !== '[DONE]') {
                      try {
                        const jsonData = JSON.parse(jsonDataString);
                        totalEventsProcessed++;
                        lastEventType = jsonData.event || 'unknown';

                        if (shouldProcessEvent(jsonData)) {
                          const extractedText = extractTextFromData(jsonData);
                          
                          if (extractedText) {
                            // console.log(`[API Chat Public Stream Parser] Extracted text from event "${lastEventType}": "${extractedText.substring(0, 100)}..."`);
                            const userFacingText = extractedText.replace(/<think>[\s\S]*?<\/think>\s*\n*/g, '').trim();
                            
                            if (userFacingText) {
                              const contentHash = hashContent(userFacingText);
                              
                              if (!sentContentHashes.has(contentHash)) {
                                const isStreamingChunk = lastEventType.includes('stream') || lastEventType.includes('chunk');
                                
                                if (isStreamingChunk) {
                                  const formattedChunk = formatVercelChunk(userFacingText);
                                  controller.enqueue(formattedChunk);
                                  sentContentHashes.add(contentHash);
                                  textChunksSent++;
                                  hasStreamedContent = true;
                                  // console.log(`[API Chat Public Stream Parser] Sent streaming chunk (${textChunksSent})`);
                                } else if (!hasStreamedContent) {
                                  const formattedChunk = formatVercelChunk(userFacingText);
                                  controller.enqueue(formattedChunk);
                                  sentContentHashes.add(contentHash);
                                  textChunksSent++;
                                  // console.log(`[API Chat Public Stream Parser] Sent final content (${textChunksSent})`);
                                } else {
                                  // console.log(`[API Chat Public Stream Parser] Skipping final content - already streamed`);
                                }
                              } else {
                                // console.log(`[API Chat Public Stream Parser] Skipping duplicate content`);
                              }
                            }
                            
                            accumulatedContent += extractedText; // Accumulate original extracted text
                          }
                        }
                      } catch (e: any) {
                        if (process.env.NODE_ENV === 'development') {
                          console.warn(`[API Chat Public Stream Parser] Error processing SSE data: ${e.message}`);
                          console.warn(`[API Chat Public Stream Parser] Problematic data: ${jsonDataString.substring(0, 200)}...`);
                        }
                      }
                    }
                  }
                }
              }
              return unprocessedPortion;
            }
          } catch (error: any) {
            console.error('[API Chat Public Stream Parser] Error in reader loop:', error.message);
            controller.error(error);
          } finally {
            reader.releaseLock();
          }
        }
      });
      
      const streamingResponseHeaders = {
        ...responseHeaders,
        'Content-Type': 'text/plain; charset=utf-8',
        'X-Experimental-Stream-Data': 'true',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      };
      return new Response(customTransformStream.readable, { headers: streamingResponseHeaders });

    } else {
      console.log('[API Chat Public] Python service did not return a stream. Processing as JSON.');
      const responseData = await pythonResponse.json();
      return NextResponse.json(responseData, { headers: responseHeaders });
    }

  } catch (error: any) {
    console.error('Error in public chat API proxy:', error);
    
    const errorResponseHeaders: Record<string, string> = { ...CORS_HEADERS };
    if (threadId) { 
        errorResponseHeaders['X-Chat-Thread-ID'] = threadId;
    }
    
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ 
      message: 'Failed to process chat request', 
      error: errorMessage 
    }, { status: 500, headers: errorResponseHeaders });
  }
}