import { NextResponse, NextRequest } from 'next/server'; // Import NextRequest
import prisma from '@/lib/prisma';

export async function GET(
  request: NextRequest, // Type request as NextRequest
  { params }: { params: { id: string } }
) {
  const id = (await params).id;

  if (!id) {
    return NextResponse.json({ message: 'Chatbot ID is required' }, { status: 400, headers: { 'Access-Control-Allow-Origin': '*' } });
  }

  try {
    const chatbotInstance = await prisma.chatbotInstance.findUnique({
      where: { id },
      select: {
        name: true,
        uiSettings: true,
        status: true,
        type: true,
      },
    });

    if (!chatbotInstance) {
      return NextResponse.json({ message: 'Chatbot not found' }, { status: 404, headers: { 'Access-Control-Allow-Origin': '*' } });
    }

    if (chatbotInstance.status !== 'PUBLISHED') {
      // It's important to return a 403 Forbidden if the chatbot isn't published,
      // but for testing purposes with the widget, you might temporarily comment this out
      // or ensure your test chatbot instance is set to 'PUBLISHED'.
      // For now, let's keep the check active as it's correct for production.
      return NextResponse.json({ message: 'Chatbot is not published' }, { status: 403, headers: { 'Access-Control-Allow-Origin': '*' } });
    }

    const publicConfig = {
      name: chatbotInstance.name,
      uiSettings: chatbotInstance.uiSettings,
      type: chatbotInstance.type,
    };

    return NextResponse.json(publicConfig, { headers: { 'Access-Control-Allow-Origin': '*' } });
  } catch (error) {
    console.error(`Error fetching public config for chatbot ${id}:`, error);
    let errorMessage = 'An unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ message: 'Failed to fetch chatbot public configuration', error: errorMessage }, { status: 500, headers: { 'Access-Control-Allow-Origin': '*' } });
  }
}