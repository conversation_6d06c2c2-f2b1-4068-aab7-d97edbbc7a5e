// src/app/api/chatbot-instances/[id]/route.ts
import { NextResponse, NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { auth } from '../../../../../auth';
import { Pool } from 'pg'; // Added for RAG DB connection

export const dynamic = 'force-dynamic';

// RAG DB Connection Pool
// Ensure these environment variables are set in your .env.local or environment
const ragDbPool = new Pool({
  user: process.env.RAG_DB_USER || 'AIQLabs',
  host: process.env.RAG_DB_HOST || 'localhost', // Or your Docker service name e.g., 'postgres-rag-db'
  database: process.env.RAG_DB_NAME || 'agentiveaiq_knowledge',
  password: process.env.RAG_DB_PASSWORD || 'GrowthPros1!1!',
  port: parseInt(process.env.RAG_DB_PORT || '5435', 10),
});

// GET a specific chatbot instance (Original GET handler from your file)
export async function GET(req: NextRequest, { params: unresolvedParams }: { params: { id: string } }) {
  const params = await unresolvedParams;
  const chatbotInstanceId = params.id;
  const session = await auth(); 

  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  const userId = session.user.id;

  if (!chatbotInstanceId) {
    return NextResponse.json({ message: 'Chatbot instance ID is required' }, { status: 400 });
  }

  try {
    const chatbotInstance = await prisma.chatbotInstance.findUnique({
      where: {
        id: chatbotInstanceId,
        userId: userId, 
      },
    });

    if (!chatbotInstance) {
      return NextResponse.json({ message: 'Chatbot instance not found or access denied' }, { status: 404 });
    }
    return NextResponse.json(chatbotInstance);
  } catch (error) {
    console.error(`Error fetching chatbot instance ${chatbotInstanceId}:`, error);
    return NextResponse.json({ message: 'Error fetching chatbot instance' }, { status: 500 });
  }
}

// Schema for updating chatbot instance (Original PUT schema from your file)
const chatbotUpdateSchema = z.object({
  primaryAgentGoals: z.array(z.string()).optional(),
  secondaryAgentGoals: z.array(z.string()).optional(),
  businessContext: z.string().optional().nullable(),
  customGoalDetail: z.string().optional().nullable(),
  industry: z.string().optional().nullable(),
  targetAudience: z.string().optional().nullable(),
  tonePreference: z.string().optional().nullable(),
  agentPersonaName: z.string().optional().nullable(),
  companyName: z.string().optional().nullable(),
  uiSettings: z.record(z.string(), z.any()).optional().nullable(),
  processLeads: z.boolean().optional(),
  leadSummaryPreference: z.enum(['all_chats', 'pressing_issues']).optional().nullable(),
  deliveryMethod: z.enum(['email', 'webhook']).optional().nullable(),
  recipientEmail: z.string().optional().nullable(),
  webhookUrl: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).optional(),
});
import { z } from 'zod'; // Ensure z is imported if not already at the top

// PUT (update) a specific chatbot instance (Original PUT handler from your file)
export async function PUT(req: NextRequest, { params: unresolvedParams }: { params: { id:string } }) {
  const params = await unresolvedParams;
  const chatbotInstanceId = params.id;
  const session = await auth(); 

  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  const userId = session.user.id;

  if (!chatbotInstanceId) {
    return NextResponse.json({ message: 'Chatbot instance ID is required' }, { status: 400 });
  }

  try {
    const existingInstance = await prisma.chatbotInstance.findUnique({
      where: { id: chatbotInstanceId, userId: userId },
    });

    if (!existingInstance) {
      return NextResponse.json({ message: 'Chatbot instance not found or access denied' }, { status: 404 });
    }

    const body = await req.json();
    const validation = chatbotUpdateSchema.safeParse(body); 

    if (!validation.success) {
      return NextResponse.json({ errors: validation.error.flatten().fieldErrors }, { status: 400 });
    }

    const { /* destructure validated data */ } = validation.data;
    const updatePayload: Prisma.ChatbotInstanceUpdateInput = {};

    // ... (original updatePayload logic from your file) ...
    if (validation.data.primaryAgentGoals !== undefined) updatePayload.primaryAgentGoals = validation.data.primaryAgentGoals;
    if (validation.data.secondaryAgentGoals !== undefined) updatePayload.secondaryAgentGoals = validation.data.secondaryAgentGoals;
    if (validation.data.businessContext !== undefined) updatePayload.businessContext = validation.data.businessContext;
    if (validation.data.customGoalDetail !== undefined) updatePayload.customGoalDetail = validation.data.customGoalDetail;
    if (validation.data.industry !== undefined) updatePayload.industry = validation.data.industry;
    if (validation.data.targetAudience !== undefined) updatePayload.targetAudience = validation.data.targetAudience;
    if (validation.data.tonePreference !== undefined) updatePayload.tonePreference = validation.data.tonePreference;
    if (validation.data.agentPersonaName !== undefined) updatePayload.agentPersonaName = validation.data.agentPersonaName;
    if (validation.data.companyName !== undefined) updatePayload.companyName = validation.data.companyName;
    if (validation.data.uiSettings !== undefined) updatePayload.uiSettings = validation.data.uiSettings === null ? Prisma.DbNull : validation.data.uiSettings;
    if (validation.data.processLeads !== undefined) updatePayload.processLeads = validation.data.processLeads;
    if (validation.data.leadSummaryPreference !== undefined) updatePayload.leadSummaryPreference = validation.data.leadSummaryPreference;
    if (validation.data.deliveryMethod !== undefined) updatePayload.deliveryMethod = validation.data.deliveryMethod;
    if (validation.data.recipientEmail !== undefined) updatePayload.recipientEmail = validation.data.recipientEmail;
    if (validation.data.webhookUrl !== undefined) updatePayload.webhookUrl = validation.data.webhookUrl;
    if (validation.data.status !== undefined) updatePayload.status = validation.data.status;


    if (Object.keys(updatePayload).length === 0) {
      return NextResponse.json(existingInstance);
    }

    const updatedChatbotInstance = await prisma.chatbotInstance.update({
      where: { id: chatbotInstanceId },
      data: updatePayload,
    });

    return NextResponse.json(updatedChatbotInstance);

  } catch (error) {
    console.error(`Error updating chatbot instance ${chatbotInstanceId}:`, error);
    if (error instanceof z.ZodError) { // Ensure z is imported for this
        return NextResponse.json({ errors: error.flatten().fieldErrors }, { status: 400 });
    }
    return NextResponse.json({ message: 'Error updating chatbot instance' }, { status: 500 });
  }
}

// DELETE a specific chatbot instance
export async function DELETE(
  req: NextRequest,
  { params: unresolvedParams }: { params: { id: string } }
) {
const params = await unresolvedParams; // Await params
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  const userId = session.user.id;
  const chatbotId = params.id; // Directly use params.id

  if (!chatbotId) {
    return NextResponse.json({ message: 'Chatbot ID is required' }, { status: 400 });
  }

  let ragClient;
  try {
    const chatbotInstance = await prisma.chatbotInstance.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbotInstance) {
      return NextResponse.json({ message: 'Chatbot instance not found' }, { status: 404 });
    }

    if (chatbotInstance.userId !== userId && session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Access denied: You do not own this chatbot instance or are not an admin.' }, { status: 403 });
    }
    
    // Connect to RAG DB
    ragClient = await ragDbPool.connect();

    // Start RAG DB Deletions (assuming column name `chatbot_instance_cuid` in chat_sessions)
    // It's safer to delete chat_sessions first if they might reference knowledge_sources implicitly
    // or if knowledge_sources deletion is slow and you want to avoid orphaned chat data.
    // However, based on schema, chat_sessions are not directly linked to knowledge_sources via chatbot_id CUID.
    // They were linked via the (now unused) chatbot_configurations table.
    // We need to confirm the current linkage for chat_sessions to the SaaS chatbot_id (CUID).
    // Assuming `chatbot_instance_cuid` on `chat_sessions` table for now.
    
    // Delete chat sessions (and their messages via cascade)
    const deleteChatSessionsQuery = 'DELETE FROM chat_sessions WHERE chatbot_id = $1';
    await ragClient.query(deleteChatSessionsQuery, [chatbotId]);
    console.log(`Deleted chat_sessions for chatbot_id (CUID): ${chatbotId} from RAG DB.`);

    // Delete knowledge sources (and their chunks via cascade)
    const deleteKnowledgeSourcesQuery = 'DELETE FROM knowledge_sources WHERE chatbot_id = $1';
    await ragClient.query(deleteKnowledgeSourcesQuery, [chatbotId]);
    console.log(`Deleted knowledge_sources (and chunks) for chatbot_id: ${chatbotId} from RAG DB.`);

    // Perform the Prisma deletion from SaaS DB
    await prisma.chatbotInstance.delete({
      where: { id: chatbotId },
    });
    console.log(`Deleted chatbot_instance: ${chatbotId} from SaaS DB.`);

    return NextResponse.json({ message: 'Chatbot instance and associated RAG data deleted successfully' }, { status: 200 });

  } catch (error: any) {
    console.error(`Error deleting chatbot instance ${chatbotId}:`, error);
    // Log RAG DB specific errors if possible
    if (error.code && typeof error.code === 'string' && error.code.startsWith('2')) { // Basic check for pg error codes
        console.error("PostgreSQL RAG DB Error Code:", error.code, "Details:", error.message);
    } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2025') {
         return NextResponse.json({ message: 'Chatbot instance not found or already deleted from SaaS DB' }, { status: 404 });
      }
      return NextResponse.json({ message: `SaaS DB error: ${error.message}` }, { status: 500 });
    }
    return NextResponse.json({ message: (error instanceof Error ? error.message : 'Error deleting chatbot instance') }, { status: 500 });
  } finally {
    if (ragClient) {
      ragClient.release(); // Release the client back to the pool
    }
  }
}