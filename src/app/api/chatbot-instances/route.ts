// src/app/api/chatbot-instances/route.ts
import { NextResponse, NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client'; // Import Prisma for types
import { auth } from '../../../../auth'; // Import NextAuth.js auth
import { z } from 'zod';

export const dynamic = 'force-dynamic'; // Opt into dynamic rendering for Route Handlers

const chatbotInstanceSchema = z.object({
  name: z.string().min(1, { message: 'Chatbot name is required' }).optional(), // Made name optional for default creation
  type: z.enum(['FLOATING_WIDGET', 'EMBEDDED_WINDOW']).optional(),
});

export async function GET(req: NextRequest) {
  const session = await auth(); 

  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  const userId = session.user.id;

  try {
    const chatbotInstances = await prisma.chatbotInstance.findMany({
      where: { userId: userId }, 
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(chatbotInstances);
  } catch (error) {
    console.error('Error fetching chatbot instances:', error);
    return NextResponse.json({ message: 'Error fetching chatbot instances' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  const session = await auth(); 

  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  const userId = session.user.id; 

  try {
    const body = await req.json().catch(() => ({})); // Allow empty body for default creation
    const validation = chatbotInstanceSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ errors: validation.error.flatten().fieldErrors }, { status: 400 });
    }

    const validatedName = validation.data.name;
    const type = validation.data.type;

    const chatbotName = validatedName || `Untitled Chatbot ${new Date().toISOString().slice(0,10)}`; // Default name if not provided

    // TODO: Check plan limits before creating a new instance in a later phase

    // Create the ChatbotInstance in the SaaS DB.
    // The 'id' will be auto-generated by Prisma (CUID).
    // We will use this auto-generated 'id' as the 'centralConfigId'.
    // This assumes the Python LangServe service will use this ChatbotInstance.id
    // to fetch configuration details directly from this SaaS DB.

    let newChatbotInstance;
    try {
      // Step 1: Create the instance. centralConfigId might be null or have a default
      // depending on your schema, or we can omit it and update it in a second step.
      // For simplicity and to ensure it's set, we'll do a create then update.
      newChatbotInstance = await prisma.chatbotInstance.create({
        data: {
          user: {
            connect: {
              id: userId
            }
          },
          name: chatbotName, // Use potentially defaulted name
          type: type || undefined, // Let Prisma handle default if type is not provided
          // centralConfigId will be set to its own ID in the next step.
          // Initialize with a temporary unique value if absolutely required by a NOT NULL constraint
          // and no default, though CUIDs for `id` are usually sufficient.
          // For now, assuming `centralConfigId` can be updated after creation or is nullable.
          // If `centralConfigId` has a `@default(cuid())` in Prisma, this initial set is not strictly needed
          // before the update, but we want to ensure it's the *instance's own ID*.
          // To avoid potential issues with @unique on centralConfigId if it were to default
          // to something non-unique temporarily, we ensure it's set to its own ID.
          // We will set it to a temporary placeholder then update, to handle @unique.
          // Using a simpler placeholder that doesn't rely on the uuid package.
          centralConfigId: `placeholder-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
        },
      });

      // Step 2: Update the instance to set centralConfigId to its own ID.
      const updatedInstance = await prisma.chatbotInstance.update({
        where: { id: newChatbotInstance.id },
        data: { centralConfigId: newChatbotInstance.id },
      });
      
      console.log(`Created ChatbotInstance with ID: ${updatedInstance.id}, and centralConfigId set to: ${updatedInstance.centralConfigId}`);
      return NextResponse.json(updatedInstance, { status: 201 });

    } catch (error: any) {
      console.error('Error in POST /api/chatbot-instances (Prisma interaction):', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2002') {
              const target = error.meta?.target as string[] | undefined;
              console.error(`Prisma unique constraint violation on: ${target?.join(', ')}`, error.message);
              return NextResponse.json({ message: `A data conflict occurred (constraint: ${target?.join(', ')})` }, { status: 409 });
          }
          return NextResponse.json({ message: `Database error: ${error.message}` }, { status: 500 });
      }
      // The ZodError should have been caught by the initial validation,
      // but including it here as a fallback from the original code structure.
      if (error instanceof z.ZodError) {
          return NextResponse.json({ errors: error.flatten().fieldErrors }, { status: 400 });
      }
      return NextResponse.json({ message: (error instanceof Error ? error.message : 'Error creating chatbot instance') }, { status: 500 });
    }
  } catch (error: any) { // This outer catch is for errors like req.json() failing
    console.error('Error in POST /api/chatbot-instances (outermost catch):', error);
    return NextResponse.json({ message: (error instanceof Error ? error.message : 'Failed to process request') }, { status: 500 });
  }
}