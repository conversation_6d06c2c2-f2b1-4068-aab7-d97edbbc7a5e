import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import { validateEvent, WebhookVerificationError } from "@polar-sh/sdk/webhooks";
import { SubscriptionTier, UserRole } from "@prisma/client"; // Import enums

export const dynamic = "force-dynamic";

// Define types for the incoming webhook payloads based on webhook.md
interface PolarCustomer {
  id: string;
  email: string;
  // add other customer fields if needed
}
interface PolarPrice {
  id: string;
  productId: string;
  // add other price fields
}

interface PolarProduct { // Not directly used in current logic but good for reference
  id: string;
  name: string;
  prices?: PolarPrice[];
  // add other product fields
}

interface PolarSubscription {
  id: string;
  status: string;
  customerId: string;
  productId: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd?: boolean | null;
  endsAt?: string | null;
  prices?: PolarPrice[];
  product?: {
    prices?: PolarPrice[];
  };
  metadata?: any; // Added metadata
}

interface PolarOrder {
  id: string;
  customerId: string;
  subscriptionId?: string | null;
  status: string;
  items: Array<{
    productPriceId: string;
  }>;
  customer: PolarCustomer;
  subscription?: PolarSubscription;
  productId?: string | null;
  product?: PolarProduct | null;
  metadata?: any; // Added metadata
}


// Helper function to determine tier based on Polar Product ID
function getTierFromPolarProductId(polarProductId: string | null | undefined): SubscriptionTier {
  if (!polarProductId) return SubscriptionTier.FREE;
  // Compare against your known Polar Product IDs from .env
  // Ensure these env variables are correctly mapped to your actual Product IDs for Base and Pro tiers
  if (polarProductId === process.env.NEXT_PUBLIC_POLAR_PRO_MONTHLY_ID || polarProductId === process.env.NEXT_PUBLIC_POLAR_PRO_YEARLY_ID) {
    return SubscriptionTier.PRO;
  }
  if (polarProductId === process.env.NEXT_PUBLIC_POLAR_BASE_MONTHLY_ID || polarProductId === process.env.NEXT_PUBLIC_POLAR_BASE_YEARLY_ID) {
    return SubscriptionTier.BASE;
  }
  return SubscriptionTier.FREE;
}


export async function POST(req: NextRequest) {
  try {
    const rawBody = await req.text();
    
    // Convert headers to plain object
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
    });

    console.log("Raw webhook body:", rawBody); // Log raw body for inspection
    const event = validateEvent(
      rawBody,
      headers,
      process.env.POLAR_WEBHOOK_SECRET ?? ''
    );

    console.log(`Polar webhook received - Type: ${event.type}`);
    console.log("Full event payload after validation:", JSON.stringify(event, null, 2)); // Added for debugging
    // For debugging, you can log the full payload:
    // console.log("Full payload for type", event.type, JSON.stringify(event.data, null, 2));

    switch (event.type) {
      case 'order.paid': {
        const order = event.data as PolarOrder;
        console.log(`Handler: order.paid triggered for order: ${order.id}`);
        try {
          console.log("order.paid full payload:", JSON.stringify(event, null, 2));
          if (!order.customerId || !order.subscriptionId || !order.items || order.items.length === 0) {
            console.error("order.paid: Missing critical data (customerId, subscriptionId, or items).");
            return NextResponse.json({ status: 'ignored', message: 'Missing critical data' }, { status: 400 });
          }
          
          const customerId = order.customerId;
          const subscriptionId = order.subscriptionId;
          const priceId = order.items[0]?.productPriceId;
          const productIdFromOrder = order.productId || order.product?.id;
    
          if (!productIdFromOrder) {
            console.error(`order.paid: Critical - Product ID not found in order payload for order ${order.id}. Cannot determine tier.`);
            return NextResponse.json({ status: 'ignored', message: 'Missing product ID' }, { status: 400 });
          }
          if (!priceId) {
            console.error(`order.paid: Critical - Price ID not found in order.items for order ${order.id}.`);
            return NextResponse.json({ status: 'ignored', message: 'Missing price ID' }, { status: 400 });
          }
          let user = await prisma.user.findUnique({
            where: { paymentProviderCustomerId: customerId },
          });
    if (!user && order.customer?.email) {
      user = await prisma.user.findUnique({ where: { email: order.customer.email }});
      if (user) {
        // Always update the paymentProviderCustomerId to ensure it matches Polar's customer ID
        await prisma.user.update({
          where: { id: user.id },
          data: { paymentProviderCustomerId: customerId },
        });
        console.log(`order.paid: Updated paymentProviderCustomerId to ${customerId} for user ${user.id}`);
      }
    }
          
          if (!user) {
            console.error(`order.paid: User not found for customer ID ${customerId} or email ${order.customer?.email}. Cannot update subscription.`);
            return NextResponse.json({ status: 'ignored', message: 'User not found' }, { status: 404 });
          }

          let currentPeriodEndValue: Date | null = null;
          if (order.subscription?.currentPeriodEnd) {
              currentPeriodEndValue = new Date(order.subscription.currentPeriodEnd);
          } else {
              console.warn(`order.paid: currentPeriodEnd not found in order.subscription for sub ID ${subscriptionId}. This might be set by a subsequent subscription.updated event.`);
          }
    
          const newTier = getTierFromPolarProductId(productIdFromOrder);
    
          await prisma.user.update({
            where: { id: user.id },
            data: {
              paymentProviderSubscriptionId: subscriptionId,
              paymentProviderPriceId: priceId,
              paymentProviderCurrentPeriodEnd: currentPeriodEndValue,
              tier: newTier,
              paymentProviderCustomerId: customerId,
            },
          });
          console.log(`order.paid: User ${user.id} subscription updated. Tier: ${newTier}, PriceID: ${priceId}`);
          return NextResponse.json({ status: 'ok' });
        } catch (error: any) {
          console.error("Error in order.paid handler:", error.message, error.stack);
          console.error("Failing order.paid payload:", JSON.stringify(event, null, 2));
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
      }

      case 'subscription.created': {
        const subscription = event.data as PolarSubscription;
        console.log(`Handler: subscription.created triggered for subscription: ${subscription.id}, Status: ${subscription.status}`);
        try {
          console.log("subscription.created full payload:", JSON.stringify(event, null, 2));
          const customerId = subscription.customerId;
          const userId = subscription.metadata?.userId as string | undefined; // Assuming userId is in metadata

          if (!userId) {
            console.error("subscription.created: No user ID in subscription metadata.");
            return NextResponse.json({ status: 'ignored', message: 'Missing user ID' }, { status: 400 });
          }

          const user = await prisma.user.findUnique({
            where: { id: userId },
          });

          if (!user) {
            console.error(`subscription.created: User not found for ID ${userId}.`);
            return NextResponse.json({ status: 'ignored', message: 'User not found' }, { status: 404 });
          }
    
          const priceId = subscription.prices?.[0]?.id || subscription.product?.prices?.[0]?.id || null;
          const productIdFromSubscription = subscription.productId;
    
          if (!productIdFromSubscription) {
            console.error(`subscription.created: Critical - Product ID not found in subscription payload for subscription ${subscription.id}. Cannot determine tier.`);
            return NextResponse.json({ status: 'ignored', message: 'Missing product ID' }, { status: 400 });
          }
          if (!priceId) {
            console.warn(`subscription.created: Price ID not found in subscription.prices or subscription.product.prices for subscription ${subscription.id}.`);
          }
    
          const newTier = getTierFromPolarProductId(productIdFromSubscription);
    
          await prisma.user.update({
            where: { id: user.id },
            data: {
              paymentProviderSubscriptionId: subscription.id,
              paymentProviderPriceId: priceId,
              paymentProviderCurrentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              tier: newTier,
              paymentProviderCustomerId: customerId, // Ensure customerId is linked
            },
          });
          console.log(`subscription.created: User ${user.id} status updated. New Tier: ${newTier}, PriceID: ${priceId}`);
          return NextResponse.json({ status: 'ok' });
        } catch (error: any) {
          console.error("Error in subscription.created handler:", error.message, error.stack);
          console.error("Failing subscription.created payload:", JSON.stringify(event, null, 2));
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
      }

      case 'subscription.updated': {
        const subscription = event.data as PolarSubscription;
        console.log(`Handler: subscription.updated triggered for subscription: ${subscription.id}, Status: ${subscription.status}`);
        try {
          console.log("subscription.updated full payload:", JSON.stringify(event, null, 2));
          const customerId = subscription.customerId;
          const userId = subscription.metadata?.userId as string | undefined; // Assuming userId is in metadata

          if (!userId) {
            console.error("subscription.updated: No user ID in subscription metadata.");
            return NextResponse.json({ status: 'ignored', message: 'Missing user ID' }, { status: 400 });
          }

          const user = await prisma.user.findUnique({
            where: { id: userId },
          });

          if (!user) {
            console.error(`subscription.updated: User not found for ID ${userId}.`);
            return NextResponse.json({ status: 'ignored', message: 'User not found' }, { status: 404 });
          }
    
          const priceId = subscription.prices?.[0]?.id || subscription.product?.prices?.[0]?.id || null;
          const productIdFromSubscription = subscription.productId;
    
          if (!productIdFromSubscription) {
            console.error(`subscription.updated: Critical - Product ID not found in subscription payload for subscription ${subscription.id}. Cannot determine tier.`);
            return NextResponse.json({ status: 'ignored', message: 'Missing product ID' }, { status: 400 });
          }
          if (!priceId) {
            console.warn(`subscription.updated: Price ID not found in subscription.prices or subscription.product.prices for subscription ${subscription.id}.`);
          }
    
          const newTier = getTierFromPolarProductId(productIdFromSubscription);
    
          let updateData: any = {
            paymentProviderSubscriptionId: subscription.id,
            paymentProviderPriceId: priceId,
            paymentProviderCurrentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
            tier: newTier,
          };
          if (subscription.status === "canceled" || subscription.status === "ended" || subscription.cancelAtPeriodEnd) {
            updateData.tier = SubscriptionTier.FREE;
            if (subscription.status === "ended" || (subscription.endsAt && new Date(subscription.endsAt) < new Date())) {
              updateData.paymentProviderSubscriptionId = null;
              updateData.paymentProviderPriceId = null;
              updateData.paymentProviderCurrentPeriodEnd = null;
            }
          } else if (subscription.status !== "active" && subscription.status !== "trialing") {
            updateData.tier = SubscriptionTier.FREE;
          }
    
          await prisma.user.update({
            where: { id: user.id },
            data: updateData,
          });
          console.log(`subscription.updated: User ${user.id} status updated. New Tier: ${updateData.tier}, PriceID: ${priceId}`);
          return NextResponse.json({ status: 'ok' });
        } catch (error: any) {
          console.error("Error in subscription.updated handler:", error.message, error.stack);
          console.error("Failing subscription.updated payload:", JSON.stringify(event, null, 2));
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
      }

      case 'customer.created': {
        const customer = event.data as PolarCustomer;
        console.log("Handler: customer.created triggered for customer:", customer.id, "Email:", customer.email);
        try {
          console.log("customer.created full payload:", JSON.stringify(event, null, 2));
          const user = await prisma.user.findUnique({ where: { email: customer.email } });
          if (user && !user.paymentProviderCustomerId) {
            await prisma.user.update({
              where: { id: user.id },
              data: { paymentProviderCustomerId: customer.id },
            });
            console.log(`customer.created: Linked Polar Customer ID ${customer.id} to user ${user.id}`);
          } else if (user && user.paymentProviderCustomerId && user.paymentProviderCustomerId !== customer.id) {
            console.warn(`customer.created: User ${user.id} (email: ${user.email}) already has Polar Customer ID ${user.paymentProviderCustomerId}. New Polar Customer ID is ${customer.id} for email ${customer.email}. This might indicate a duplicate customer record in Polar or a previous manual linking.`);
          } else if (!user) {
            console.log(`customer.created: No user found with email ${customer.email} to link Polar Customer ID ${customer.id}. A new user might be created if an order/subscription for this customer follows.`);
          }
          return NextResponse.json({ status: 'ok' });
        } catch (error: any) {
          console.error("Error in customer.created handler:", error.message, error.stack);
          console.error("Failing customer.created payload:", JSON.stringify(event, null, 2));
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
      }
      
      case 'customer.updated': {
        const customer = event.data as PolarCustomer;
        console.log("Handler: customer.updated triggered for customer:", customer.id, "Email:", customer.email);
        try {
          console.log("customer.updated full payload:", JSON.stringify(event, null, 2));
          const user = await prisma.user.findUnique({ where: { paymentProviderCustomerId: customer.id } });
          if (user && user.email !== customer.email) {
            console.log(`customer.updated: Email for Polar Customer ${customer.id} changed from ${user.email} to ${customer.email}. Consider if local user email should be updated.`);
          } else if (!user) {
             console.warn(`customer.updated: No user found with Polar Customer ID ${customer.id} to update.`);
          }
          return NextResponse.json({ status: 'ok' });
        } catch (error: any) {
          console.error("Error in customer.updated handler:", error.message, error.stack);
          console.error("Failing customer.updated payload:", JSON.stringify(event, null, 2));
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
      }

      default:
        console.log(`Unhandled Polar webhook event type: ${event.type}`);
        return NextResponse.json({ status: 'ignored' });
    }
  } catch (error) {
    if (error instanceof WebhookVerificationError) {
      console.error("Webhook verification failed:", error);
      return NextResponse.json({ error: "Invalid signature" }, { status: 403 });
    }
    console.error("Error in webhook handler:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}