// src/app/api/chat-history/messages/[sessionDbId]/route.ts
import { NextResponse, NextRequest } from 'next/server';
import { auth } from '../../../../../../auth'; // Corrected relative path (5 levels up)
// Note: We might need a way to verify user ownership of the session indirectly,
// e.g., by ensuring the session belongs to a chatbot they own.
// For an MVP, if the sessionDbId is only obtained via the sessions endpoint (which is authed),
// this might be implicitly covered for now, but it's a security consideration.

export const dynamic = 'force-dynamic';

export async function GET(
  req: NextRequest,
  { params: unresolvedParams }: { params: { sessionDbId: string } }
) {
  const params = await unresolvedParams; // Await the params
  const session = await auth(); // Auth check for the logged-in user
  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  // const userId = session.user.id; // Available if needed for further checks

  const { sessionDbId } = params; // Now destructure from the resolved params

  if (!sessionDbId) {
    return NextResponse.json({ message: 'Session DB ID is required' }, { status: 400 });
  }

  // TODO: Add a security check here in a real application:
  // Verify that the requesting user has the right to view messages for this sessionDbId.
  // This might involve:
  // 1. Fetching the session from RAG DB using sessionDbId.
  // 2. Getting the chatbot_id (CUID) associated with that session.
  // 3. Checking if that chatbot_id belongs to the current userId in the SaaS DB (prisma.chatbotInstance.findUnique).
  // For this initial implementation, we'll assume the frontend only requests valid session IDs.

  try {
    const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
    if (!centralApiUrl) {
      console.error("CENTRAL_API_URL is not defined in environment variables.");
      return NextResponse.json({ message: 'Central API URL is not configured' }, { status: 500 });
    }

    const messagesResponse = await fetch(`${centralApiUrl}/api/v1/chat-history/messages/${sessionDbId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any necessary auth headers if your Python service requires them
      },
    });

    if (!messagesResponse.ok) {
      const errorData = await messagesResponse.json().catch(() => ({ message: 'Failed to parse error from history service' }));
      console.error(`Error from central-api-service fetching messages (status ${messagesResponse.status}):`, errorData);
      return NextResponse.json({ message: errorData.detail || `Failed to fetch chat messages. Status: ${messagesResponse.status}` }, { status: messagesResponse.status });
    }

    const messagesData = await messagesResponse.json();
    return NextResponse.json(messagesData);

  } catch (error: any) {
    console.error(`Error fetching chat messages for session ${sessionDbId}:`, error);
    return NextResponse.json({ message: error.message || 'Error fetching chat messages' }, { status: 500 });
  }
}