// src/app/api/chat-history/sessions/[chatbotInstanceId]/route.ts
import { NextResponse, NextRequest } from 'next/server';
import { auth } from '../../../../../../auth'; // Corrected relative path for auth
import prisma from '@/lib/prisma';

export const dynamic = 'force-dynamic';

export async function GET(
  req: NextRequest,
  { params: unresolvedParams }: { params: { chatbotInstanceId: string } }
) {
  const params = await unresolvedParams; // Await the params
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
  }
  const userId = session.user.id;
  const { chatbotInstanceId } = params; // Now destructure from the resolved params

  if (!chatbotInstanceId) {
    return NextResponse.json({ message: 'Chatbot Instance ID is required' }, { status: 400 });
  }

  try {
    // Verify user owns the chatbot instance
    const chatbotInstance = await prisma.chatbotInstance.findUnique({
      where: {
        id: chatbotInstanceId,
        userId: userId,
      },
    });

    if (!chatbotInstance) {
      return NextResponse.json({ message: 'Chatbot instance not found or access denied' }, { status: 404 });
    }

    const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;
    if (!centralApiUrl) {
      console.error("CENTRAL_API_URL is not defined in environment variables.");
      return NextResponse.json({ message: 'Central API URL is not configured' }, { status: 500 });
    }

    const historyResponse = await fetch(`${centralApiUrl}/api/v1/chat-history/sessions/${chatbotInstanceId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any necessary auth headers if your Python service requires them
      },
    });

    if (!historyResponse.ok) {
      const errorData = await historyResponse.json().catch(() => ({ message: 'Failed to parse error from history service' }));
      console.error(`Error from central-api-service fetching sessions (status ${historyResponse.status}):`, errorData);
      return NextResponse.json({ message: errorData.detail || `Failed to fetch chat sessions. Status: ${historyResponse.status}` }, { status: historyResponse.status });
    }

    const sessionsData = await historyResponse.json();
    return NextResponse.json(sessionsData);

  } catch (error: any) {
    console.error(`Error fetching chat sessions for ${chatbotInstanceId}:`, error);
    return NextResponse.json({ message: error.message || 'Error fetching chat sessions' }, { status: 500 });
  }
}