import { NextResponse } from 'next/server';
import sgMail from '@sendgrid/mail';

export async function POST(request: Request) {
  const { name, email, message } = await request.json();

  if (!process.env.SENDGRID_API_KEY) {
    console.error('SENDGRID_API_KEY is not set.');
    return NextResponse.json({ error: 'Server configuration error: Email service is not available.' }, { status: 500 });
  }

  sgMail.setApiKey(process.env.SENDGRID_API_KEY);

  const senderEmail = process.env.SENDGRID_SENDER_EMAIL;
  const senderName = process.env.SENDGRID_SENDER_FROM_NAME || 'AgentiveAIQ Support';
  const replyToEmail = process.env.SENDGRID_REPLYTO_ADDRESS || '<EMAIL>';
  
  // The email address where support messages should be sent.
  // This <NAME_EMAIL> or the SENDGRID_SENDER_EMAIL if it's monitored for support.
  const supportRecipientEmail = '<EMAIL>'; 

  if (!senderEmail) {
    console.error('SENDGRID_SENDER_EMAIL is not set.');
    return NextResponse.json({ error: 'Server configuration error: Sender email is not configured.' }, { status: 500 });
  }

  if (!name || !email || !message) {
    return NextResponse.json({ error: 'Missing required fields: name, email, and message are required.' }, { status: 400 });
  }

  const msg = {
    to: supportRecipientEmail,
    from: {
        name: senderName,
        email: senderEmail,
    },
    replyTo: email, // User's email as reply-to
    subject: `New Support Request from ${name}`,
    text: `You have received a new support request:\n\nName: ${name}\nEmail: ${email}\n\nMessage:\n${message}`,
    html: `<p>You have received a new support request:</p>
           <p><strong>Name:</strong> ${name}</p>
           <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
           <p><strong>Message:</strong></p>
           <p>${message.replace(/\n/g, '<br>')}</p>`,
  };

  try {
    await sgMail.send(msg);
    return NextResponse.json({ success: true, message: 'Support email sent successfully.' });
  } catch (error: any) {
    console.error('Error sending support email with SendGrid:', error.response?.body || error.message);
    return NextResponse.json({ error: 'Failed to send support email.', details: error.response?.body?.errors || error.message }, { status: 500 });
  }
}