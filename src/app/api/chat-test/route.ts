// app/api/chat-test/route.ts
import { Message as VercelChatMessage } from 'ai'; 

export const runtime = 'edge'; 

// Helper to format for Vercel AI SDK (simple text stream)
function formatVercelChunk(type: 'text' | 'data', data: any): string {
  // For simple text, the SDK expects "0:"<json_stringified_chunk>"\n"
  // For text chunks, the content itself is often sent directly after the prefix.
  // Let's try sending the text directly. The SDK handles JSON.stringify internally for text.
  return `0:${JSON.stringify(data)}\n`; 
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const messages: VercelChatMessage[] = body.messages || [];
    
    const centralConfigId = body.data?.centralConfigId; 
    const threadId = body.data?.threadId; 

    if (!messages || messages.length === 0) {
      return new Response(JSON.stringify({ error: 'No messages provided' }), { status: 400 });
    }
    if (!centralConfigId) {
      return new Response(JSON.stringify({ error: 'centralConfigId is required in body.data' }), { status: 400 });
    }

    const latestUserMessageContent = messages[messages.length - 1]?.content;
    const centralApiServiceUrl = `${process.env.NEXT_PUBLIC_CENTRAL_API_URL}/api/v1/lg_chat/agent/stream_events`;

    console.log(`[API Chat Test] Calling LangServe: ${centralApiServiceUrl} for config_id: ${centralConfigId}, thread_id: ${threadId}`);

    const agentResponse = await fetch(centralApiServiceUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Accept': 'text/event-stream' },
      body: JSON.stringify({
        input: { // This object maps to the GraphInput Pydantic model in Python
            config_id: centralConfigId, // This will populate RAGGraphState.config_id
            thread_id: threadId || `preview-thread-${Date.now()}`, // This will populate RAGGraphState.thread_id
            messages: messages.map(m => ({ type: m.role, content: m.content })), // Maps to RAGGraphState.messages (after processing)
        },
        config: { // This is the separate 'config' argument for langserve's .stream_events()
            configurable: {
                // thread_id passed here is typically used by RunnableWithMessageHistory
                thread_id: threadId || `preview-thread-${Date.now()}`
            }
        }
      }),
    });

    if (!agentResponse.ok) {
      const errorText = await agentResponse.text();
      console.error(`[API Chat Test] Error from central-api-service (status ${agentResponse.status}):`, errorText);
      return new Response(JSON.stringify({ error: 'Failed to get response from agent.', details: errorText, status: agentResponse.status }), { status: agentResponse.status });
    }

    if (agentResponse.body) {
      console.log('[API Chat Test] agentResponse.body is present. Setting up stream pipeline.');
      const textDecoderStream = new TextDecoderStream();
      const readableStream = agentResponse.body.pipeThrough(textDecoderStream);
      
      const customTransformStream = new TransformStream<string, string>({
        async start(controller) {
          console.log('[API Chat Test Stream Parser] TransformStream started.');
          const reader = readableStream.getReader();
          let llmResponseSent = false;

          try {
            while (true) {
              const { value, done } = await reader.read();
              if (done) {
                console.log('[API Chat Test Stream Parser] ReadableStream finished.');
                if (!llmResponseSent) {
                    console.log('[API Chat Test Stream Parser] No LLM response was extracted and sent during the stream.');
                }
                controller.terminate();
                break;
              }
              const chunk = value; 
              const normalizedChunk = chunk.replace(/\r\n/g, '\n');
              const sseMessageBlocks = normalizedChunk.split('\n\n'); 

              for (const sseMessageBlock of sseMessageBlocks) {
                if (sseMessageBlock.trim() === '') continue; 
                const messageLines = sseMessageBlock.split('\n');
                for (const dataLine of messageLines) {
                  if (dataLine.startsWith('data: ')) {
                    const jsonDataString = dataLine.substring(5).trim();
                    if (jsonDataString) {
                      try {
                        const jsonData = JSON.parse(jsonDataString);
                        const eventName = jsonData.event ? String(jsonData.event).trim() : undefined;
                        const nodeName = jsonData.name ? String(jsonData.name).trim() : undefined;

                        let extractedText: string | undefined = undefined;

                        if (!llmResponseSent) {
                          // First, try to get the LLM response from call_llm events
                          if (eventName === "on_chain_end" && nodeName === "call_llm") {
                            if (jsonData.data?.output?.llm_response_text) {
                              extractedText = jsonData.data.output.llm_response_text;
                            }
                          }
                          else if (eventName === "on_chain_stream" && nodeName === "call_llm") {
                            if (jsonData.data?.chunk?.llm_response_text) {
                              extractedText = jsonData.data.chunk.llm_response_text;
                            }
                          }
                          // Then try to get the final agent output
                          else if (eventName === "on_chain_end" && nodeName === "agent") {
                            if (jsonData.data?.output?.messages && Array.isArray(jsonData.data.output.messages)) {
                              const lastMessage = jsonData.data.output.messages[jsonData.data.output.messages.length - 1];
                              if (lastMessage && lastMessage.content) {
                                extractedText = lastMessage.content;
                              }
                            }
                          }
                          // Finally, look for any content in the output
                          else if (jsonData.data?.output?.content) {
                            extractedText = jsonData.data.output.content;
                          }
                          else if (jsonData.data?.output) {
                            // Try to extract content from any output
                            extractedText = JSON.stringify(jsonData.data.output);
                          }
                        }

                        if (extractedText && !llmResponseSent) {
                          const userFacingText = extractedText.replace(/<think>[\s\S]*?<\/think>\s*\n*/, '').trim();
                          if (userFacingText) {
                             const formattedChunk = formatVercelChunk('text', userFacingText);
                             console.log('[API Chat Test Stream Parser] Enqueuing formatted Vercel AI SDK chunk:', JSON.stringify(formattedChunk));
                             controller.enqueue(formattedChunk);
                             llmResponseSent = true; 
                          } else {
                            // console.log('[API Chat Test Stream Parser] Extracted text was empty after stripping think tags. Original:', JSON.stringify(extractedText));
                          }
                        }
                      } catch (e: any) { 
                        if (jsonDataString.includes("ChannelWrite")) {
                            // console.warn(`[API Chat Test Stream Parser] Malformed JSON for ChannelWrite event. Data: ${jsonDataString}. Error: ${e.message}`);
                        } else {
                            // console.warn(`[API Chat Test Stream Parser] Failed to parse JSON or error in logic. Data: ${jsonDataString}. Error:`, e.message);
                        }
                      }
                    }
                  }
                }
              }
            }
          } catch (error: any) { 
            console.error('[API Chat Test Stream Parser] Error in reader loop:', error.message);
            controller.error(error);
          } finally {
            reader.releaseLock();
          }
        }
      });

      // The Response headers should indicate it's a Vercel AI Stream
      return new Response(customTransformStream.readable, { 
        headers: { 
          'Content-Type': 'text/plain; charset=utf-8', // Vercel AI SDK often uses text/plain for its prefixed stream
          'X-Experimental-Stream-Data': 'true' // Header used by Vercel AI SDK
        },
      });
    } else {
      console.error('[API Chat Test] No stream body from agentResponse');
      return new Response(JSON.stringify({ error: 'No stream content received from agent.' }), { status: 500 });
    }
  } catch (error: any) { 
    console.error("[API Chat Test Route Error]", error.message);
    return new Response(JSON.stringify({ error: error.message || 'An unknown error occurred in chat-test API route.', details: error.stack }), { status: 500 });
  }
}