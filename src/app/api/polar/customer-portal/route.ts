import { getCurrentUser } from "@/lib/session";
import { prisma } from "@/lib/db";
import { CustomerPortal } from "@polar-sh/nextjs";
import { NextRequest } from "next/server";

export const dynamic = "force-dynamic";

export const GET = CustomerPortal({
  server: 'sandbox', // Configure for sandbox environment
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  getCustomerId: async (req: NextRequest) => {
    console.log("POLAR_ACCESS_TOKEN used in customer-portal route:", process.env.POLAR_ACCESS_TOKEN ? "Token present" : "Token missing");
    const user = await getCurrentUser();
    if (!user || !user.id) {
      console.error("Customer Portal: User not found or not authenticated.");
      return null;
    }

    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { paymentProviderCustomerId: true },
    });

    if (!dbUser?.paymentProviderCustomerId) {
      console.error(`Customer Portal: Polar Customer ID not found for user ${user.id}`);
      return null;
    }
    console.log(`Customer Portal: User platform ID: ${user.id}`);
    console.log(`Customer Portal: Using Polar customer ID: ${dbUser.paymentProviderCustomerId}`);
    return dbUser.paymentProviderCustomerId;
  },
  returnUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
});