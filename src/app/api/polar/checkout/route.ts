import { getCurrentUser } from "@/lib/session";
import { prisma } from "@/lib/db";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || !user.id) {
      console.error("Checkout: User not found or not authenticated.");
      return NextResponse.json({ error: "User not authenticated." }, { status: 401 });
    }

    // Get request parameters
    const body = await req.json();
    const { priceId, returnUrl } = body;

    if (!priceId) {
      console.error("Error: Price ID (priceId) is missing in request body.");
      return NextResponse.json({ error: "Price ID (priceId) is required." }, { status: 400 });
    }

    // Use provided return URL or fallback to current page
    const successUrl = returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`;

    console.log("Creating checkout session for user:", user.id);
    console.log("Using Polar token:", process.env.POLAR_ACCESS_TOKEN ? `${process.env.POLAR_ACCESS_TOKEN.substring(0,10)}...` : "MISSING");
    console.log("Token length:", process.env.POLAR_ACCESS_TOKEN?.length || 0);
    console.log("Token prefix:", process.env.POLAR_ACCESS_TOKEN?.startsWith('polar_oat_') ? "Valid" : "Invalid");

    const requestBody = {
      products: [priceId], // Changed to products array with product ID
      success_url: `${successUrl}?checkout_id={CHECKOUT_ID}`,
      embed_origin: process.env.NEXT_PUBLIC_APP_URL,
      customer_email: user.email,
      customer_name: user.name || user.email,
      metadata: {
        userId: user.id
      }
    };

    console.log("Request body to Polar:", JSON.stringify(requestBody, null, 2));

    // Create checkout session with user details using sandbox API
    const checkoutResponse = await fetch("https://sandbox-api.polar.sh/api/v1/checkouts", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.POLAR_ACCESS_TOKEN}`,
      },
      body: JSON.stringify(requestBody), // Use the constructed requestBody
    });

    if (!checkoutResponse.ok) {
      const errorText = await checkoutResponse.text();
      console.error("Failed to create Polar checkout session. Status:", checkoutResponse.status, "Response:", errorText);
      return NextResponse.json({
        error: "Failed to create checkout session.",
        details: errorText
      }, { status: 500 });
    }

    const session = await checkoutResponse.json();
    console.log("Checkout session created successfully:", session.id);
    
    return NextResponse.json({
      url: session.url,
      sessionId: session.id
    });

  } catch (error) {
    console.error("Error in checkout POST handler:", error);
    return NextResponse.json({
      error: "Failed to process checkout.",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}