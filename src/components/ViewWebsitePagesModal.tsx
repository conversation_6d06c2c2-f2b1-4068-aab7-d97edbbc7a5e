'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from 'sonner';
import { Loader2, Eye, ExternalLink } from 'lucide-react';
import ViewFileContentModal from '@/components/ViewFileContentModal'; // To view individual page markdown

// Simplified KnowledgeSource type for the parent website source
interface WebsiteKnowledgeSource {
    source_id: string;
    name: string; // e.g., the root URL or a user-given name
    type: string; // Should be 'WEBSITE'
    location_uri?: string; // The root URL
}

// Matches ScrapedPageDetail from backend
interface ScrapedPageDetail {
    page_id: string; 
    parent_source_id: string; 
    page_url: string;
    page_title?: string | null;
    minio_markdown_object_name: string;
    status: string;
    content_length_markdown?: number;
    // created_at: string; // If needed
    // last_updated: string; // If needed
}

// Props for ViewFileContentModal when viewing a specific page's markdown
interface PageMarkdownSourceForModal {
    source_id: string; // This will be the parent_source_id (websiteSource.source_id)
    name: string;      // We can use page_title or page_url
    type: 'WEBSITE_PAGE_MARKDOWN'; // Special type for ViewFileContentModal to recognize
    location_uri?: string; // page_url
    metadata_json?: {
        page_minio_path: string; // Crucial for fetching
        // We might need to simulate other metadata if ViewFileContentModal expects it
        // For now, ViewFileContentModal doesn't rely heavily on metadata_json for this new type
    };
}

interface ViewWebsitePagesModalProps {
  isOpen: boolean;
  onClose: () => void;
  websiteSource: WebsiteKnowledgeSource | null;
}

const ViewWebsitePagesModal: React.FC<ViewWebsitePagesModalProps> = ({ isOpen, onClose, websiteSource }) => {
  const [pages, setPages] = useState<ScrapedPageDetail[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [isViewPageMarkdownModalOpen, setIsViewPageMarkdownModalOpen] = useState(false);
  const [selectedPageForMarkdown, setSelectedPageForMarkdown] = useState<PageMarkdownSourceForModal | null>(null);

  const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;

  const fetchScrapedPages = useCallback(async () => {
    if (!websiteSource || !centralApiUrl) return;
    setIsLoading(true);
    setError(null);
    setPages([]);
    try {
      const response = await fetch(`${centralApiUrl}/api/v1/knowledge/sources/${websiteSource.source_id}/scraped-pages`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: "Failed to fetch scraped pages." }));
        throw new Error(errorData.detail || `Error ${response.status}`);
      }
      const data: ScrapedPageDetail[] = await response.json();
      setPages(data);
      if (data.length === 0) {
        toast.info("No scraped pages found for this website source yet, or the crawl is still in progress.");
      }
    } catch (err) {
      console.error("Error fetching scraped pages:", err);
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
      setError(errorMessage);
      toast.error(`Failed to load scraped pages: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  }, [websiteSource, centralApiUrl]);

  useEffect(() => {
    if (isOpen && websiteSource) {
      fetchScrapedPages();
    }
  }, [isOpen, websiteSource, fetchScrapedPages]);

  const handleViewPageMarkdown = (page: ScrapedPageDetail) => {
    if (!websiteSource) return;
    setSelectedPageForMarkdown({
      source_id: websiteSource.source_id, // This is the parent_source_id
      name: page.page_title || page.page_url,
      type: 'WEBSITE_PAGE_MARKDOWN',
      location_uri: page.page_url,
      metadata_json: {
        page_minio_path: page.minio_markdown_object_name,
      }
    });
    setIsViewPageMarkdownModalOpen(true);
  };
  
  const handleClose = () => {
    setError(null);
    setPages([]); // Clear pages on close
    onClose();
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl max-h-[85vh] flex flex-col"> {/* Made wider */}
        <DialogHeader className="p-4 border-b">
          <DialogTitle>Scraped Pages for: {websiteSource?.name || websiteSource?.location_uri || 'N/A'}</DialogTitle>
        </DialogHeader>
        <div className="flex-grow overflow-y-auto p-1"> {/* Main vertical scroll */}
          {isLoading && (
            <div className="flex justify-center items-center h-40"><Loader2 className="h-8 w-8 animate-spin" /> <p className="ml-2">Loading pages...</p></div>
          )}
          {error && !isLoading && (
            <div className="text-red-600 p-4">Error loading pages: {error}</div>
          )}
          {!isLoading && !error && pages.length === 0 && (
            <p className="p-4 text-center text-muted-foreground">No pages found or crawl still in progress.</p>
          )}
          {!isLoading && !error && pages.length > 0 && (
            <ScrollArea className="h-[calc(75vh-150px)] w-full" type="auto"> {/* Ensure ScrollArea takes full width and scrolls auto */}
              <div className="overflow-x-auto"> {/* This div will allow horizontal scrolling for the table */}
                <Table className="min-w-full"> {/* Ensure table can expand beyond modal width */}
                  <TableHeader>
                    <TableRow>
                    <TableHead className="w-[60%]">Title / URL</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Length (MD)</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pages.map((page, index) => {
                    console.log(`[ViewWebsitePagesModal] Rendering page ${index + 1}/${pages.length}:`, page.page_title, page.page_url, "Status:", page.status, "Minio Path:", page.minio_markdown_object_name); // DEBUG LOG
                    return (
                    <TableRow key={page.page_id}>
                      <TableCell>
                        <div className="font-medium">{page.page_title || 'N/A'}</div>
                        <a href={page.page_url} target="_blank" rel="noopener noreferrer" className="text-xs text-muted-foreground hover:underline flex items-center">
                          {page.page_url} <ExternalLink className="ml-1 h-3 w-3" />
                        </a>
                      </TableCell>
                      <TableCell className="capitalize">{page.status.replace(/_/g, ' ').toLowerCase()}</TableCell>
                      <TableCell>{page.content_length_markdown ?? 'N/A'}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleViewPageMarkdown(page)} title="View Page Content">
                          <Eye className="mr-1 h-4 w-4" /> View Content
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
                </TableBody>
              </Table>
            </div>
            </ScrollArea>
          )}
        </div>
        <DialogFooter className="p-4 border-t">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={handleClose}>Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>

      {/* Nested Modal for viewing specific page markdown */}
      {selectedPageForMarkdown && (
        <ViewFileContentModal
          isOpen={isViewPageMarkdownModalOpen}
          onClose={() => {
            setIsViewPageMarkdownModalOpen(false);
            setSelectedPageForMarkdown(null);
          }}
          source={selectedPageForMarkdown}
        />
      )}
    </Dialog>
  );
};

export default ViewWebsitePagesModal;