"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname } from "next/navigation"; // Import usePathname
import { ShieldCheck, LogOut, UserCircle, ArrowLeftRight } from 'lucide-react'; // Added icons

export default function AuthButton() {
  const { data: session, status } = useSession();
  const pathname = usePathname(); // Get current path

  if (status === "loading") {
    return <div className="w-20 h-8 bg-gray-200 animate-pulse rounded"></div>;
  }

  const isAdmin = session?.user?.role === "ADMIN";
  const isAdminPage = pathname.startsWith("/admin");

  if (session) {
    return (
      <div className="flex items-center gap-3"> {/* Increased gap slightly */}
        {isAdmin && (
          <Button asChild variant="ghost" size="sm" className="px-2">
            {isAdminPage ? (
              <Link href="/dashboard" title="View User Dashboard">
                <UserCircle className="h-5 w-5 mr-1 md:mr-2" /> 
                <span className="hidden md:inline">User View</span>
              </Link>
            ) : (
              <Link href="/admin/dashboard" title="Admin Panel">
                <ShieldCheck className="h-5 w-5 mr-1 md:mr-2" />
                <span className="hidden md:inline">Admin</span>
              </Link>
            )}
          </Button>
        )}
        <span className="text-sm hidden sm:inline"> {/* Hide name on very small screens if space is tight */}
          {session.user?.name || session.user?.email}
        </span>
        <Button onClick={() => signOut({ callbackUrl: '/' })} variant="outline" size="sm" className="px-2">
          <LogOut className="h-5 w-5 sm:mr-1 md:mr-2" />
          <span className="hidden sm:inline">Sign Out</span>
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Button asChild variant="outline" size="sm">
        <Link href="/sign-in">Sign In</Link>
      </Button>
      <Button asChild size="sm">
        <Link href="/sign-up">Sign Up</Link>
      </Button>
    </div>
  );
}