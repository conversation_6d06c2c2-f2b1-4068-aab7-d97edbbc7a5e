'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from 'sonner';
import { Loader2, Download } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Updated KnowledgeSource type for this component's needs
interface KnowledgeSourceForModal {
    source_id: string; // For FILE, SINGLE_PAGE this is the direct source_id. For WEBSITE_PAGE_MARKDOWN, this is parent_source_id.
    name: string;
    type: 'WEBSITE' | 'FILE' | 'TEXT' | 'SINGLE_PAGE' | 'WEBSITE_PAGE_MARKDOWN'; // Added new type
    location_uri?: string; // Original URL of the page/file
    metadata_json?: {
        content_type?: string; // For FILE type
        page_minio_path?: string; // For WEBSITE_PAGE_MARKDOWN type
        // other metadata if needed by this modal
    };
}

interface ViewFileContentModalProps {
  isOpen: boolean;
  onClose: () => void;
  source: KnowledgeSourceForModal | null;
}

const ViewFileContentModal: React.FC<ViewFileContentModalProps> = ({ isOpen, onClose, source }) => {
  const [fileContent, setFileContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contentType, setContentType] = useState<string | null>(null);

  const centralApiUrl = process.env.NEXT_PUBLIC_CENTRAL_API_URL;

  // URL for fetching content for inline display (text, or src for PDF iframe)
  const contentDisplayUrl = useMemo(() => {
    if (!source || !centralApiUrl) return null;
    const sourceTypeUpper = source.type?.toUpperCase();
    if (sourceTypeUpper === 'FILE') {
      // For PDF iframe or fetching text, we want inline disposition (default for the endpoint)
      return `${centralApiUrl}/api/v1/knowledge/sources/${source.source_id}/original-file`;
    }
    if (sourceTypeUpper === 'SINGLE_PAGE') {
      return `${centralApiUrl}/api/v1/knowledge/sources/${source.source_id}/page-markdown`;
    }
    if (sourceTypeUpper === 'WEBSITE_PAGE_MARKDOWN' && source.metadata_json?.page_minio_path) {
      return `${centralApiUrl}/api/v1/knowledge/sources/${source.source_id}/page-markdown?page_minio_path=${encodeURIComponent(source.metadata_json.page_minio_path)}`;
    }
    return null;
  }, [source, centralApiUrl]);

  // URL specifically for the "Download Original File" button, forcing attachment
  const forceDownloadUrl = useMemo(() => {
    if (source && source.type?.toUpperCase() === 'FILE' && centralApiUrl) {
      return `${centralApiUrl}/api/v1/knowledge/sources/${source.source_id}/original-file?disposition=attachment`;
    }
    return null;
  }, [source, centralApiUrl]);

  useEffect(() => {
    if (isOpen && source && contentDisplayUrl) { // Use contentDisplayUrl for fetching
      const fetchSourceContent = async () => {
        setIsLoading(true);
        setError(null);
        setFileContent(null);
        
        const sourceTypeUpper = source.type?.toUpperCase();
        let initialContentType = source.metadata_json?.content_type || null;
        if (sourceTypeUpper === 'SINGLE_PAGE' || sourceTypeUpper === 'WEBSITE_PAGE_MARKDOWN') {
            initialContentType = 'text/markdown'; // Assume markdown
        }
        setContentType(initialContentType);

        try {
          const response = await fetch(contentDisplayUrl); // Use contentDisplayUrl
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: "Failed to fetch content." }));
            throw new Error(errorData.detail || `Error ${response.status}`);
          }

          const fetchedRespContentType = response.headers.get('Content-Type');
          console.log("[ViewFileContentModal] Fetched Content-Type Header:", fetchedRespContentType, "for source:", source?.name, "Type:", source.type);
          
          // Use response content type if available, otherwise stick to initial/metadata
          const finalContentType = fetchedRespContentType || initialContentType;
          setContentType(finalContentType);

          const isFileAndEndsWithMd = sourceTypeUpper === 'FILE' && source.name && source.name.toLowerCase().endsWith('.md');

          if (finalContentType?.startsWith('text/') || finalContentType === 'application/markdown' || isFileAndEndsWithMd) {
            const text = await response.text();
            setFileContent(text);
            if (isFileAndEndsWithMd && (!finalContentType || finalContentType === 'application/octet-stream')) {
              // If it's an .md file but server sent octet-stream, override UI display type to markdown
              setContentType('text/markdown');
            }
          } else if (finalContentType === 'application/pdf' && sourceTypeUpper === 'FILE') {
            setFileContent('pdf_iframe');
          } else {
            setFileContent('download_only');
          }
        } catch (err) {
          console.error("Error fetching source content:", err);
          const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
          setError(errorMessage);
          toast.error(`Failed to load content: ${errorMessage}`);
        } finally {
          setIsLoading(false);
        }
      };

      const sourceTypeUpper = source.type?.toUpperCase();
      if (sourceTypeUpper === 'FILE') {
        const cType = source.metadata_json?.content_type?.toLowerCase();
        const isPotentiallyPreviewableFile =
            cType?.startsWith('text/') ||
            cType === 'application/pdf' ||
            cType === 'application/markdown' ||
            (source.name && source.name.toLowerCase().endsWith('.md')); // Also check .md extension here

        if (isPotentiallyPreviewableFile) {
           fetchSourceContent();
        } else {
          setContentType(cType || 'application/octet-stream');
          setFileContent('download_only');
          setIsLoading(false);
        }
      } else if (sourceTypeUpper === 'SINGLE_PAGE' || sourceTypeUpper === 'WEBSITE_PAGE_MARKDOWN') {
        fetchSourceContent(); // Always attempt to fetch for these, expect markdown
      }
    }
  }, [isOpen, source, contentDisplayUrl]); // Use contentDisplayUrl in dependency array

  const handleClose = () => {
    setFileContent(null);
    setError(null);
    setIsLoading(false);
    setContentType(null);
    onClose();
  };

  const renderContent = () => {
    if (isLoading) {
      return <div className="flex justify-center items-center h-40"><Loader2 className="h-8 w-8 animate-spin" /> <p className="ml-2">Loading content...</p></div>;
    }
    if (error) {
      return <div className="text-red-600 p-4">Error loading content: {error}</div>;
    }
    // Use contentDisplayUrl for PDF iframe src (which defaults to inline disposition)
    if (fileContent === 'pdf_iframe' && source?.type?.toUpperCase() === 'FILE' && contentDisplayUrl) {
      return (
        <iframe
          src={contentDisplayUrl}
          width="100%"
          height="500px"
          title={source?.name || 'File Preview'}
          className="border rounded-md"
        />
      );
    }
    if (fileContent && fileContent !== 'download_only' && fileContent !== 'pdf_iframe') {
      // Check if content type from header or initial assumption is markdown, or if it's a WEBSITE_PAGE_MARKDOWN/SINGLE_PAGE type, or if filename ends with .md
      const isMarkdown = contentType === 'text/markdown' ||
                         source?.type.toUpperCase() === 'WEBSITE_PAGE_MARKDOWN' ||
                         source?.type.toUpperCase() === 'SINGLE_PAGE' ||
                         (source?.type.toUpperCase() === 'FILE' && source?.name?.toLowerCase().endsWith('.md'));

      if (isMarkdown) {
        return (
          <ScrollArea className="h-[500px] w-full rounded-md border">
            <div className="prose dark:prose-invert max-w-none p-4"> {/* Apply prose styles and padding to this wrapper div */}
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {fileContent}
              </ReactMarkdown>
            </div>
          </ScrollArea>
        );
      }
      // Fallback for other text/* types (like text/plain)
      return (
        <ScrollArea className="h-[500px] w-full rounded-md border p-4 whitespace-pre-wrap break-all">
          {fileContent}
        </ScrollArea>
      );
    }
    if (fileContent === 'download_only' || !fileContent) {
        // For SINGLE_PAGE, if it's download_only, it means fetching failed or it wasn't markdown.
        // For FILE, it means it's not text/pdf.
        const message = source?.type?.toUpperCase() === 'SINGLE_PAGE'
            ? "Could not load Markdown preview. Please try downloading if an option is available, or check source."
            : "Preview for this file type is not available. Please download the file to view it.";
        return (
            <div className="p-4 text-center">
                <p className="mb-4">{message}</p>
            </div>
        );
    }
    return <p className="p-4">No content to display or unsupported file type for direct preview.</p>;
  };
  
  const dialogTitle = useMemo(() => {
    if (!source) return 'View Content';
    const sourceTypeUpper = source.type?.toUpperCase();
    if (sourceTypeUpper === 'FILE') return `View File: ${source.name || 'N/A'}`;
    if (sourceTypeUpper === 'SINGLE_PAGE' || sourceTypeUpper === 'WEBSITE_PAGE_MARKDOWN') {
      return `View Page Content: ${source.name || source.location_uri || 'N/A'}`;
    }
    return 'View Content';
  }, [source]);

  // forceDownloadUrl (defined earlier, around line 57) is used for the download button.
  // contentDisplayUrl (defined earlier, around line 40) is used for the iframe src.
  // fileSpecificDownloadUrl is redundant and can be removed.

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader className="p-4 border-b">
          <DialogTitle>{dialogTitle}</DialogTitle>
        </DialogHeader>
        <div className="flex-grow overflow-y-auto p-4">
          {renderContent()}
        </div>
        <DialogFooter className="p-4 border-t flex-col sm:flex-row sm:justify-between items-center">
            <div className="text-xs text-muted-foreground">
                Type: {contentType ||
                       (source?.type?.toUpperCase() === 'SINGLE_PAGE' || source?.type?.toUpperCase() === 'WEBSITE_PAGE_MARKDOWN'
                           ? 'text/markdown'
                           : source?.metadata_json?.content_type) ||
                       'Unknown'}
            </div>
            <div className="flex gap-2 mt-2 sm:mt-0">
                {/* Use forceDownloadUrl for the download button */}
                {(source?.type?.toUpperCase() === 'FILE') && forceDownloadUrl && (
                    <Button variant="outline" asChild>
                        <a href={forceDownloadUrl} download={source?.name || 'download'}> {/* download attribute still useful */}
                            <Download className="mr-2 h-4 w-4" /> Download Original File
                        </a>
                    </Button>
                )}
                {/* No download button for WEBSITE_PAGE_MARKDOWN or SINGLE_PAGE by default in this modal */}
                <DialogClose asChild>
                    <Button type="button" variant="secondary" onClick={handleClose}>Close</Button>
                </DialogClose>
            </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewFileContentModal;