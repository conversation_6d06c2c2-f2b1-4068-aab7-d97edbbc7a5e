"use client";

import Link from "next/link";
import { useSession } from "next-auth/react";
import AuthButton from "@/components/auth/AuthButton";

export default function HeaderNav() {
  const { data: session } = useSession();

  return (
<nav className="flex items-center space-x-4">
  <Link href="/blog" className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">
    Blog
  </Link>
  {session?.user && (
    <Link href="/dashboard" className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">
      Dashboard
    </Link>
  )}
  <AuthButton />
</nav>
  );
}
