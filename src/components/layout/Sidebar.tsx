'use client';

import React from 'react'; // Removed useState as it's no longer needed
import Link from 'next/link';
// Removed useRouter and toast as they are no longer needed for direct navigation
import { Button } from '@/components/ui/button';
import { Home, BotMessageSquare, PlusCircle, History, Settings, LifeBuoy, CreditCard } from 'lucide-react'; // Removed Loader2

const Sidebar = () => {
  // Removed router and isCreatingChatbot state

  const navItems = [
    { href: '/dashboard', label: 'Dashboard', icon: Home },
    { href: '/dashboard/builder', label: 'My Chatbots', icon: BotMessageSquare },
    { href: '/dashboard/chat-history', label: 'Chat History', icon: History },
    { href: '/dashboard/settings', label: 'Settings', icon: Settings },
    { href: '/dashboard/support', label: 'Support', icon: LifeBuoy },
    { href: '/dashboard/billing', label: 'Billing', icon: Credit<PERSON>ard },
  ];

  // Removed handleCreateChatbot function

  return (
    <aside className="w-64 bg-gray-50 p-5 border-r border-gray-200 dark:bg-gray-900 dark:border-gray-800 flex flex-col fixed h-full mt-[65px] z-10">
      <nav className="flex flex-col space-y-2 flex-grow">
        {navItems.map((item) => (
          <Button
            key={item.label}
            variant="ghost"
            className="w-full justify-start text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            asChild
          >
            <Link href={item.href}>
              <item.icon className="mr-3 h-5 w-5" />
              {item.label}
            </Link>
          </Button>
        ))}
        {/* Changed "Build Chatbot" button to a Link wrapped in a Button with asChild */}
        <Button
          variant="ghost"
          className="w-full justify-start text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
          asChild
        >
          <Link href="/dashboard/builder/new">
            <PlusCircle className="mr-3 h-5 w-5" />
            Build Chatbot
          </Link>
        </Button>
      </nav>
    </aside>
  );
};

export default Sidebar;