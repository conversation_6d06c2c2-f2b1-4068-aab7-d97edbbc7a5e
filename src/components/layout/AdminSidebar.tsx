'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { LayoutDashboard, Users, MessageSquareText, Cpu, Cog, FileText, Settings as AdminSettingsIcon } from 'lucide-react'; // Added more icons

const AdminSidebar = () => {
  const navItems = [
    { href: '/admin/dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { href: '/admin/agents', label: 'Agents', icon: Users }, // Placeholder icon, consider specific one if available
    { href: '/admin/prompts', label: 'Prompts', icon: MessageSquareText },
    { href: '/admin/llms', label: 'LLMs', icon: Cpu },
    { href: '/admin/mcps', label: 'MCPs', icon: Cog }, // Using Cog as a generic for "Managed Components/Processes"
    { href: '/admin/plan-management', label: 'Plan Management', icon: FileText },
    { href: '/admin/blog', label: 'Blog', icon: FileText },
    { href: '/admin/settings', label: 'Settings', icon: AdminSettingsIcon },
  ];

  return (
    <aside className="w-64 bg-gray-100 p-5 border-r border-gray-200 dark:bg-gray-800 dark:border-gray-700 flex flex-col fixed h-full mt-[65px] z-10"> {/* Ensure z-index is appropriate */}
      <nav className="flex flex-col space-y-2 flex-grow">
        {navItems.map((item) => (
          <Button
            key={item.label}
            variant="ghost"
            className="w-full justify-start text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            asChild
          >
            <Link href={item.href}>
              <item.icon className="mr-3 h-5 w-5" />
              {item.label}
            </Link>
          </Button>
        ))}
      </nav>
    </aside>
  );
};

export default AdminSidebar;
