"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Copy } from "lucide-react";

interface EmbedSnippetModalProps {
  isOpen: boolean;
  onClose: () => void;
  chatbotInstanceId: string | null;
  chatbotName?: string;
}

export default function EmbedSnippetModal({ isOpen, onClose, chatbotInstanceId, chatbotName }: EmbedSnippetModalProps) {
  if (!isOpen || !chatbotInstanceId) return null;

  const snippet = `<div id="aiq-chatbot-container" data-chatbot-id="${chatbotInstanceId}"></div>
<script async src="${process.env.NEXT_PUBLIC_APP_URL}/embed/v1/aiq-chat-widget.js"></script>`;

  const handleCopySnippet = () => {
    navigator.clipboard.writeText(snippet)
      .then(() => {
        toast.success("Embed code copied to clipboard!");
      })
      .catch(err => {
        toast.error("Failed to copy code. Please try again.");
        console.error("Failed to copy text: ", err);
      });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Embed Chatbot: {chatbotName || "Your Chatbot"}</DialogTitle>
          <DialogDescription>
            Copy and paste this code snippet into the HTML of your website where you want the chatbot to appear.
            The widget will typically be a floating icon unless configured for direct embedding.
          </DialogDescription>
        </DialogHeader>
        <div className="my-4">
          <Textarea
            value={snippet}
            readOnly
            rows={8}
            className="w-full p-2 border rounded-md font-mono text-sm bg-gray-50 dark:bg-gray-800 resize-none"
          />
        </div>
        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={onClose}>Close</Button>
          <Button onClick={handleCopySnippet}>
            <Copy className="w-4 h-4 mr-2" /> Copy Code
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}