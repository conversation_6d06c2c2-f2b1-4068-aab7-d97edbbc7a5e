"use client";

import { useState } from "react";
import { CheckIcon } from "@radix-ui/react-icons";
import { PolarEmbedCheckout } from "@polar-sh/checkout/embed";
import Link from "next/link";
import { type User } from "@prisma/client";
import { type SubscriptionPlan } from "@/lib/subscription";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { Icons } from "@/components/shared/icons";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface PricingCardsProps {
  userId: User["id"];
  subscriptionPlan: SubscriptionPlan;
}

export function PricingCards({
  userId,
  subscriptionPlan,
}: PricingCardsProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [selectedInterval, setSelectedInterval] = useState<"month" | "year">(
    subscriptionPlan.interval || "month",
  );

  const plans = [
    {
      name: "Free",
      description: "The free plan for basic usage.",
      features: ["5 Chatbots", "1000 Messages/month"],
      monthlyPrice: 0,
      yearlyPrice: 0,
      polarPriceId: null,
      polarProductId: null,
      isFree: true,
    },
    {
      name: "Base",
      description: "The base plan for growing businesses.",
      features: ["25 Chatbots", "10,000 Messages/month", "Customizable UI"],
      monthlyPrice: 10,
      yearlyPrice: 100,
      polarPriceId: {
        month: process.env.NEXT_PUBLIC_POLAR_BASE_MONTHLY_ID,
        year: process.env.NEXT_PUBLIC_POLAR_BASE_YEARLY_ID,
      },
      isFree: false,
    },
    {
      name: "Pro",
      description: "The pro plan for large-scale operations.",
      features: [
        "Unlimited Chatbots",
        "Unlimited Messages",
        "Advanced Analytics",
        "Priority Support",
      ],
      monthlyPrice: 50,
      yearlyPrice: 500,
      polarPriceId: {
        month: process.env.NEXT_PUBLIC_POLAR_PRO_MONTHLY_ID,
        year: process.env.NEXT_PUBLIC_POLAR_PRO_YEARLY_ID,
      },
      isFree: false,
    },
  ];

  async function onSubmit(priceId: string) {
    if (!priceId) {
      console.error("No price ID provided");
      return;
    }

    setIsLoading(priceId);

    try {
      console.log("Sending checkout request with:", {
        priceId,
        returnUrl: window.location.href
      });

      const response = await fetch("/api/polar/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          priceId: priceId,
          returnUrl: window.location.href,
          metadata: {
            userId: userId
          }
        }),
      });

      if (!response?.ok) {
        const errorData = await response.json();
        console.error("Checkout API error:", errorData);
        
        toast({
          title: "Checkout failed",
          description: errorData.error || "Please refresh the page and try again.",
          variant: "destructive",
        });
        return;
      }

      const session = await response.json();
      console.log("Checkout session response:", session);

      if (session && session.url) {
        try {
          // Create and show the Polar embedded checkout with light theme
          const checkout = await PolarEmbedCheckout.create(session.url, {
            server: 'sandbox', // Configure for sandbox environment
            theme: 'light'
          });

          // Handle successful payment
          checkout.addEventListener('success', (event) => {
            console.log("Purchase successful!", event.detail);
            
            toast({
              title: "Payment successful!",
              description: "Your subscription has been activated.",
            });

            // Refresh the page to show updated subscription status
            setTimeout(() => {
              window.location.reload();
            }, 2000);
          });

          // Handle checkout close
          checkout.addEventListener('close', () => {
            console.log("Checkout modal closed");
            setIsLoading(null);
          });

          // Note: PolarEmbedCheckout doesn't have an 'error' event
          // Errors are handled through the try/catch and the 'close' event

        } catch (checkoutError) {
          console.error("Error creating Polar checkout:", checkoutError);
          toast({
            title: "Checkout initialization failed",
            description: "Could not open payment modal. Please try again.",
            variant: "destructive",
          });
        }
      } else {
        console.error("No checkout URL in response:", session);
        toast({
          title: "Invalid response",
          description: "No checkout URL received from server.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error during checkout process:", error);
      toast({
        title: "Something went wrong.",
        description: "Please refresh the page and try again.",
        variant: "destructive",
      });
    } finally {
      // Only reset loading if there was an error before checkout creation
      if (isLoading === priceId) {
        setIsLoading(null);
      }
    }
  }

  return (
    <div className="flex w-full flex-col gap-6">
      <div className="flex items-center justify-center space-x-4">
        <RadioGroup
          defaultValue={selectedInterval}
          onValueChange={(value: "month" | "year") => setSelectedInterval(value)}
          className="grid grid-cols-2 gap-x-1 rounded-md bg-muted p-1 text-center"
        >
          <Label
            htmlFor="month"
            className={cn(
              "cursor-pointer rounded-md px-3 py-1 text-sm font-medium",
              selectedInterval === "month"
                ? "bg-white shadow-sm"
                : "text-muted-foreground",
            )}
          >
            <RadioGroupItem value="month" id="month" className="sr-only" />
            Monthly
          </Label>
          <Label
            htmlFor="year"
            className={cn(
              "cursor-pointer rounded-md px-3 py-1 text-sm font-medium",
              selectedInterval === "year"
                ? "bg-white shadow-sm"
                : "text-muted-foreground",
            )}
          >
            <RadioGroupItem value="year" id="year" className="sr-only" />
            Yearly
          </Label>
        </RadioGroup>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {plans.map((plan) => {
          const isCurrentPlan =
            subscriptionPlan.name === plan.name;

          const price =
            selectedInterval === "month" ? plan.monthlyPrice : plan.yearlyPrice;
          // Use actual price IDs from environment variables
          let priceId = null;
          if (plan.name === "Base") {
            priceId = selectedInterval === "month"
              ? process.env.NEXT_PUBLIC_POLAR_BASE_MONTHLY_ID
              : process.env.NEXT_PUBLIC_POLAR_BASE_YEARLY_ID;
          } else if (plan.name === "Pro") {
            priceId = selectedInterval === "month"
              ? process.env.NEXT_PUBLIC_POLAR_PRO_MONTHLY_ID
              : process.env.NEXT_PUBLIC_POLAR_PRO_YEARLY_ID;
          }

          return (
            <Card
              key={plan.name}
              className={cn(
                "flex flex-col",
                isCurrentPlan && "border-2 border-primary",
              )}
            >
              <CardHeader>
                <CardTitle className="text-lg">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>
              <CardContent className="grid flex-1 place-items-start gap-6">
                <div className="text-3xl font-bold">
                  ${price}
                  {!plan.isFree && (
                    <span className="text-sm font-normal text-muted-foreground">
                      /{selectedInterval}
                    </span>
                  )}
                </div>
                <ul className="grid gap-3 text-sm text-muted-foreground">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center">
                      <CheckIcon className="mr-2 h-4 w-4" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                {isCurrentPlan ? (
                  <Button className="w-full" disabled>
                    Current Plan
                  </Button>
                ) : !subscriptionPlan.isFree && plan.isFree ? (
                  <Button asChild className="w-full">
                    <Link href="/api/polar/customer-portal">Downgrade</Link>
                  </Button>
                ) : !plan.isFree && !subscriptionPlan.isFree ? (
                  <Button asChild className="w-full">
                    <Link href="/api/polar/customer-portal">
                      {plan.monthlyPrice! > (subscriptionPlan.monthlyPrice ?? 0) ? "Upgrade" : "Downgrade"}
                    </Link>
                  </Button>
                ) : (
                  <Button
                    className="w-full"
                    onClick={() => priceId && onSubmit(priceId)}
                    disabled={isLoading === priceId || !priceId}
                  >
                    {isLoading === priceId && (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Upgrade
                  </Button>
                )}
              </CardFooter>
            </Card>
          );
        })}
      </div>
    </div>
  );
}