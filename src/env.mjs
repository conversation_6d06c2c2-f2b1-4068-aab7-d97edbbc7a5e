import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  server: {
    DATABASE_URL: z.string().url(),
    NEXTAUTH_URL: z.string().url(),
    NEXTAUTH_SECRET: z.string(),
    NODE_ENV: z.enum(["development", "test", "production"]),
    POLAR_ACCESS_TOKEN: z.string(),
    POLAR_SUCCESS_URL: z.string().url(),
    POLAR_WEBHOOK_SECRET: z.string(),
    SENDGRID_SENDER_EMAIL: z.string().email(),
    SENDGRID_REPLYTO_ADDRESS: z.string().email(),
    SENDGRID_API_KEY: z.string(),
    SENDGRID_SENDER_FROM_NAME: z.string(),
  },
  client: {
    NEXT_PUBLIC_CENTRAL_API_URL: z.string().url(),
    NEXT_PUBLIC_APP_URL: z.string().url(),
    NEXT_PUBLIC_POLAR_SUCCESS_URL: z.string().url(),
    NEXT_PUBLIC_POLAR_BASE_MONTHLY_ID: z.string(),
    NEXT_PUBLIC_POLAR_BASE_YEARLY_ID: z.string(),
    NEXT_PUBLIC_POLAR_PRO_MONTHLY_ID: z.string(),
    NEXT_PUBLIC_POLAR_PRO_YEARLY_ID: z.string(),
    NEXT_PUBLIC_POLAR_BASE_PRODUCT_ID: z.string().optional(),
    NEXT_PUBLIC_POLAR_PRO_PRODUCT_ID: z.string().optional(),
  },
  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NODE_ENV: process.env.NODE_ENV,
    POLAR_ACCESS_TOKEN: process.env.POLAR_ACCESS_TOKEN,
    POLAR_SUCCESS_URL: process.env.POLAR_SUCCESS_URL,
    POLAR_WEBHOOK_SECRET: process.env.POLAR_WEBHOOK_SECRET,
    SENDGRID_SENDER_EMAIL: process.env.SENDGRID_SENDER_EMAIL,
    SENDGRID_REPLYTO_ADDRESS: process.env.SENDGRID_REPLYTO_ADDRESS,
    SENDGRID_API_KEY: process.env.SENDGRID_API_KEY,
    SENDGRID_SENDER_FROM_NAME: process.env.SENDGRID_SENDER_FROM_NAME,
    NEXT_PUBLIC_CENTRAL_API_URL: process.env.NEXT_PUBLIC_CENTRAL_API_URL,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_POLAR_SUCCESS_URL: process.env.NEXT_PUBLIC_POLAR_SUCCESS_URL,
    NEXT_PUBLIC_POLAR_BASE_MONTHLY_ID: process.env.POLAR_BASE_MONTHLY_ID,
    NEXT_PUBLIC_POLAR_BASE_YEARLY_ID: process.env.POLAR_BASE_YEARLY_ID,
    NEXT_PUBLIC_POLAR_PRO_MONTHLY_ID: process.env.POLAR_PRO_MONTHLY_ID,
    NEXT_PUBLIC_POLAR_PRO_YEARLY_ID: process.env.POLAR_PRO_YEARLY_ID,
    NEXT_PUBLIC_POLAR_BASE_PRODUCT_ID: process.env.POLAR_BASE_PRODUCT_ID,
    NEXT_PUBLIC_POLAR_PRO_PRODUCT_ID: process.env.POLAR_PRO_PRODUCT_ID,
  },
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  emptyStringAsUndefined: true,
});