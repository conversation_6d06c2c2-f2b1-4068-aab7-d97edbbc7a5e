version: '3.8'

services:
  document_query_mcp:
    build:
      context: .  # Uses the Dockerfile in the current directory
      dockerfile: Dockerfile
    image: document-query-mcp:latest # Optional: Tags the image after building
    container_name: doc_query_mcp_compose_instance
    ports:
      - "8045:8045"
    env_file:
      - .env  # Loads environment variables from .env in the current directory
    restart: unless-stopped
    # If your fastmcp_sdk_local needs to be mounted as a volume for development (live changes),
    # you could add a volume mount here. However, the Dockerfile already copies it.
    # For production or simpler setups, copying via Dockerfile is usually sufficient.
    # Example for development volume (uncomment and adjust if needed):
    # volumes:
    #   - ./fastmcp_sdk_local:/app/fastmcp_sdk 
    # Note: This would override the one copied by the Dockerfile.
    # Ensure consistency if you use this.