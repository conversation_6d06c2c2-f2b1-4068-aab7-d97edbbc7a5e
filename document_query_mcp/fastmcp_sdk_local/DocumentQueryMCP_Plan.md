# Plan: Intelligent Document Query MCP

**Date:** 2025-05-30

**Version:** 1.0

## 1. MCP Goal & Overview

*   **MCP Name:** `DocumentQueryMCP`
*   **Assigned Port:** `8045`
*   **Purpose:** To provide LangGraph agents with the ability to perform in-depth queries on the full content of original source documents when standard RAG chunks are insufficient. The MCP handles fetching original documents, converting them to Markdown if necessary (storing temporary Markdown in a dedicated Minio bucket), searching the full Markdown content, and returning relevant paragraphs as snippets.
*   **Development Base:** `/home/<USER>/agentiveaiq/fastmcp`
*   **MCP Guide Reference:** [`agentiveaiq/mcpguide.md`](agentiveaiq/mcpguide.md:1)

## 2. Assumptions

*   **`document_identifier` format:** `source_bucket_name/filename.ext` (e.g., `public-reports/annual_Q1.pdf`). The agent or upstream system is responsible for providing the correct source bucket name.
*   **Conversion Strategy:** MCP-led, on-demand conversion of documents (PDF, DOCX, TXT) to Markdown.
*   **Original Document Access:** Original documents are publicly readable from their respective Minio buckets via HTTP GET. The MCP will construct the full URL using a base Minio endpoint and the provided `document_identifier`.
*   **Temporary Markdown Storage:**
    *   **Bucket:** `doc-mcp` (Minio)
    *   **Endpoint for Upload:** `http://************:8000`
    *   **Access Key for Upload:** `QyrurJhQLNA7MSIeJ0rK`
    *   **Secret Key for Upload:** `SRC4u4hhZuxgE1jAdmHcspdJuMyhhy04x5d0GdU3`
    *   **Lifecycle:** 3-day auto-deletion policy on the `doc-mcp` bucket.
*   **Search Results:** The MCP will return full paragraphs that contain matches for the search query.
*   **Search Type:** Advanced keyword search (case-insensitive, stemmed).

## 3. MCP Structure (Python Skeleton)

The MCP will be developed in a new directory within `/home/<USER>/agentiveaiq/fastmcp`, for example, `/home/<USER>/agentiveaiq/fastmcp/document_query_mcp/`.

**File: `main.py` (Illustrative Skeleton)**

```python
# /home/<USER>/agentiveaiq/fastmcp/document_query_mcp/main.py
from fastmcp import FastMCP
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import os

# --- Configuration ---
MINIO_ENDPOINT = os.getenv("DQ_MINIO_ENDPOINT", "http://************:8000")
TEMP_MD_BUCKET_NAME = os.getenv("DQ_TEMP_MD_BUCKET_NAME", "doc-mcp")
TEMP_MD_ACCESS_KEY = os.getenv("DQ_TEMP_MD_ACCESS_KEY", "QyrurJhQLNA7MSIeJ0rK")
TEMP_MD_SECRET_KEY = os.getenv("DQ_TEMP_MD_SECRET_KEY", "SRC4u4hhZuxgE1jAdmHcspdJuMyhhy04x5d0GdU3") # Store securely, e.g., in Docker secrets or env vars

# --- Pydantic Models for Tool Arguments ---
class QueryFullDocumentArgs(BaseModel):
    document_identifier: str = Field(
        ..., 
        description="Identifier for the original document. Format: 'source_bucket_name/filename.ext' (e.g., 'financial-reports/Q1_2024_report.pdf'). The MCP constructs the full public Minio URL."
    )
    search_query: str = Field(
        ..., 
        description="The specific query or phrase the agent wants to search for within the full document."
    )
    # Optional: Consider adding if extension inference is problematic
    # content_type: Optional[str] = Field(None, description="Explicit content type of the original document (e.g., 'pdf', 'docx', 'txt', 'md'). If None, MCP will infer from extension.")

# --- MCP Initialization ---
mcp = FastMCP(
    name="DocumentQueryMCP",
    instructions="""
# Document Query MCP - v1.0

## TOOL DESCRIPTION: 
This MCP empowers AI agents to perform deep analysis of full source documents. When a standard RAG chunk lacks sufficient context, the agent can use this MCP to fetch the original document (PDF, DOCX, TXT, MD), convert it to Markdown (if needed), and perform an advanced keyword search within its entire content. The MCP returns relevant paragraphs, offering richer context than initial chunks. Temporary Markdown conversions are stored in the 'doc-mcp' Minio bucket (endpoint: http://************:8000) with a 3-day auto-deletion policy.

## QUICK START:
Query a full document when RAG snippets are insufficient. Provide the document identifier (bucket/filename) and your search terms.
EXAMPLE: {"document_identifier": "my-public-bucket/annual_report_2023.pdf", "search_query": "environmental impact initiatives"}

## AVAILABLE TOOLS:
- query_full_document: Fetches, (converts if needed), and searches a full document, returning relevant paragraphs.

## COMMON WORKFLOWS:
1. RAG Retrieval → Insufficient Context → Query Full Document: Agent receives RAG chunks, finds context lacking, uses this tool with original document ID and specific search terms to get more detailed paragraphs.

## LIMITATIONS:
- Relies on public accessibility of original document buckets in Minio (constructed URL: MINIO_ENDPOINT/document_identifier).
- Document conversion time can vary based on size and type. Supported types: PDF, DOCX, TXT, MD.
- Search effectiveness depends on the quality of the search query and document content.
- Temporary Markdown files in 'doc-mcp' are wiped after 3 days.

## TROUBLESHOOTING:
SYMPTOM: Document not found
CAUSE: Incorrect 'document_identifier' (bucket or filename typo) OR document not publicly accessible at the constructed URL.
SOLUTION: Verify the 'document_identifier' and ensure the source Minio bucket and object are publicly readable. Check Minio logs if possible. The MCP attempts to fetch from MINIO_ENDPOINT/document_identifier.

SYMPTOM: Conversion failed
CAUSE: Unsupported file format (other than PDF, DOCX, TXT, MD), corrupted document, or issue with conversion libraries.
SOLUTION: Ensure document is a supported type and not corrupted. Check MCP logs for specific conversion errors.

SYMPTOM: No relevant snippets returned
CAUSE: Search query too broad/narrow, or the information is not present in the document.
SOLUTION: Refine the search query. Try broader terms or more specific phrases. Manually verify if the information exists in the document.
    """
)

# --- Tool Definition ---
@mcp.tool()
async def query_full_document(args: QueryFullDocumentArgs) -> Dict[str, Any]:
    """
    ### query_full_document:
    TOOL USE: Fetches an original source document from a public Minio bucket (path constructed from MINIO_ENDPOINT + document_identifier), converts it to Markdown if it's not already (e.g., PDF, DOCX, TXT -> MD), stores the temporary Markdown in the 'doc-mcp' Minio bucket, performs an advanced keyword search (case-insensitive, stemmed) on the full Markdown content, and returns the full paragraphs containing the search hits. This tool is used when initial RAG chunks are insufficient and deeper context from the source document is required.

    INPUTS:
      - REQUIRED:
        - document_identifier: String - Identifier for the original document. Format: 'source_bucket_name/filename.ext' (e.g., 'financial-reports/Q1_2024_report.pdf').
        - search_query: String - The text phrase or keywords the agent wants to find within the full document.
      
      - OPTIONAL:
        # - content_type: String - Explicit content type (e.g., "pdf", "docx"). If omitted, inferred from document_identifier's extension.

    DEFAULT VALUES:
      - (None applicable for required inputs)

    VARIABLE OPTIONS:
      - (None directly applicable to inputs, but search behavior includes stemming and case-insensitivity)

    PARAMETER RELATIONSHIPS:
      - The 'document_identifier' must correctly point to a publicly accessible file in Minio.
      - The 'search_query' should be specific enough to find relevant information.

    OUTPUT: Returns a JSON object containing:
      - success: Boolean - True if the operation (including fetch, optional conversion, and search) was successful, False otherwise.
      - message: String - A status message (e.g., "Snippets retrieved successfully.", "Document not found.", "Conversion error.", "Search query yielded no results.").
      - retrieved_paragraphs: Array of Strings - A list of full paragraph strings from the document that contained matches for the search_query. Empty if no matches.
      - original_document_url: String - The full public URL constructed and used to fetch the original document.
      - temporary_markdown_url: String (Nullable) - The URL to the temporary Markdown file created in the 'doc-mcp' Minio bucket. Present if conversion occurred and was successful. Null otherwise.
      - search_terms_found: Array of Strings (Nullable) - List of unique stemmed search terms that were actually found in the document.

    ERROR HANDLING:
      - Document Not Found: {"success": false, "message": "Error: Original document not found at [URL].", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": null}
      - Conversion Failure: {"success": false, "message": "Error: Failed to convert document. Details: [error_details]", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": null}
      - Minio Upload Failure (Temp MD): {"success": false, "message": "Error: Failed to upload temporary Markdown to Minio. Details: [error_details]", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": null}
      - Search Yielded No Results: {"success": true, "message": "Search completed. No matching paragraphs found for the query.", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": "[URL_if_conversion_happened]"}
      - General Processing Error: {"success": false, "message": "Error: An unexpected error occurred. Details: [error_details]", "retrieved_paragraphs": [], "original_document_url": null, "temporary_markdown_url": null}

    EXAMPLE USAGE:
    ```json
    {
      "document_identifier": "company-knowledgebase/onboarding_guide_v3.pdf",
      "search_query": "policy on remote work"
    }
    ```

    RELATED TOOLS:
      - (This MCP is standalone but complements a RAG system.)

    USER HINTS:
      - IDENTIFYING NEED: Use when the agent has retrieved chunks via RAG but determines the context is insufficient to fully answer a query, and the metadata indicates a source document is available.
      - GATHERING INFO: The agent needs the 'document_identifier' (from RAG metadata, possibly transformed) and a specific 'search_query' based on what information is missing.
      - PRESENTING RESULTS: If successful, the agent receives full paragraphs. It should then synthesize this richer information into its response. If no results, inform the user the specific query didn't find matches in the full document.

    CONVERSATION EXAMPLES:
    User: "The RAG snippet about 'Project Alpha's budget' is too vague. Can you find more details in the main project proposal PDF?"
    Agent (Thinking): "The user needs more detail. The RAG metadata points to 'project_docs/alpha_proposal.pdf'. I will use the query_full_document tool to search for 'budget allocation details' within this PDF."
    Agent (Tool Call): query_full_document(document_identifier="project_docs/alpha_proposal.pdf", search_query="budget allocation details")
    """
    # --- Tool Implementation Logic (Placeholder) ---
    # 1. Construct full public URL: original_document_url = f"{MINIO_ENDPOINT}/{args.document_identifier}"
    # 2. Fetch original document (e.g., using httpx). Handle errors.
    # 3. Determine file type (e.g., from args.document_identifier.split('.')[-1]).
    # 4. If not Markdown:
    #    a. Convert to Markdown (use appropriate library: PyMuPDF for PDF, python-docx for DOCX, etc.). Handle errors.
    #    b. Connect to Minio (TEMP_MD_ENDPOINT, TEMP_MD_ACCESS_KEY, TEMP_MD_SECRET_KEY).
    #    c. Upload converted Markdown to TEMP_MD_BUCKET_NAME. Construct temporary_markdown_url. Handle errors.
    #    d. Content_to_search = converted Markdown.
    # 5. Else (is Markdown):
    #    a. Content_to_search = original document content.
    #    b. temporary_markdown_url = None (or original_document_url if it's already MD in a sense)
    # 6. Perform search on Content_to_search:
    #    a. Preprocess search_query (lowercase, stem).
    #    b. Split Content_to_search into paragraphs.
    #    c. For each paragraph: stem, check for all stemmed search terms. Collect matching original paragraphs.
    # 7. Populate and return the output dictionary.
    print(f"Executing query_full_document with: {args}")
    return {
        "success": True, 
        "message": "Placeholder: Tool logic not yet implemented.",
        "retrieved_paragraphs": ["This is a placeholder paragraph from the document matching your query."],
        "original_document_url": f"{MINIO_ENDPOINT}/{args.document_identifier}",
        "temporary_markdown_url": f"{MINIO_ENDPOINT}/{TEMP_MD_BUCKET_NAME}/temp_{args.document_identifier.replace('/', '_')}.md" if ".pdf" in args.document_identifier else None, # Example
        "search_terms_found": [term for term in args.search_query.split()] # Example
    }

# --- Main Execution Block ---
if __name__ == "__main__":
    # Ensure environment variables for Minio credentials are set if running locally for testing
    # For production, these would be set in the Docker environment or orchestration layer.
    print(f"Attempting to run DocumentQueryMCP on port 8045...")
    print(f"Minio Endpoint for originals: {MINIO_ENDPOINT}")
    print(f"Minio Temp Bucket for MD: {TEMP_MD_BUCKET_NAME}")
    # Add other necessary setup if required
    
    # It's good practice to ensure the Minio client is configured here if needed globally
    # or within the tool call for temporary uploads.
    
    mcp.run(transport="sse", host="0.0.0.0", port=8045, log_level="info")

```

## 4. MCP Internal Workflow Diagram

```mermaid
graph TD
    A[Agent calls query_full_document tool with document_identifier & search_query] --> B{MCP Receives Request};
    B --> C[1. Construct Full Public URL for Original Document (MINIO_ENDPOINT + document_identifier)];
    C --> D{2. Fetch Original Document (HTTP GET)};
    D -- Success --> E{3. Determine File Type (from extension)};
    D -- Fetch Error --> X[Return Error: Document Not Found];
    E -- Is Markdown? --> H[5a. Prepare for Search (use original content)];
    E -- Not Markdown (PDF, DOCX, TXT) --> F[4a. Convert to Markdown];
    F -- Conversion Error --> Y[Return Error: Conversion Failed];
    F -- Success --> G[4b. Upload Temp MD to 'doc-mcp' Minio Bucket (using configured credentials)];
    G -- Upload Error --> Z[Return Error: Minio Upload Failed];
    G -- Success --> H[5a. Prepare for Search (use converted MD)];
    H --> I[5b. Perform Advanced Keyword Search on Markdown Content (stemmed, case-insensitive)];
    I -- No Matches --> J[Return Success: No Matches Found];
    I -- Matches Found --> K[5c. Extract Matching Full Original Paragraphs];
    K --> L[6. Return Success with Retrieved Paragraphs, Original URL, Temp MD URL (if applicable)];

    classDef error fill:#ffcccc,stroke:#cc0000,stroke-width:2px;
    class X,Y,Z error;
```

## 5. Key Implementation Details for `query_full_document` Logic

*   **Minio Client:** Use the `minio` Python library for uploading the temporary Markdown to the `doc-mcp` bucket. Initialize with `TEMP_MD_ACCESS_KEY` and `TEMP_MD_SECRET_KEY`.
*   **HTTP Client:** Use `httpx` (async) or `requests` (sync, would need `asyncio.to_thread` if used in async tool) for fetching the original public document. `httpx` is preferred for async FastMCP tools.
*   **Document Conversion Libraries:**
    *   **PDF:** `PyMuPDF` (fitz) is recommended for robust text extraction.
    *   **DOCX:** `python-docx` for reading content.
    *   **TXT:** Direct read.
    *   **Pandoc (via `pypandoc`):** A powerful alternative if its system dependency is acceptable in the Docker image. Can simplify handling multiple input types to Markdown.
*   **Search Logic:**
    *   **Paragraph Splitting:** For Markdown, split content by one or more newline characters (e.g., `re.split(r'\n\s*\n+', markdown_content)` to handle various spacing).
    *   **Stemming:** Use NLTK (e.g., `nltk.stem.PorterStemmer` or `nltk.stem.SnowballStemmer`). Ensure NLTK data (like `punkt` for tokenization if needed by stemmer or paragraph logic) is available in the Docker image.
    *   **Matching:** For each paragraph, stem its content and check if all stemmed terms from the `search_query` are present.
*   **Configuration Management:** Minio endpoint, bucket names, and credentials should be managed via environment variables (as shown with `os.getenv`) for security and flexibility, especially for Docker deployment.
*   **Error Handling:** Implement robust error handling for network issues, file errors, conversion problems, and Minio operations.
*   **Asynchronous Operations:** Since FastMCP tools are `async`, ensure I/O-bound operations (HTTP requests, file operations if not fully async, Minio client calls if they are blocking) are handled appropriately (e.g., using async libraries like `httpx`, or `asyncio.to_thread` for blocking calls). The `minio` library's standard client is blocking; consider alternatives or careful wrapping if performance under high concurrency is critical. For typical MCP usage, `asyncio.to_thread` might be sufficient.

## 6. Dockerization Considerations (Brief)

*   Create a `Dockerfile` in the MCP's directory (`/home/<USER>/agentiveaiq/fastmcp/document_query_mcp/`).
*   Base image: A suitable Python image (e.g., `python:3.10-slim`).
*   Install system dependencies if needed (e.g., for Pandoc if used, or build tools for some Python libraries).
*   Copy `main.py` and any other necessary files (e.g., `requirements.txt`).
*   Install Python dependencies from `requirements.txt` (including `fastmcp`, `pydantic`, `minio`, `httpx`, `PyMuPDF`, `python-docx`, `nltk`).
*   Download NLTK data if needed during build (`RUN python -m nltk.downloader punkt stopwords` etc.).
*   Expose the MCP port (`8045`).
*   Set environment variables for Minio configuration.
*   Define the `CMD` to run the MCP (e.g., `CMD ["python", "main.py"]`).

This plan provides a comprehensive starting point for developing the `DocumentQueryMCP`.