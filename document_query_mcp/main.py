# /home/<USER>/agentiveaiq/mcps/document_query_mcp/main.py
from fastmcp import FastMCP # Assumes fastmcp SDK is available in PYTHONPATH
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import os
import httpx # For fetching original document
from minio import Minio # For uploading temporary Markdown
from minio.error import S3Error
import re
import nltk # For stemming
from nltk.stem import PorterStemmer
# Ensure necessary NLTK data is available (e.g., punkt for tokenization)
try:
    nltk.data.find('tokenizers/punkt')
except nltk.downloader.Downloader:
    nltk.download('punkt', quiet=True)
try:
    nltk.data.find('corpora/stopwords')
except nltk.downloader.Downloader:
    nltk.download('stopwords', quiet=True)
try:
    nltk.data.find('tokenizers/punkt_tab')
except nltk.downloader.Downloader:
    nltk.download('punkt_tab', quiet=True)

# Document conversion libraries (add specific imports as you implement)
# For PDF:
try:
    import fitz # PyMuPDF
except ImportError:
    print("PyMuPDF (fitz) not installed. PDF conversion will not be available.")
    fitz = None
# For DOCX:
try:
    from docx import Document as DocxDocument
except ImportError:
    print("python-docx not installed. DOCX conversion will not be available.")
    DocxDocument = None

# --- Configuration ---
MINIO_ENDPOINT_URL = os.getenv("DQ_MINIO_ENDPOINT", "http://************:8000") # Full URL for fetching
MINIO_TEMP_UPLOAD_HOST = os.getenv("DQ_MINIO_TEMP_UPLOAD_HOST", "************:8000") # Host for Minio client
TEMP_MD_BUCKET_NAME = os.getenv("DQ_TEMP_MD_BUCKET_NAME", "doc-mcp")
TEMP_MD_ACCESS_KEY = os.getenv("DQ_TEMP_MD_ACCESS_KEY") # Removed fallback
TEMP_MD_SECRET_KEY = os.getenv("DQ_TEMP_MD_SECRET_KEY") # Removed fallback

# Initialize Minio client for temporary uploads
minio_client_temp_upload = None
if TEMP_MD_ACCESS_KEY and TEMP_MD_SECRET_KEY:
    try:
        minio_client_temp_upload = Minio(
            MINIO_TEMP_UPLOAD_HOST,
            access_key=TEMP_MD_ACCESS_KEY,
            secret_key=TEMP_MD_SECRET_KEY,
            secure=MINIO_TEMP_UPLOAD_HOST.startswith("https")
        )
        # Check if bucket exists, create if not (optional, depends on setup)
        # found = minio_client_temp_upload.bucket_exists(TEMP_MD_BUCKET_NAME)
        # if not found:
        #     minio_client_temp_upload.make_bucket(TEMP_MD_BUCKET_NAME)
        #     print(f"Bucket '{TEMP_MD_BUCKET_NAME}' created for temporary Markdown files.")
        # else:
        #     print(f"Bucket '{TEMP_MD_BUCKET_NAME}' already exists for temporary Markdown files.")
    except Exception as e:
        print(f"Error initializing Minio client for temp uploads: {e}")
        minio_client_temp_upload = None
else:
    print("Minio credentials for temporary uploads not fully configured. Upload of temp MD will fail.")


# --- Pydantic Models for Tool Arguments ---
class QueryFullDocumentArgs(BaseModel):
    object_path_in_knowledgebase: str = Field(
        ...,
        description="The object path of the original document within the 'knowledgebase-documents' Minio bucket (e.g., 'd67632d7-9c66-4574-873f-32f89da83d6b/file.pdf' or 'file_uploads/guid/another.docx'). This path is typically found in the RAG metadata's 'minio_object_path' field."
    )
    search_query: str = Field(
        ...,
        description="The specific query or phrase the agent wants to search for within the full document."
    )
    content_type: Optional[str] = Field(None, description="Explicit content type of the original document (e.g., 'pdf', 'docx', 'txt', 'md'). If None, MCP will infer from extension.")

# --- MCP Initialization ---
mcp = FastMCP(
    name="DocumentQueryMCP",
    instructions="""
# Document Query MCP - v1.0

## TOOL DESCRIPTION:
This MCP empowers AI agents to perform deep analysis of full source documents. When a standard RAG chunk lacks sufficient context, the agent can use this MCP to fetch the original document (PDF, DOCX, TXT, MD), convert it to Markdown (if needed), and perform an advanced keyword search within its entire content. The MCP returns relevant paragraphs, offering richer context than initial chunks. Temporary Markdown conversions are stored in the 'doc-mcp' Minio bucket (endpoint: http://************:8000) with a 3-day auto-deletion policy.

## QUICK START:
Query a full document when RAG snippets are insufficient. Provide the object path (from RAG metadata's 'minio_object_path') and your search terms.
EXAMPLE: {"object_path_in_knowledgebase": "d67632d7-9c66-4574-873f-32f89da83d6b/some_document.pdf", "search_query": "details about project X"}

## AVAILABLE TOOLS:
- query_full_document: Fetches, (converts if needed), and searches a full document from the 'knowledgebase-documents' bucket, returning relevant paragraphs.

## COMMON WORKFLOWS:
1. RAG Retrieval → Insufficient Context → Query Full Document: Agent receives RAG chunks, finds context lacking, uses this tool with original document ID and specific search terms to get more detailed paragraphs.

## LIMITATIONS:
- Relies on public accessibility of the 'knowledgebase-documents' Minio bucket. (URL: MINIO_ENDPOINT_URL/knowledgebase-documents/object_path_in_knowledgebase).
- Document conversion time can vary based on size and type. Supported types: PDF, DOCX, TXT, MD.
- Search effectiveness depends on the quality of the search query and document content.
- Temporary Markdown files in 'doc-mcp' are wiped after 3 days.

## TROUBLESHOOTING:
SYMPTOM: Document not found
CAUSE: Incorrect 'object_path_in_knowledgebase' OR document not publicly accessible at the constructed URL within 'knowledgebase-documents' bucket.
SOLUTION: Verify the 'object_path_in_knowledgebase' (should match RAG metadata's 'minio_object_path'). Ensure the 'knowledgebase-documents' Minio bucket and the specific object are publicly readable. The MCP attempts to fetch from MINIO_ENDPOINT_URL/knowledgebase-documents/object_path_in_knowledgebase.

SYMPTOM: Conversion failed
CAUSE: Unsupported file format (other than PDF, DOCX, TXT, MD), corrupted document, or issue with conversion libraries (e.g., PyMuPDF for PDF, python-docx for DOCX).
SOLUTION: Ensure document is a supported type and not corrupted. Check MCP logs for specific conversion errors. Ensure required libraries are installed.

SYMPTOM: No relevant snippets returned
CAUSE: Search query too broad/narrow, or the information is not present in the document.
SOLUTION: Refine the search query. Try broader terms or more specific phrases. Manually verify if the information exists in the document.
    """
)

# --- Helper Functions ---
def get_file_extension(filename: str) -> str:
    return os.path.splitext(filename)[1].lower().strip('.')

async def convert_pdf_to_markdown(pdf_content: bytes) -> str:
    if not fitz:
        raise ImportError("PyMuPDF (fitz) is not installed or failed to import. Cannot convert PDF.")
    markdown_text = ""
    try:
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            markdown_text += page.get_text("text") + "\n\n" # Use plain text extraction for robustness
        return markdown_text.strip()
    except Exception as e:
        print(f"PyMuPDF error details: {type(e).__name__} - {str(e)}")
        raise RuntimeError(f"PDF to Markdown conversion failed. Details: {type(e).__name__} - {str(e)}")

async def convert_docx_to_markdown(docx_content: bytes) -> str:
    if not DocxDocument:
        raise ImportError("python-docx is not installed or failed to import. Cannot convert DOCX.")
    try:
        import io
        doc = DocxDocument(io.BytesIO(docx_content))
        # Basic conversion: extract paragraphs. For more complex MD, consider pandoc or other libraries.
        markdown_text = "\n\n".join([para.text for para in doc.paragraphs])
        return markdown_text
    except Exception as e:
        raise RuntimeError(f"DOCX to Markdown conversion failed: {e}")

def stem_text(text: str, stemmer) -> List[str]:
    tokens = nltk.word_tokenize(text.lower())
    return [stemmer.stem(token) for token in tokens]

# --- Tool Definition ---
@mcp.tool()
async def query_full_document(args: QueryFullDocumentArgs) -> Dict[str, Any]:
    """
    ### query_full_document:
    TOOL USE: This tool is used to gain further information and more context on a topic a user is asking about, especially when the initial RAG chunk lacks sufficient details. It fetches the entire original document, identified by its complete MinIO object path (found in RAG chunk metadata), converts it to a searchable format if necessary, and then searches the full document content using a provided query. The tool returns relevant snippets (paragraphs) of information related to the search query, providing deeper insight and knowledge than the initial RAG chunk.

    INPUTS:
      - REQUIRED:
        - object_path_in_knowledgebase: String - The complete MinIO object path of the original document. This path is found in the RAG chunk metadata (typically the 'minio_object_path' field). Examples: 'file_uploads/d67632d7-9c66-4574-873f-32f89da83d6b/96bb9202-3426-45b4-a812-1a035a0695ff/testdocx.docx' or 'd67632d7-9c66-4574-873f-32f89da83d6b/bc5c241c-50a7-49db-91cd-5cbfd7ce72fa/meetaiq.com/blog/what-are-the-disadvantages-of-ai-in-the-economy.md'.
        - search_query: String - The text phrase or keywords the agent wants to find within the full document.
      
      - OPTIONAL:
        - content_type: String - Explicit content type (e.g., "pdf", "docx", "txt", "md"). If omitted, inferred from the object_path_in_knowledgebase's extension.

    DEFAULT VALUES:
      - (None applicable for required inputs)

    VARIABLE OPTIONS:
      - (None directly applicable to inputs, but search behavior includes stemming and case-insensitivity)

    PARAMETER RELATIONSHIPS:
      - The 'object_path_in_knowledgebase' must correctly point to a publicly accessible file within the 'knowledgebase-documents' Minio bucket.
      - The 'search_query' should be specific enough to find relevant information.

    OUTPUT: Returns a JSON object containing:
      - success: Boolean - True if the operation (including fetch, optional conversion, and search) was successful, False otherwise.
      - message: String - A status message (e.g., "Snippets retrieved successfully.", "Document not found.", "Conversion error.", "Search query yielded no results.").
      - retrieved_paragraphs: Array of Strings - A list of contextual snippets from the document. Each snippet string contains the matching paragraph and, where available, the paragraph immediately preceding and succeeding it, joined by double newlines. Empty if no matches.
      - original_document_url: String - The full public URL constructed and used to fetch the original document.
      - temporary_markdown_url: String (Nullable) - The URL to the temporary Markdown file created in the 'doc-mcp' Minio bucket. Present if conversion occurred and was successful. Null otherwise.
      - search_terms_found: Array of Strings (Nullable) - List of unique stemmed search terms that were actually found in the document.

    ERROR HANDLING:
      - Document Not Found: {"success": false, "message": "Error: Original document not found at [URL].", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": null}
      - Conversion Failure: {"success": false, "message": "Error: Failed to convert document. Details: [error_details]", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": null}
      - Minio Upload Failure (Temp MD): {"success": false, "message": "Error: Failed to upload temporary Markdown to Minio. Details: [error_details]", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": null}
      - Search Yielded No Results: {"success": true, "message": "Search completed. No matching paragraphs found for the query.", "retrieved_paragraphs": [], "original_document_url": "[URL]", "temporary_markdown_url": "[URL_if_conversion_happened]"}
      - General Processing Error: {"success": false, "message": "Error: An unexpected error occurred. Details: [error_details]", "retrieved_paragraphs": [], "original_document_url": null, "temporary_markdown_url": null}

    EXAMPLE USAGE:
    ```json
    {
      "object_path_in_knowledgebase": "d67632d7-9c66-4574-873f-32f89da83d6b/important_document.pdf",
      "search_query": "section about new policy"
    }
    ```

    RELATED TOOLS:
      - (This MCP is standalone but complements a RAG system.)

    USER HINTS:
      - IDENTIFYING NEED: Use this tool when the context provided in a RAG chunk is not enough to fully address the user's query. If you need more insight or knowledge on the requested topic and the RAG metadata contains a 'minio_object_path', this tool can search the entire original document for more comprehensive information.
      - GATHERING INFO: The agent needs the 'object_path_in_knowledgebase' (the complete MinIO object path from RAG metadata's 'minio_object_path' field) and a specific 'search_query' based on what information is missing or needs to be elaborated.
      - PRESENTING RESULTS: If successful, the agent receives full paragraphs (snippets). It should then synthesize this richer information into its response. If no results, inform the user the specific query didn't find matches in the full document.

    CONVERSATION EXAMPLES:
    User: "The RAG snippet about 'Project Alpha's budget' is too vague. Can you find more details in the main project proposal PDF? The metadata says its path is 'project_files/alpha_proposal.pdf'."
    Agent (Thinking): "The user needs more detail. The RAG metadata 'minio_object_path' is 'project_files/alpha_proposal.pdf'. I will use the query_full_document tool to search for 'budget allocation details' within this PDF."
    Agent (Tool Call): query_full_document(object_path_in_knowledgebase="project_files/alpha_proposal.pdf", search_query="budget allocation details")
    """
    KNOWLEDGEBASE_BUCKET_NAME = "knowledgebase-documents" # Fixed bucket name for source documents
    
    # Construct the full object path including the fixed bucket name for URL construction
    # The args.object_path_in_knowledgebase is the path *within* KNOWLEDGEBASE_BUCKET_NAME
    full_object_path_for_url = f"{KNOWLEDGEBASE_BUCKET_NAME}/{args.object_path_in_knowledgebase}"
    original_document_url = f"{MINIO_ENDPOINT_URL}/{full_object_path_for_url}"
    
    content_to_search: Optional[str] = None
    temporary_markdown_url: Optional[str] = None
    original_content_bytes: Optional[bytes] = None

    # 1. Fetch original document
    print(f"Attempting to fetch document from: {original_document_url}")
    try:
        async with httpx.AsyncClient(timeout=30.0) as client: # 30s timeout
            response = await client.get(original_document_url)
            response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
            original_content_bytes = response.content
            print(f"Successfully fetched {len(original_content_bytes)} bytes from {original_document_url}")
    except httpx.HTTPStatusError as e:
        print(f"HTTPStatusError: {e.response.status_code} while fetching {original_document_url}")
        return {"success": False, "message": f"Error: Original document not found or access denied at {original_document_url}. Status: {e.response.status_code}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
    except httpx.RequestError as e:
        print(f"RequestError: {e} while fetching {original_document_url}")
        return {"success": False, "message": f"Error: Network request failed for {original_document_url}. Details: {str(e)}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
    except Exception as e:
        print(f"Unexpected error during fetch: {e}")
        return {"success": False, "message": f"Error: An unexpected error occurred during document fetch. Details: {str(e)}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}

    if original_content_bytes is None: # Should not happen if raise_for_status is effective
        return {"success": False, "message": "Error: Failed to fetch document content (content is None).", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}

    # 2. Determine file type and convert if necessary
    file_ext = args.content_type if args.content_type else get_file_extension(args.object_path_in_knowledgebase)
    print(f"Determined file extension: {file_ext} from path {args.object_path_in_knowledgebase}")

    conversion_needed = False
    if file_ext == "pdf":
        if not fitz:
            return {"success": False, "message": "Error: PDF processing library (PyMuPDF/fitz) not available.", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
        try:
            print("Converting PDF to Markdown...")
            content_to_search = await convert_pdf_to_markdown(original_content_bytes)
            conversion_needed = True
            print("PDF to Markdown conversion successful.")
        except Exception as e:
            print(f"PDF conversion error: {e}")
            return {"success": False, "message": f"Error: PDF to Markdown conversion failed. Details: {str(e)}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
    elif file_ext == "docx":
        if not DocxDocument:
            return {"success": False, "message": "Error: DOCX processing library (python-docx) not available.", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
        try:
            print("Converting DOCX to Markdown...")
            content_to_search = await convert_docx_to_markdown(original_content_bytes)
            conversion_needed = True
            print("DOCX to Markdown conversion successful.")
        except Exception as e:
            print(f"DOCX conversion error: {e}")
            return {"success": False, "message": f"Error: DOCX to Markdown conversion failed. Details: {str(e)}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
    elif file_ext == "txt":
        print("Decoding TXT file...")
        try:
            content_to_search = original_content_bytes.decode('utf-8', errors='replace')
            print("TXT decoding successful.")
        except Exception as e:
            print(f"TXT decoding error: {e}")
            return {"success": False, "message": f"Error: Failed to decode TXT file. Details: {str(e)}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
    elif file_ext == "md":
        print("Decoding Markdown file...")
        try:
            content_to_search = original_content_bytes.decode('utf-8', errors='replace')
            print("Markdown decoding successful.")
        except Exception as e:
            print(f"Markdown decoding error: {e}")
            return {"success": False, "message": f"Error: Failed to decode Markdown file. Details: {str(e)}", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}
    else:
        print(f"Unsupported file type: {file_ext}")
        return {"success": False, "message": f"Error: Unsupported file type '{file_ext}'. Supported types: pdf, docx, txt, md.", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}

    if content_to_search is None:
         return {"success": False, "message": "Error: Content became None after conversion/decoding attempt.", "retrieved_paragraphs": [], "original_document_url": original_document_url, "temporary_markdown_url": None, "search_terms_found": None}

    # 3. Upload converted Markdown to Minio (if conversion happened)
    if conversion_needed and content_to_search:
        if minio_client_temp_upload:
            try:
                # Sanitize object_path_in_knowledgebase for use in object name, replace slashes
                sanitized_object_path = args.object_path_in_knowledgebase.replace('/', '_')
                temp_md_object_name = f"temp_converted/{sanitized_object_path}.md"
                
                md_bytes = content_to_search.encode('utf-8')
                import io
                
                print(f"Attempting to upload temporary Markdown to Minio: bucket='{TEMP_MD_BUCKET_NAME}', object='{temp_md_object_name}'")
                minio_client_temp_upload.put_object(
                    TEMP_MD_BUCKET_NAME,
                    temp_md_object_name,
                    io.BytesIO(md_bytes),
                    len(md_bytes),
                    content_type='text/markdown'
                )
                # Construct the URL for the temporary file based on how it would be publicly accessed
                temporary_markdown_url = f"{MINIO_ENDPOINT_URL}/{TEMP_MD_BUCKET_NAME}/{temp_md_object_name}"
                print(f"Successfully uploaded temporary Markdown. URL: {temporary_markdown_url}")
            except S3Error as e:
                print(f"Minio S3Error during temporary Markdown upload: {e}")
                # Non-fatal for search, but good to indicate upload issue
                temporary_markdown_url = f"Minio upload failed: {str(e)}" # Provide feedback in the response
            except Exception as e:
                print(f"Unexpected error during temporary Markdown upload: {e}")
                temporary_markdown_url = f"Unexpected Minio upload error: {str(e)}" # Provide feedback
        else:
            print(f"Warning: Conversion was needed for {args.object_path_in_knowledgebase} but Minio client for temp uploads is not available. Cannot upload.")
            temporary_markdown_url = "Upload skipped: Minio client not configured" # Provide feedback
    elif not conversion_needed:
        print(f"No conversion needed for {args.object_path_in_knowledgebase}, skipping Minio temp upload.")
        # temporary_markdown_url remains None

    # 4. Perform search
    # Current search logic is basic and will be reviewed/improved next.
    # print("Placeholder: Search logic to be implemented.") # Removed placeholder message
    retrieved_paragraphs = []
    search_terms_found_in_doc = set()
    stemmed_query_terms = [] # Initialize to handle empty search query case
    
    # --- Start of Search Logic ---
    if content_to_search: # Ensure content_to_search is not None
        stemmer = PorterStemmer()
        # Ensure search_query is not empty before stemming
        if not args.search_query.strip():
            print("Search query is empty. Skipping search.")
            # No search terms, so retrieved_paragraphs will remain empty.
        else:
            stemmed_query_terms = stem_text(args.search_query, stemmer)
            if not stemmed_query_terms:
                 print("Stemmed query terms are empty (e.g., only stopwords). Skipping search.")
            else:
                paragraphs = re.split(r'\n\s*\n+', content_to_search)
                print(f"Searching {len(paragraphs)} paragraphs for query: '{args.search_query}' (stemmed: {stemmed_query_terms})")

                for para_idx, para in enumerate(paragraphs):
                    if not para.strip():
                        continue
                    try:
                        stemmed_para_terms = stem_text(para, stemmer)
                        match = True
                        current_para_found_terms_in_para = [] # Renamed to avoid conflict
                        for query_term in stemmed_query_terms:
                            if query_term not in stemmed_para_terms:
                                match = False
                                break
                            else:
                                current_para_found_terms_in_para.append(query_term)
                        
                        if match:
                            contextual_snippet_parts = []
                            # Add previous paragraph if exists and is not just whitespace
                            if para_idx > 0 and paragraphs[para_idx - 1].strip():
                                contextual_snippet_parts.append(paragraphs[para_idx - 1].strip())
                            
                            # Add current matching paragraph (already stripped and checked)
                            contextual_snippet_parts.append(para.strip())
                            
                            # Add next paragraph if exists and is not just whitespace
                            if para_idx < len(paragraphs) - 1 and paragraphs[para_idx + 1].strip():
                                contextual_snippet_parts.append(paragraphs[para_idx + 1].strip())
                            
                            # Join the parts to form the full snippet with context
                            full_snippet = "\n\n".join(contextual_snippet_parts)
                            if full_snippet: # Ensure we don't add empty snippets if all parts were empty
                                retrieved_paragraphs.append(full_snippet)
                            search_terms_found_in_doc.update(current_para_found_terms_in_para)
                    except Exception as para_e:
                        print(f"Error processing paragraph {para_idx} for search: {para_e}")
                        # Decide if you want to skip this paragraph or halt
    else: # content_to_search is None
        print("Content to search is None. Skipping search logic.")
        return { # This case should ideally be caught earlier
            "success": False,
            "message": "Error: No content available to search (was None after processing attempts).",
            "retrieved_paragraphs": [],
            "original_document_url": original_document_url,
            "temporary_markdown_url": temporary_markdown_url,
            "search_terms_found": None
        }
    # --- End of Search Logic ---


    if not retrieved_paragraphs:
        # If search logic was actually run and found nothing, or if query was empty
        print(f"Search completed for '{args.search_query}'. No matching paragraphs found.")
        return {
            "success": True, # Search itself was successful, even if no results
            "message": f"Search completed for '{args.search_query}'. No matching paragraphs found.",
            "retrieved_paragraphs": [],
            "original_document_url": original_document_url,
            "temporary_markdown_url": temporary_markdown_url,
            "search_terms_found": list(stemmed_query_terms) # Show what was searched for, even if no hits
        }
            
    # If we have results
    print(f"Search found {len(retrieved_paragraphs)} matching paragraphs.")
    return {
        "success": True,
        "message": "Snippets retrieved successfully.",
        "retrieved_paragraphs": retrieved_paragraphs,
        "original_document_url": original_document_url,
        "temporary_markdown_url": temporary_markdown_url,
        "search_terms_found": list(search_terms_found_in_doc)
    }

# --- Main Execution Block ---
if __name__ == "__main__":
    print(f"Attempting to run DocumentQueryMCP on port 8045...")
    print(f"Minio Endpoint for originals: {MINIO_ENDPOINT_URL}")
    print(f"Minio Host for temp uploads: {MINIO_TEMP_UPLOAD_HOST}")
    print(f"Minio Temp Bucket for MD: {TEMP_MD_BUCKET_NAME}")
    if not minio_client_temp_upload:
        print("WARNING: Minio client for temporary uploads is not initialized. Uploads will fail.")
    
    mcp.run(transport="sse", host="0.0.0.0", port=8045, log_level="info")