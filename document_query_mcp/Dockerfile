# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# --- fastmcp SDK Handling ---
# Copy the fastmcp SDK source code from the host into the image.
# This assumes the Docker build context is /home/<USER>/agentiveaiq/mcps/document_query_mcp/
# and the fastmcp SDK is located at ../../fastmcp relative to this Dockerfile.
# For a more robust build, consider setting the build context to /home/<USER>/agentiveaiq/
# and then paths would be: COPY fastmcp /app/fastmcp_sdk and COPY mcps/document_query_mcp /app
# Updated to copy the local SDK copy
COPY fastmcp_sdk_local /app/fastmcp_sdk_local
# Install the local fastmcp SDK using pip. This assumes fastmcp_sdk_local has a setup.py or pyproject.toml.
RUN pip install --no-cache-dir /app/fastmcp_sdk_local

# --- Application Code & Dependencies ---
# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Download NLTK data
# It's good practice to download these during the build to avoid runtime downloads.
RUN python -m nltk.downloader punkt stopwords punkt_tab

# --- Environment Variables (placeholders, set these at runtime) ---
# Minio configuration for fetching original documents (public access assumed for endpoint)
ENV DQ_MINIO_ENDPOINT="http://************:8000"
# Minio configuration for uploading temporary Markdown files
ENV DQ_MINIO_TEMP_UPLOAD_HOST="************:8000"
ENV DQ_TEMP_MD_BUCKET_NAME="doc-mcp"
# Secrets will be injected at runtime via --env-file or -e flags
ENV DQ_TEMP_MD_ACCESS_KEY=""
ENV DQ_TEMP_MD_SECRET_KEY=""

# Make port 8045 available to the world outside this container
EXPOSE 8045

# Define environment variable for the Python script
ENV PYTHONUNBUFFERED=1

# Run main.py when the container launches
CMD ["python", "main.py"]