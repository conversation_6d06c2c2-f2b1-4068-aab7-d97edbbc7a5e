import NextAuth, { DefaultSession, DefaultUser } from "next-auth";
import { JWT, DefaultJWT } from "next-auth/jwt";
import { UserRole, SubscriptionTier } from "@prisma/client"; // Assuming these enums are exported or accessible

declare module "next-auth" {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string;
      role?: UserRole | null; // Or string, depending on what you set in JWT callback
      tier?: SubscriptionTier | null; // Or string
    } & DefaultSession["user"]; // Extends the default user properties (name, email, image)
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User extends DefaultUser {
    role?: UserRole | null;
    tier?: SubscriptionTier | null;
  }
}

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `auth`, when using JWT sessions */
  interface JWT extends DefaultJWT {
    id: string;
    role?: UserRole | null;
    tier?: SubscriptionTier | null;
  }
}