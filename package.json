{"name": "agentiveaiq-frontend-new", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "postinstall": "prisma generate"}, "dependencies": {"@assistant-ui/react": "^0.10.13", "@assistant-ui/react-langgraph": "^0.5.4", "@assistant-ui/react-markdown": "^0.10.4", "@auth/prisma-adapter": "^2.9.1", "@langchain/langgraph-sdk": "^0.0.78", "@polar-sh/checkout": "^0.1.11", "@polar-sh/nextjs": "^0.4.0", "@polar-sh/sdk": "^0.32.16", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@sendgrid/mail": "^8.1.5", "ai": "^4.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.414.0", "next": "15.3.2", "next-auth": "5.0.0-beta.28", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.4", "svix": "^1.66.0", "tailwind-merge": "^2.6.0", "uuid": "^11.1.0", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@prisma/client": "^5.22.0", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/node": "^20.17.50", "@types/pg": "^8.15.2", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.4.0", "postcss": "^8.5.4", "prettier": "^3.5.3", "prisma": "^5.22.0", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3"}}