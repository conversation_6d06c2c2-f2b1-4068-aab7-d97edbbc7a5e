# Shopify MCP Server - Environment Configuration Example
# Rename this file to .env and fill in your actual values

# SHOPIFY STORE URL - Required
SHOPIFY_SHOP_URL=your-store.myshopify.com

# OPTION A: API Key + Password Authentication
SHOPIFY_API_KEY=your_api_key_here
SHOPIFY_PASSWORD=your_private_app_password_here

# OPTION B: Access Token Authentication
# SHOPIFY_ACCESS_TOKEN=your_access_token_here

# API Version - Optional (defaults to 2023-01)
SHOPIFY_API_VERSION=2023-10

# SECURITY NOTE: Keep this file private and set chmod 600 .env
