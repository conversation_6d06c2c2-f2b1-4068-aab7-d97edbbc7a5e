version: '3.8'
services:
  postgres-saas-db:
    image: postgres:16
    container_name: saas-frontend-db
    environment:
      POSTGRES_USER: saas_user
      POSTGRES_PASSWORD: saas_password # IMPORTANT: Change this for production
      POSTGRES_DB: saas_db
    ports:
      - "5436:5432"
    volumes:
      - saas_frontend_db_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  saas_frontend_db_data: