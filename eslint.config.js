// eslint.config.js
import nextPlugin from 'eslint-config-next';
import prettierPlugin from 'eslint-plugin-prettier/recommended';

/** @type {import('eslint').Linter.FlatConfig[]} */
const config = [
  nextPlugin, // Base Next.js rules
  prettierPlugin, // Integrates Prettier, disables conflicting rules, and adds Prettier rules
  {
    rules: {
      // You can add any custom rule overrides here if needed
      // e.g., "prettier/prettier": "warn" to show Prettier issues as warnings
    }
  }
];

export default config;