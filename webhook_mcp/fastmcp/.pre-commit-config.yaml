fail_fast: true

repos:
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.23
    hooks:
      - id: validate-pyproject

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, json5]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.4
    hooks:
      # Run the linter.
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      # Run the formatter.
      - id: ruff-format

  - repo: https://github.com/northisup/pyright-pretty
    rev: v0.1.0
    hooks:
      - id: pyright-pretty
        files: ^src/|^tests/
