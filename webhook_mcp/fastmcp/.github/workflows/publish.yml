name: Publish FastMCP to PyPI
on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  pypi-publish:
    name: Upload to PyPI
    runs-on: ubuntu-latest
    permissions:
      id-token: write # For PyPI's trusted publishing
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: "Install uv"
        uses: astral-sh/setup-uv@v3

      - name: Build
        run: uv build

      - name: Publish to PyPi
        run: uv publish -v dist/*
