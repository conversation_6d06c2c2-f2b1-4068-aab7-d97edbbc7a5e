name: Run tests

env:
  # enable colored output
  PY_COLORS: 1

on:
  push:
    branches: ["main"]
    paths:
      - "src/**"
      - "tests/**"
      - "uv.lock"
      - "pyproject.toml"
      - ".github/workflows/**"

  # run on all pull requests because these checks are required and will block merges otherwise
  pull_request:

  workflow_dispatch:

permissions:
  contents: read

jobs:
  run_tests:
    name: "Run tests: Python ${{ matrix.python-version }} on ${{ matrix.os }}"
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        python-version: ["3.10"]
      fail-fast: false
    timeout-minutes: 5

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ matrix.python-version }}

      - name: Install FastMCP
        run: uv sync --locked

      - name: Run tests
        run: uv run pytest tests
