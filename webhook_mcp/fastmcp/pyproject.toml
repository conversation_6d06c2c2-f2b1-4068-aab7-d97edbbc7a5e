[project]
name = "fastmcp"
dynamic = ["version"]
description = "The fast, Pythonic way to build MCP servers."
authors = [{ name = "<PERSON>" }]
dependencies = [
    "python-dotenv>=1.1.0",
    "exceptiongroup>=1.2.2",
    "httpx>=0.28.1",
    "mcp>=1.9.2,<2.0.0",
    "openapi-pydantic>=0.5.1",
    "rich>=13.9.4",
    "typer>=0.15.2",
    "websockets>=14.0",
    "authlib>=1.5.2",
]
requires-python = ">=3.10"
readme = "README.md"
license = "Apache-2.0"

keywords = [
    "mcp",
    "mcp server",
    "mcp client",
    "model context protocol",
    "fastmcp",
    "llm",
    "agent",
]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Typing :: Typed",
]

[dependency-groups]
dev = [
    "copychat>=0.5.2",
    "dirty-equals>=0.9.0",
    "fastapi>=0.115.12",
    "ipython>=8.12.3",
    "pdbpp>=0.10.3",
    "pre-commit",
    "pyinstrument>=5.0.2",
    "pyright>=1.1.389",
    "pytest>=8.3.3",
    "pytest-asyncio>=0.23.5",
    "pytest-cov>=6.1.1",
    "pytest-env>=1.1.5",
    "pytest-flakefinder",
    "pytest-httpx>=0.35.0",
    "pytest-report>=0.2.1",
    "pytest-timeout>=2.4.0",
    "pytest-xdist>=3.6.1",
    "ruff",
]

[project.scripts]
fastmcp = "fastmcp.cli:app"

[project.urls]
Homepage = "https://gofastmcp.com"
Repository = "https://github.com/jlowin/fastmcp"
Documentation = "https://gofastmcp.com"


[build-system]
requires = ["hatchling", "uv-dynamic-versioning>=0.7.0"]
build-backend = "hatchling.build"

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.uv-dynamic-versioning]
vcs = "git"
style = "pep440"
bump = true
fallback-version = "0.0.0"


[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "session"
asyncio_default_test_loop_scope = "session"
filterwarnings = []
timeout = 3
env = [
    "FASTMCP_TEST_MODE=1",
    'D:FASTMCP_LOG_LEVEL=DEBUG',
    'D:FASTMCP_ENABLE_RICH_TRACEBACKS=0',
]

[tool.pyright]
include = ["src", "tests"]
exclude = ["**/node_modules", "**/__pycache__", ".venv", ".git", "dist"]
pythonVersion = "3.10"
pythonPlatform = "Darwin"
typeCheckingMode = "basic"
reportMissingImports = true
reportMissingTypeStubs = false
useLibraryCodeForTypes = true
venvPath = "."
venv = ".venv"
strict = ["src/fastmcp/server/server.py"]

[tool.ruff.lint]
extend-select = ["I", "UP"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "I001", "RUF013"]
