from fastmcp import FastMCP
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, EmailStr
import requests

# Pydantic model for tool arguments
class SendWebhookLeadArgs(BaseModel):
    webhook_url: str = Field(..., description="The URL to send the webhook to.")
    email: EmailStr = Field(..., description="Mandatory email of the lead.")
    name: Optional[str] = Field(None, description="Name of the lead (optional).")
    phone: Optional[str] = Field(None, description="Phone number of the lead (optional).")
    summary: str = Field(..., description="Mandatory summary of what the lead wanted.")

# Initialize the MCP with enhanced description
mcp = FastMCP(
    name="Webhook_Lead_MCP",
    instructions="""
# Webhook Lead MCP - v1.0

## TOOL DESCRIPTION: 
A service for sending lead information via a webhook to a specified URL. This MCP allows agents to capture and forward lead details, including email, name, phone, and a summary of their request, to an external system.

## QUICK START:
Send lead information to a webhook URL.
EXAMPLE: {"webhook_url": "https://example.com/webhook", "email": "<EMAIL>", "summary": "Lead interested in product demo."}

## AVAILABLE TOOLS:
- send_webhook_lead: Sends lead information to a specified webhook URL.

## COMMON WORKFLOWS:
1. Lead Capture → Webhook Send: Gather lead details → Send to CRM via webhook.

## LIMITATIONS:
- Requires a valid webhook URL.
- Email and summary are mandatory fields.

## TROUBLESHOOTING:
SYMPTOM: Webhook not received by the external system.
CAUSE: Incorrect webhook URL or network issues.
SOLUTION: Verify the webhook URL and check network connectivity.

SYMPTOM: Webhook data is incomplete or malformed.
CAUSE: Missing mandatory fields or incorrect data types.
SOLUTION: Ensure all required fields (email, summary) are provided and data types match the schema.
    """
)

@mcp.tool()
async def send_webhook_lead(args: SendWebhookLeadArgs) -> Dict[str, Any]:
    """
    ### send_webhook_lead:
    TOOL USE: Sends lead information (email, name, phone, summary) to a specified webhook URL. This tool is used to integrate lead capture with external systems like CRMs or marketing automation platforms.

    INPUTS:
      - REQUIRED:
        - webhook_url: String - The URL to send the webhook to.
        - email: String (email format) - Mandatory email of the lead.
        - summary: String - Mandatory summary of what the lead wanted.
      
      - OPTIONAL:
        - name: String - Name of the lead.
        - phone: String - Phone number of the lead.

    DEFAULT VALUES:
      - None for optional parameters if not provided.

    VARIABLE OPTIONS:
      - None

    PARAMETER RELATIONSHIPS:
      - `email` and `summary` must always be provided.
      - `webhook_url` must be a valid and accessible URL.

    OUTPUT: Returns a JSON object containing:
      - success: Boolean indicating if the webhook was sent successfully.
      - message: String - A descriptive message about the outcome.
      - status_code: Integer - The HTTP status code of the webhook request (if successful).
      - response_text: String - The text response from the webhook endpoint (if successful).

    ERROR HANDLING:
      - If `webhook_url` is invalid or unreachable, returns {"success": false, "message": "Failed to connect to webhook URL", "error": "..."}
      - If the webhook request fails (e.g., non-2xx status code), returns {"success": false, "message": "Webhook request failed", "status_code": ..., "response_text": "..."}
      - If mandatory fields are missing, this will be caught by Pydantic validation before the tool is called.

    EXAMPLE USAGE:
    ```json
    {
      "webhook_url": "https://api.example.com/leads",
      "email": "<EMAIL>",
      "name": "John Doe",
      "phone": "************",
      "summary": "Interested in enterprise solution, requested a callback."
    }
    ```

    RELATED TOOLS:
      - None directly within this MCP, but can be used in conjunction with tools that gather lead information.

    USER HINTS:
      - IDENTIFYING NEED: Use when the user wants to send collected lead data to an external system.
      - GATHERING INFO: Ensure you have the `webhook_url`, `email`, and `summary`. Ask for `name` and `phone` if available.
      - PRESENTING RESULTS: Inform the user if the webhook was sent successfully and provide any relevant response from the webhook endpoint.

    CONVERSATION EXAMPLES:
    User: "I have a new lead: <NAME_EMAIL>, they want to know about pricing."
    Agent: "Great! What is the webhook URL where I should send this lead information? Do you have their name or phone number?"
    """
    try:
        payload = args.dict(exclude_unset=True)
        webhook_url = payload.pop("webhook_url")
        
        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
        
        return {
            "success": True,
            "message": "Webhook sent successfully.",
            "status_code": response.status_code,
            "response_text": response.text,
        }
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "message": f"Failed to connect to webhook URL or request failed: {e}",
            "error": str(e),
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"An unexpected error occurred: {e}",
            "error": str(e),
        }

# Run the MCP server
if __name__ == "__main__":
    mcp.run(transport="sse", host="0.0.0.0", port=6010, log_level="info")