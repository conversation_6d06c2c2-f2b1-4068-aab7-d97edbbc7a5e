/*
  Warnings:

  - You are about to drop the column `agentGoal` on the `chatbot_instances` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "PromptSnippetType" AS ENUM ('BASE_SYSTEM_PROMPT', 'GOAL_INSTRUCTION', 'TONE_MODIFIER', 'INDUSTRY_CONTEXT', 'AUDIENCE_ADJUSTMENT');

-- AlterTable
ALTER TABLE "chatbot_instances" DROP COLUMN "agentGoal",
ADD COLUMN     "industry" TEXT,
ADD COLUMN     "primaryAgentGoals" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "secondaryAgentGoals" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "targetAudience" TEXT,
ADD COLUMN     "tonePreference" TEXT;

-- CreateTable
CREATE TABLE "prompt_snippets" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" "PromptSnippetType" NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "prompt_snippets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "prompt_snippets_name_key" ON "prompt_snippets"("name");
