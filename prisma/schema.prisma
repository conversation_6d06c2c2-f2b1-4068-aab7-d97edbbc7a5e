generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String   @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @default(now()) @map("updated_at")
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("sessions")
}

model User {
  id                              String            @id @default(cuid())
  name                            String?
  email                           String?           @unique
  password                        String?
  emailVerified                   DateTime?
  image                           String?
  createdAt                       DateTime          @default(now()) @map("created_at")
  updatedAt                       DateTime          @default(now()) @map("updated_at")
  role                            UserRole          @default(USER)
  tier                            SubscriptionTier  @default(FREE)
  paymentProviderCustomerId       String?           @unique @map("payment_provider_customer_id")
  paymentProviderSubscriptionId   String?           @unique @map("payment_provider_subscription_id")
  paymentProviderPriceId          String?           @map("payment_provider_price_id")
  paymentProviderCurrentPeriodEnd DateTime?         @map("payment_provider_current_period_end")
  accounts                        Account[]
  chatbotInstances                ChatbotInstance[]
  sessions                        Session[]

  @@map("users")
}

model ChatbotInstance {
  id                    String        @id @default(cuid())
  userId                String
  name                  String
  status                ChatbotStatus @default(DRAFT)
  type                  ChatbotType   @default(FLOATING_WIDGET)
  centralConfigId       String        @unique
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt
  businessContext       String?
  customGoalDetail      String?
  uiSettings            Json?
  industry              String?
  primaryAgentGoals     String[]      @default([])
  secondaryAgentGoals   String[]      @default([])
  targetAudience        String?
  tonePreference        String?
  agentPersonaName      String?
  companyName           String?
  deliveryMethod        String?
  leadSummaryPreference String?
  processLeads          Boolean       @default(false)
  recipientEmail        String?
  webhookUrl            String?
  user                  User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("chatbot_instances")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model PromptSnippet {
  id        String            @id @default(cuid())
  name      String            @unique
  content   String
  type      PromptSnippetType
  isActive  Boolean           @default(true)
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  @@map("prompt_snippets")
}

model BlogCategory {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  posts     BlogPost[]
  
  @@map(name: "blog_categories")
}

model BlogPost {
  id              String       @id @default(cuid())
  title           String
  slug            String       @unique
  excerpt         String?
  content         String?
  status          String       @default("DRAFT") // DRAFT or PUBLISHED
  categoryId      String
  category        BlogCategory @relation(fields: [categoryId], references: [id])
  metaTitle       String?
  metaDescription String?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  
  @@map(name: "blog_posts")
}

enum UserRole {
  ADMIN
  USER
}

enum SubscriptionTier {
  FREE
  BASE
  PRO
}

enum ChatbotStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum ChatbotType {
  FLOATING_WIDGET
  EMBEDDED_WINDOW
}

enum PromptSnippetType {
  BASE_SYSTEM_PROMPT
  GOAL_INSTRUCTION
  TONE_MODIFIER
  INDUSTRY_CONTEXT
  AUDIENCE_ADJUSTMENT
}
