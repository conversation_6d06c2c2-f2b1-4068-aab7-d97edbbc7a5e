# MCP Enhanced Description Implementation Guide

This guide explains how to implement the enhanced MCP descriptions in your actual MCP servers to make them more agent-friendly.

## Overview

The enhanced MCP descriptions follow a structured format that provides comprehensive information about each tool while maintaining clarity and conciseness. These descriptions are designed to be embedded directly in the MCP server code, making them immediately available to AI agents without requiring external documentation.

## Implementation Steps

### 1. Update the Main MCP Description

In each MCP server's main file (typically `main.py`), locate the FastMCP initialization and update the `instructions` parameter:

```python
mcp = FastMCP(
    name="Your_MCP_Server_Name",
    instructions="""
# Your MCP Name - v1.0

## TOOL DESCRIPTION: 
[Detailed explanation of what this MCP does, its primary purpose, and core capabilities]

## QUICK START:
[1-2 sentence summary of the most common use case]
EXAMPLE: [Simple example of the most common usage]

## AVAILABLE TOOLS:
- TOOL 1: [Brief summary of Tool 1]
- TOOL 2: [Brief summary of Tool 2]
- [etc.]

## COMMON WORKFLOWS:
1. [Workflow 1]: [Step 1] → [Step 2] → [Step 3]
2. [Workflow 2]: [Step 1] → [Step 2]

## LIMITATIONS:
- [Limitation 1]
- [Limitation 2]
- [etc.]

## TROUBLESHOOTING:
SYMPTOM: [Common issue 1]
CAUSE: [Likely cause]
SOLUTION: [How to fix it]

SYMPTOM: [Common issue 2]
CAUSE: [Likely cause]
SOLUTION: [How to fix it]
    """
)
```

### 2. Update Individual Tool Descriptions

For each tool in your MCP, update the docstring to include the enhanced description:

```python
@mcp.tool()
async def your_tool_name(args: YourToolArgs) -> Dict[str, Any]:
    """
    ### your_tool_name:
    TOOL USE: [Detailed description of tool functionality]

    INPUTS:
      - REQUIRED:
        - param1: Type - Description
        - param2: Type - Description
      
      - OPTIONAL:
        - param3: Type - Description
        - param4: Type - Description

    DEFAULT VALUES:
      - param3: default_value
      - param4: default_value

    VARIABLE OPTIONS:
      - param1: "option1", "option2", "option3"
      - param3: "optionA", "optionB", "optionC"

    PARAMETER RELATIONSHIPS:
      - [Relationship 1]
      - [Relationship 2]

    OUTPUT: Returns a JSON object containing:
      - success: Boolean indicating request success
      - [other output fields with descriptions]

    ERROR HANDLING:
      - [Error condition 1]: [Error response]
      - [Error condition 2]: [Error response]

    EXAMPLE USAGE:
    ```json
    {
      "param1": "value1",
      "param2": "value2",
      "param3": "value3"
    }
    ```

    RELATED TOOLS:
      - [related_tool_1]: [How it relates]
      - [related_tool_2]: [How it relates]

    USER HINTS:
      - IDENTIFYING NEED: [When to use this tool]
      - GATHERING INFO: [What to ask users]
      - PRESENTING RESULTS: [How to explain the output]

    CONVERSATION EXAMPLES:
    User: "[Example user request]"
    Agent: "[Example agent response]"
    """
    # Tool implementation...
```

### 3. Example Implementation for Short Video Maker MCP

Here's how you would implement the enhanced description for the Short Video Maker MCP:

```python
# In main.py or equivalent
from fastmcp import FastMCP
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

# Pydantic models for tool arguments
class Scene(BaseModel):
    text: str = Field(..., description="The text to be narrated and captioned")
    searchTerms: Optional[List[str]] = Field(None, description="Keywords for background video search")

class Config(BaseModel):
    paddingBack: Optional[int] = Field(1500, description="End screen duration in milliseconds")
    music: Optional[str] = Field(None, description="Background music mood")
    captionPosition: Optional[str] = Field("center", description="Position of captions (top, center, bottom)")
    captionBackgroundColor: Optional[str] = Field("green", description="Background color of captions")
    voice: Optional[str] = Field("af_nova", description="Kokoro TTS voice to use")
    orientation: Optional[str] = Field("portrait", description="Video orientation (portrait, landscape)")

class CreateShortVideoArgs(BaseModel):
    scenes: List[Scene] = Field(..., description="List of scenes with text and optional search terms")
    config: Optional[Config] = Field(None, description="Configuration options for the video")

class VideoStatusArgs(BaseModel):
    videoId: str = Field(..., description="The ID of the video to check")

# Initialize the MCP with enhanced description
mcp = FastMCP(
    name="Short_Video_Maker",
    instructions="""
# Short Video Maker MCP - v1.0

## TOOL DESCRIPTION: 
A comprehensive service for creating short-form videos suitable for TikTok, Instagram Reels, and YouTube Shorts. This MCP combines text-to-speech narration, background videos from Pexels, captions, and music to create engaging short videos from simple text input.

## QUICK START:
Create a short video by providing text for narration and optional search terms for background footage.
EXAMPLE: {"scenes": [{"text": "Hello world!", "searchTerms": ["nature"]}], "config": {"voice": "af_nova"}}

## AVAILABLE TOOLS:
- create-short-video: Creates a multi-scene video with narration, background footage, and captions
- get-video-status: Checks the processing status of a previously submitted video

## COMMON WORKFLOWS:
1. Pexels Search → Short Video Creation: Find specific footage → Use in short video creation
2. Voice Selection → Short Video Creation: Choose voice → Create narrated video
3. Short Video Creation → Video Crop: Create video → Crop to specific platform dimensions

## LIMITATIONS:
- Maximum 10 scenes per video
- Maximum 100 words per scene
- Processing time increases with video complexity
- Total video length typically limited to 3 minutes
- Stored in MinIO bucket: video-creator-standalone

## TROUBLESHOOTING:
SYMPTOM: Video creation takes a long time
CAUSE: Complex scenes or many search terms
SOLUTION: Reduce number of scenes or simplify search terms

SYMPTOM: Background videos don't match expectations
CAUSE: Search terms too vague or ambiguous
SOLUTION: Use more specific search terms (2-3 per scene)
    """
)

# Define tools with enhanced descriptions
@mcp.tool()
async def create_short_video(args: CreateShortVideoArgs) -> Dict[str, Any]:
    """
    ### create-short-video:
    TOOL USE: Creates a short video from text input, automatically generating narration using text-to-speech, finding relevant background footage, adding captions, and including background music. Each video can contain multiple scenes with different text and visuals.

    INPUTS:
      - REQUIRED:
        - scenes: Array - List of scene objects, each containing:
          - text: String - The text to be narrated and captioned for this scene
      
      - OPTIONAL:
        - scenes[].searchTerms: Array of Strings - Keywords for background video search (2-3 terms recommended)
        - config: Object - Configuration options:
          - paddingBack: Integer - End screen duration in milliseconds (default: 1500)
          - music: String - Background music mood (e.g., "chill", "upbeat", "dramatic")
          - captionPosition: String - Position of captions ("top", "center", "bottom")
          - captionBackgroundColor: String - Background color of captions (CSS color)
          - voice: String - Kokoro TTS voice to use
          - orientation: String - Video orientation ("portrait", "landscape")

    DEFAULT VALUES:
      - paddingBack: 1500 (milliseconds)
      - captionPosition: "center"
      - captionBackgroundColor: "green"
      - voice: "af_nova"
      - orientation: "portrait"
      - music: random selection if not specified

    VARIABLE OPTIONS:
      - captionPosition: "top", "center", "bottom"
      - orientation: "portrait", "landscape"
      - voice: Use get_voices from Kokoro TTS MCP to see all available options
      - music: "chill", "upbeat", "dramatic", "sad", "happy", "relaxing", "energetic", "inspirational"

    PARAMETER RELATIONSHIPS:
      - Each scene should have 10-15 words of text for optimal pacing
      - 2-3 search terms per scene works best for relevant footage
      - For professional content, "center" caption position is recommended
      - For social media, "portrait" orientation is typically preferred
      - Voice should match the tone of the content (formal, casual, energetic)

    OUTPUT: Returns a JSON object containing:
      - success: Boolean indicating request success
      - videoId: String - Unique identifier for the created video
      - status: String - Current processing status ("queued", "processing", "completed", "failed")
      - message: String - Additional information about the video status
      - estimatedTime: Integer - Estimated processing time in seconds

    ERROR HANDLING:
      - If scenes are missing, returns {"success": false, "error": "No scenes provided"}
      - If text is missing, returns {"success": false, "error": "Scene text is required"}
      - If processing fails, returns {"success": false, "error": "Processing failed", "details": "..."}

    EXAMPLE USAGE:
    ```json
    {
      "scenes": [
        {
          "text": "Hello world! This is a test video created with the Short Video Maker MCP.",
          "searchTerms": ["nature", "mountains"]
        },
        {
          "text": "You can create multiple scenes with different background videos.",
          "searchTerms": ["ocean", "waves"]
        }
      ],
      "config": {
        "paddingBack": 1500,
        "music": "chill",
        "captionPosition": "center",
        "captionBackgroundColor": "green",
        "voice": "af_nova",
        "orientation": "portrait"
      }
    }
    ```

    RELATED TOOLS:
      - get-video-status: To check the processing status of the created video
      - get_voices (Kokoro TTS MCP): To see available voice options
      - search_videos (Pexels MCP): To find specific videos for searchTerms

    USER HINTS:
      - IDENTIFYING NEED: Use when users want to create short videos for social media or presentations
      - GATHERING INFO: Ask about the story they want to tell, helping them break it into 3-5 scenes
      - PRESENTING RESULTS: Explain that processing takes time, provide the videoId for status checks

    CONVERSATION EXAMPLES:
    User: "I want to create a short video about climate change"
    Agent: "I can help you create a short video about climate change. Let's break this down into 3-4 scenes, each with a key point about climate change. For each scene, I'll need:
    1. The text you want narrated (10-15 words works best per scene)
    2. Any specific visuals you want (or I can suggest search terms)

    Would you like to use a male or female voice for narration? And do you prefer portrait format (for mobile) or landscape (for desktop)?"
    """
    # Tool implementation...

@mcp.tool()
async def get_video_status(args: VideoStatusArgs) -> Dict[str, Any]:
    """
    ### get-video-status:
    TOOL USE: Checks the current processing status of a video that was previously submitted for creation. Use this to monitor progress and get the final video URL when processing is complete.

    INPUTS:
      - REQUIRED:
        - videoId: String - The ID of the video to check (obtained from create-short-video response)

    OUTPUT: Returns a JSON object containing:
      - success: Boolean indicating request success
      - videoId: String - The ID of the video
      - status: String - Current processing status ("queued", "processing", "completed", "failed")
      - progress: Number - Processing progress percentage (0-100)
      - url: String - URL to the completed video (only present if status is "completed")
      - error: String - Error message (only present if status is "failed")
      - createdAt: String - Timestamp when the video was created
      - updatedAt: String - Timestamp when the status was last updated

    ERROR HANDLING:
      - If videoId is invalid, returns {"success": false, "error": "Invalid video ID"}
      - If video not found, returns {"success": false, "error": "Video not found"}

    EXAMPLE USAGE:
    ```json
    {
      "videoId": "cma9sjly700020jo25vwzfnv9"
    }
    ```

    RELATED TOOLS:
      - create-short-video: To create the video in the first place

    USER HINTS:
      - IDENTIFYING NEED: Use when users ask about the status of their video or when it will be ready
      - GATHERING INFO: You need the videoId from the create-short-video response
      - PRESENTING RESULTS: Explain the current status and estimated completion time

    CONVERSATION EXAMPLES:
    User: "Is my climate change video ready yet?"
    Agent: "Let me check the status of your climate change video. I'll need the video ID that was provided when we created the video. Let me look that up from our conversation history."
    """
    # Tool implementation...

# Run the MCP server
if __name__ == "__main__":
    mcp.run(transport="sse", host="0.0.0.0", port=8040, log_level="info")
```

## Best Practices

1. **Be Comprehensive but Concise**: Include all necessary information but avoid unnecessary verbosity.

2. **Use Consistent Formatting**: Maintain the same structure across all MCPs for consistency.

3. **Update All Tools**: Ensure every tool in your MCP has the enhanced description.

4. **Test with Agents**: After implementation, test with AI agents to ensure they can effectively use the tools.

5. **Keep Examples Current**: Update examples as features change or new use cases emerge.

6. **Maintain Parameter Accuracy**: Ensure all parameter descriptions, defaults, and options are accurate.

7. **Update Related Tools**: When adding new tools, update the "RELATED TOOLS" sections of existing tools.

## Implementation Checklist

For each MCP:

- [ ] Update main MCP description
- [ ] Update each tool's docstring
- [ ] Verify parameter descriptions match actual implementation
- [ ] Ensure default values are correctly documented
- [ ] Check that all error conditions are documented
- [ ] Verify examples are valid and current
- [ ] Test with AI agents

## Next Steps

After implementing these enhanced descriptions, consider:

1. Creating a standardized testing process for agent interactions
2. Developing a monitoring system to track which tools are most used and any common errors
3. Establishing a regular review cycle to keep descriptions updated as tools evolve
4. Collecting feedback from agents and users to further refine the descriptions

By following this implementation guide, you'll significantly improve the ability of AI agents to understand and effectively use your MCP tools, resulting in better user experiences and more successful outcomes.
