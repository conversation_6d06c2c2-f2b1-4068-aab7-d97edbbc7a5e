import type { NextAuthConfig } from "next-auth";

export default {
  providers: [], // Required by NextAuthConfig, actual providers in auth.ts
  pages: {
    signIn: '/sign-in',
    // error: '/auth/error', // You can add an error page if you have one
  },
  session: { strategy: "jwt" }, // JWT strategy is edge-compatible
  // trustHost: true, // Consider adding this if your app is behind a proxy
  // debug: process.env.NODE_ENV === "development", // For verbose logging in development
} satisfies NextAuthConfig;